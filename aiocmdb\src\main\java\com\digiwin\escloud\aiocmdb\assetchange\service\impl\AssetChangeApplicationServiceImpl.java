package com.digiwin.escloud.aiocmdb.assetchange.service.impl;

import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationAssetListMapper;
import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationExecutionPlanMapper;
import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationMapper;
import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationOperationRecordMapper;
import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeExecutionPlanExecutionRecordMapper;
import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeExecutionPlanShutdownStatusMapper;
import com.digiwin.escloud.aiocmdb.assetchange.model.*;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationDetailResponse;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationPageRequest;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationPageResponse;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationSaveRequest;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationSaveResponse;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeOperationRequest;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationProgress;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.OperationType;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.PlanStatus;
import com.digiwin.escloud.aiocmdb.assetchange.service.AssetChangeExecutePlanService;
import com.digiwin.escloud.aiocmdb.assetchange.service.IAssetChangeApplicationService;
import com.digiwin.escloud.aiocmdb.assetchange.service.StateFactory;
import com.digiwin.escloud.aiocmdb.assetchange.event.AssetChangeEmailEvent;
import com.digiwin.escloud.aiocmdb.assetchange.event.AssetChangeReportEvent;
import com.digiwin.escloud.aiouser.model.user.UserTenantInfoResponse;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;

/**
 * <p>
 * 资产变更申请单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Slf4j
@Service
public class AssetChangeApplicationServiceImpl implements IAssetChangeApplicationService {

    @Autowired
    private AssetChangeApplicationMapper assetChangeApplicationMapper;

    @Autowired
    private AssetChangeApplicationAssetListMapper assetChangeApplicationAssetListMapper;

    @Autowired
    private AssetChangeApplicationExecutionPlanMapper assetChangeApplicationExecutionPlanMapper;

    @Autowired
    private AssetChangeExecutionPlanShutdownStatusMapper assetChangeExecutionPlanShutdownStatusMapper;

    @Autowired
    private AssetChangeApplicationOperationRecordMapper assetChangeApplicationOperationRecordMapper;

    @Autowired
    private AssetChangeExecutionPlanExecutionRecordMapper assetChangeExecutionPlanExecutionRecordMapper;

    @Autowired
    private StateFactory stateFactory;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private AssetChangeExecutePlanService assetChangeExecutePlanService;
    @Autowired
    private AioUserFeignClient aioUserFeignClient;

    @Override
    public ResponseBase saveAssetChangeApplication(AssetChangeApplicationSaveRequest request) {
        // 创建事务定义
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        transactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        transactionDefinition.setIsolationLevel(TransactionDefinition.ISOLATION_DEFAULT);
        transactionDefinition.setTimeout(30); // 设置事务超时时间为30秒
        
        // 开始事务
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        
        try {
            // 参数校验
            ResponseBase validationResult = validateRequest(request);
            if (!validationResult.checkIsSuccess()) {
                transactionManager.rollback(transactionStatus);
                return validationResult;
            }

            AssetChangeApplication application = request.getApplication();
            boolean isUpdate = LongUtil.isNotEmpty(application.getId());

            // 处理申请单编号
            if (!isUpdate && StringUtils.isEmpty(application.getApplicationNumber())) {
                application.setApplicationNumber(generateApplicationNumber(application.getEid()));
            }

            // 检查申请单编号唯一性
            if (checkApplicationNumberExists(application.getApplicationNumber(), application.getId())) {
                transactionManager.rollback(transactionStatus);
                return ResponseBase.error(ResponseCode.ASSET_CHANGE_NUMBER_UNIQUE_CONSTRAINT_VIOLATION.getCode(), "申请单编号已存在");
            }

            // 保存主表
            saveMainApplication(application, isUpdate);

            // 保存子表数据
            saveSubTables(request, application.getId());

            // 处理保存后的操作提交
            ResponseBase operationResult = handleOperationSubmission(request, application);
            if (!operationResult.checkIsSuccess()) {
                transactionManager.rollback(transactionStatus);
                return operationResult;
            }

            // 提交事务
            transactionManager.commit(transactionStatus);
            
            log.info("资产变更申请单保存成功，申请单ID: {}, 申请单编号: {}", application.getId(), application.getApplicationNumber());

            AssetChangeApplicationSaveResponse response = new AssetChangeApplicationSaveResponse(
                    application.getId(),
                    application.getApplicationNumber(),
                    isUpdate ? "更新成功" : "新增成功"
            );
            return ResponseBase.ok(response);

        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(transactionStatus);
            log.error("保存资产变更申请单失败", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "保存失败: " + e.getMessage());
        }
    }

    /**
     * 处理保存后的操作提交
     */
    private ResponseBase handleOperationSubmission(AssetChangeApplicationSaveRequest request, AssetChangeApplication application) throws InterruptedException {
        // 如果isOperation为true，则调用submitForApproval方法
        if (Boolean.TRUE.equals(request.getIsOperation())) {
            TimeUnit.MILLISECONDS.sleep(100);
            AssetChangeOperationRequest operationRequest = request.getOperationRequest();
            if (operationRequest != null) {
                // 设置applicationId为刚保存的申请单ID
                operationRequest.setApplicationId(application.getId());
                operationRequest.setEid(application.getEid());

                IAssetChangeApplicationService proxyService = applicationContext.getBean(IAssetChangeApplicationService.class);
                ResponseBase submitResult = proxyService.submitForApproval(operationRequest);
                if (!submitResult.checkIsSuccess()) {
                    log.error("提交审批失败: {}", submitResult.getErrMsg());
                    return ResponseBase.error(submitResult.getCode(), "保存成功但提交审批失败: " + submitResult.getErrMsg());
                }
                log.info("资产变更申请单保存并提交审批成功，申请单ID: {}, 申请单编号: {}", application.getId(), application.getApplicationNumber());
            } else {
                log.warn("isOperation为true但operationRequest为空，跳过提交审批");
            }
        }
        return ResponseBase.ok();
    }

    /**
     * 参数校验
     */
    private ResponseBase validateRequest(AssetChangeApplicationSaveRequest request) {
        if (request == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY.getCode(), "请求参数不能为空");
        }

        AssetChangeApplication application = request.getApplication();
        if (application == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY.getCode(), "申请单信息不能为空");
        }

        // 校验必填字段
        if (application.getEid() == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY.getCode(), "eid不能为空");
        }

        if (StringUtils.isEmpty(application.getApplicantUserId())) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY.getCode(), "申请人用户ID不能为空");
        }

        if (StringUtils.isEmpty(application.getApplicantName())) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY.getCode(), "申请人名称不能为空");
        }

        if (StringUtils.isEmpty(application.getApplicationCategory())) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY.getCode(), "申请类别不能为空");
        }

        if (StringUtils.isEmpty(application.getChangePriority())) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY.getCode(), "变更优先级不能为空");
        }

        return ResponseBase.ok();
    }

    /**
     * 基础参数校验 - 校验eid和applicationId
     */
    private ResponseBase validateBasicRequest(AssetChangeOperationRequest request) {
        if (request == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY.getCode(), "请求参数不能为空");
        }

        if (request.getEid() == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY.getCode(), "eid不能为空");
        }

        if (request.getApplicationId() == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY.getCode(), "申请单ID不能为空");
        }

        return ResponseBase.ok();
    }

    /**
     * 生成申请单编号
     * 格式：CR-YYYYMMDD-流水号，示例：CR-20250401-001
     */
    @Override
    public String generateApplicationNumber(Long eid) {
        // 生成日期字符串
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 查询当天相同eid下的最大流水号
        int maxSequence = assetChangeApplicationMapper.getMaxSequenceByEid(eid);

        // 流水号加1，格式化为3位数字
        String sequence = String.format("%03d", maxSequence + 1);

        return "CR-" + dateStr + "-" + sequence;
    }

    /**
     * 检查申请单编号是否存在
     */
    private boolean checkApplicationNumberExists(String applicationNumber, Long excludeId) {
        int count = assetChangeApplicationMapper.checkApplicationNumberExists(applicationNumber, excludeId);
        return count > 0;
    }

    /**
     * 保存主表信息
     */
    private void saveMainApplication(AssetChangeApplication application, boolean isUpdate) {
        LocalDateTime now = LocalDateTime.now();

        if (isUpdate) {
            // 更新操作
            application.setUpdateTime(now);
            AssetChangeApplication assetChangeApplication = assetChangeApplicationMapper.selectById(application.getId());
            if (!ApplicationStatus.isEqual(assetChangeApplication.getApplicationStatus(), ApplicationStatus.NOT_SUBMITTED)
                    && !ApplicationStatus.isEqual(assetChangeApplication.getApplicationStatus(), ApplicationStatus.APPROVAL_RETURNED)
                    && !ApplicationStatus.isEqual(assetChangeApplication.getApplicationStatus(), ApplicationStatus.NOT_APPROVED)
            ) {
                throw new RuntimeException("当前状态不允许更新");
            }
            assetChangeApplicationMapper.updateById(application);
        } else {
            // 新增操作
            application.setId(SnowFlake.getInstance().newId());
            application.setCreateTime(now);
            application.setApplicationDate(LocalDate.now());
            application.setApplicationStatus(ApplicationStatus.NOT_SUBMITTED.name());
            application.setUpdateTime(now);
            assetChangeApplicationMapper.insert(application);
        }

        // 无论新增还是更新，都保存操作记录
        saveOperationRecord(application, isUpdate);
    }

    /**
     * 保存子表数据
     */
    private void saveSubTables(AssetChangeApplicationSaveRequest request, Long applicationId) {
        // 如果是更新操作，先删除原有的子表数据
        if (request.getApplication().getId() != null && request.getApplication().getId() > 0) {
            deleteExistingSubTableData(applicationId);
        }

        // 保存变更资产清单
        saveAssetList(request.getAssetList(), applicationId);

        // 保存执行计划及停机状况
        saveExecutionPlansWithShutdownStatus(request.getExecutionPlanList(), applicationId);
    }

    /**
     * 删除已存在的子表数据
     */
    private void deleteExistingSubTableData(Long applicationId) {
        // 删除变更资产清单
        assetChangeApplicationAssetListMapper.deleteByApplicationId(applicationId);

        // 查询执行计划ID列表，用于删除停机状况
        List<AssetChangeApplicationExecutionPlan> existingPlans =
                assetChangeApplicationExecutionPlanMapper.selectByApplicationId(applicationId);

        if (!CollectionUtils.isEmpty(existingPlans)) {
            List<Long> planIdList = existingPlans.stream()
                    .map(AssetChangeApplicationExecutionPlan::getId)
                    .collect(Collectors.toList());

            // 删除停机状况
            assetChangeExecutionPlanShutdownStatusMapper.deleteByPlanIdList(planIdList);
        }

        // 删除执行计划
        assetChangeApplicationExecutionPlanMapper.deleteByApplicationId(applicationId);

        // 不删除操作记录，保留历史记录
    }

    /**
     * 保存变更资产清单
     */
    private void saveAssetList(List<AssetChangeApplicationAssetList> assetList, Long applicationId) {
        if (CollectionUtils.isEmpty(assetList)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        for (AssetChangeApplicationAssetList asset : assetList) {
            asset.setId(SnowFlake.getInstance().newId());
            asset.setApplicationId(applicationId);
            asset.setCreateTime(now);
            asset.setUpdateTime(now);
        }

        assetChangeApplicationAssetListMapper.batchInsert(assetList);
    }

    /**
     * 保存执行计划及停机状况
     */
    private void saveExecutionPlansWithShutdownStatus(
            List<AssetChangeApplicationSaveRequest.ExecutionPlanWithShutdownStatus> executionPlanList,
            Long applicationId) {

        if (CollectionUtils.isEmpty(executionPlanList)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        List<AssetChangeApplicationExecutionPlan> planList = new ArrayList<>();
        List<AssetChangeExecutionPlanShutdownStatus> shutdownStatusList = new ArrayList<>();

        for (AssetChangeApplicationSaveRequest.ExecutionPlanWithShutdownStatus planWithStatus : executionPlanList) {
            AssetChangeApplicationExecutionPlan plan = planWithStatus.getExecutionPlan();
            AssetChangeExecutionPlanShutdownStatus shutdownStatus = planWithStatus.getShutdownStatus();

            if (plan != null) {
                // 设置执行计划信息
                Long planId = SnowFlake.getInstance().newId();
                plan.setId(planId);
                plan.setApplicationId(applicationId);
                plan.setPlanStatus(PlanStatus.NOT_EXECUTED.name());
                plan.setCreateTime(now);
                plan.setUpdateTime(now);
                planList.add(plan);

                // 设置停机状况信息
                if (shutdownStatus != null) {
                    shutdownStatus.setId(SnowFlake.getInstance().newId());
                    shutdownStatus.setPlanId(planId);
                    shutdownStatus.setCreateTime(now);
                    shutdownStatus.setUpdateTime(now);
                    shutdownStatusList.add(shutdownStatus);
                }
            }
        }

        // 批量保存执行计划
        if (!CollectionUtils.isEmpty(planList)) {
            assetChangeApplicationExecutionPlanMapper.batchInsert(planList);

            // 批量保存停机状况
            if (!CollectionUtils.isEmpty(shutdownStatusList)) {
                assetChangeExecutionPlanShutdownStatusMapper.batchInsert(shutdownStatusList);
            }
        }
    }

    /**
     * 保存操作记录
     */
    private void saveOperationRecord(AssetChangeApplication application, boolean isUpdate) {
        AssetChangeApplicationOperationRecord operationRecord = AssetChangeApplicationOperationRecord.builder()
                .id(SnowFlake.getInstance().newId())
                .applicationId(application.getId())
                .operatorUserId(application.getApplicantUserId())
                .operatorName(application.getApplicantName())
                .operationTime(LocalDateTime.now())
                .nextUserId(application.getApplicantUserId())
                .nextUserName(application.getApplicantName())
                .operationType(isUpdate ? OperationType.UPDATE.name() : OperationType.CREATE.name())
                .operationOpinion(null)
                .operationExplanation(null)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        try {
            int insert = assetChangeApplicationOperationRecordMapper.insert(operationRecord);
            log.info("操作记录保存成功，影响行数: {}, 记录ID: {}", insert, operationRecord.getId());
        } catch (Exception e) {
            log.error("保存操作记录失败", e);
            throw e;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase deleteAssetChangeApplication(String id) {
        try {
            if (StringUtils.isEmpty(id)) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "申请单ID不能为空");
            }

            long applicationId = Long.parseLong(id);
            AssetChangeApplication application = assetChangeApplicationMapper.selectById(applicationId);

            if (application == null) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "未找到指定的申请单");
            }

            // 检查申请单状态
            if (!ApplicationStatus.isEqual(application.getApplicationStatus(), ApplicationStatus.NOT_SUBMITTED)) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "只有“未提交”状态的申请单才能被删除");
            }

            // 删除所有相关的子表数据
            deleteExistingSubTableData(applicationId);

            // 删除主表数据
            assetChangeApplicationMapper.deleteById(applicationId);

            log.info("资产变更申请单删除成功，申请单ID: {}", applicationId);

            return ResponseBase.ok("删除成功");

        } catch (NumberFormatException e) {
            log.error("无效的申请单ID格式", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "无效的ID格式");
        } catch (Exception e) {
            log.error("删除资产变更申请单失败", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase submitForApproval(AssetChangeOperationRequest request) {
        // 基础参数校验
        ResponseBase validationResult = validateBasicRequest(request);
        if (!validationResult.checkIsSuccess()) {
            return validationResult;
        }

        AssetChangeApplication app = loadAndSetState(request.getApplicationId());
        app.submitApproval();
        assetChangeApplicationMapper.updateById(app); // 将更新后的状态存回数据库

        // 1. 提交审批  ：  通知审批人   邮件 需要向邀请api发送
        AssetChangeEmailEvent emailEvent = new AssetChangeEmailEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                app.getApplicationStatus(),
                request.getOperationOpinion(),
                getCurrentOperatorInfo(request.getNextUserId(),request.getEid()),
                app.getEid()
        );
        eventPublisher.publishEvent(emailEvent);
        return ResponseBase.ok("提交成功");
    }


    /**
     * 审批通过
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase approveApplication(AssetChangeOperationRequest request) {
        // 基础参数校验
        ResponseBase validationResult = validateBasicRequest(request);
        if (!validationResult.checkIsSuccess()) {
            return validationResult;
        }

        AssetChangeApplication app = loadAndSetState(request.getApplicationId());
        app.setLatestApprovalComment(request.getOperationOpinion());
        app.approvePass();
        assetChangeApplicationMapper.updateById(app);

        // 4. 审批通过：通知提交人  邮件
        AssetChangeEmailEvent emailEvent = new AssetChangeEmailEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                app.getApplicationStatus(),
                request.getOperationOpinion(),
                getCurrentOperatorInfo(app.getApplicantUserId(),request.getEid()),
                app.getEid()
        );
        eventPublisher.publishEvent(emailEvent);
        return ResponseBase.ok("审批通过");
    }

    /**
     * 审批退回修改
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase adjustApplication(AssetChangeOperationRequest request) {
        // 基础参数校验
        ResponseBase validationResult = validateBasicRequest(request);
        if (!validationResult.checkIsSuccess()) {
            return validationResult;
        }

        AssetChangeApplication app = loadAndSetState(request.getApplicationId());
        app.setLatestApprovalComment(request.getOperationOpinion());
        app.approveAdjust();
        assetChangeApplicationMapper.updateById(app);

        // 2. 审批需要调整：通知提交人  邮件
        AssetChangeEmailEvent emailEvent = new AssetChangeEmailEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                app.getApplicationStatus(),
                request.getOperationOpinion(),
                getCurrentOperatorInfo(app.getApplicantUserId(),request.getEid()),
                app.getEid()
        );
        eventPublisher.publishEvent(emailEvent);
        return ResponseBase.ok("审批退回修改");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase approveFailApplication(AssetChangeOperationRequest request) {
        // 基础参数校验
        ResponseBase validationResult = validateBasicRequest(request);
        if (!validationResult.checkIsSuccess()) {
            return validationResult;
        }

        AssetChangeApplication app = loadAndSetState(request.getApplicationId());
        app.setLatestApprovalComment(request.getOperationOpinion());
        app.approveFail();
        assetChangeApplicationMapper.updateById(app);

        // 发布报告生成事件（同事务）
        AssetChangeReportEvent reportEvent = new AssetChangeReportEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                request.getOperationOpinion(),
                app.getEid()
        );
        eventPublisher.publishEvent(reportEvent);

        // 3. 审批不通过：通知提交人  邮件
        AssetChangeEmailEvent emailEvent = new AssetChangeEmailEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                app.getApplicationStatus(),
                request.getOperationOpinion(),
                getCurrentOperatorInfo(app.getApplicantUserId(),request.getEid()),
                app.getEid()
        );
        eventPublisher.publishEvent(emailEvent);

        return ResponseBase.ok("审批退回修改");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase executeApplication(AssetChangeOperationRequest request) {
        // 基础参数校验
        ResponseBase validationResult = validateBasicRequest(request);
        if (!validationResult.checkIsSuccess()) {
            return validationResult;
        }

        // 参数校验
        ResponseBase validatePlanRequest = validatePlanRequest(request);
        if (!validatePlanRequest.checkIsSuccess()) {
            return validationResult;
        }

        AssetChangeApplication app = loadAndSetState(request.getApplicationId());


        // 保存执行记录
        saveExecutionRecord(request);

        // 更新执行计划状态
        updateExecutionPlanStatus(request);

        app.execute();

        assetChangeApplicationMapper.updateById(app);
        return ResponseBase.ok(request.getObjId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase submitAcceptanceApplication(AssetChangeOperationRequest request) {
        // 基础参数校验
        ResponseBase validationResult = validateBasicRequest(request);
        if (!validationResult.checkIsSuccess()) {
            return validationResult;
        }

        AssetChangeApplication app = loadAndSetState(request.getApplicationId());
        app.submitAcceptance();
        assetChangeApplicationMapper.updateById(app);
        // 5. 提交验收：通知验收人 邮件 发送邀请
        AssetChangeEmailEvent emailEvent = new AssetChangeEmailEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                app.getApplicationStatus(),
                request.getOperationOpinion(),
                getCurrentOperatorInfo(request.getNextUserId(),request.getEid()),
                app.getEid()
        );
        eventPublisher.publishEvent(emailEvent);
        return ResponseBase.ok("提交验收");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase acceptApplication(AssetChangeOperationRequest request) {
        // 基础参数校验
        ResponseBase validationResult = validateBasicRequest(request);
        if (!validationResult.checkIsSuccess()) {
            return validationResult;
        }

        AssetChangeApplication app = loadAndSetState(request.getApplicationId());
        app.setLatestAcceptanceComment(request.getOperationOpinion());
        app.acceptPass();
        assetChangeApplicationMapper.updateById(app);

        // 发布报告生成事件（同事务）
        AssetChangeReportEvent reportEvent = new AssetChangeReportEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                request.getOperationOpinion(),
                app.getEid()
        );
        eventPublisher.publishEvent(reportEvent);

        // 8. 验收通过：通知提交人   邮件
        AssetChangeEmailEvent emailEvent = new AssetChangeEmailEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                app.getApplicationStatus(),
                request.getOperationOpinion(),
                getCurrentOperatorInfo(app.getApplicantUserId(),request.getEid()),
                app.getEid()
        );
        eventPublisher.publishEvent(emailEvent);

        return ResponseBase.ok("验收通过");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase acceptAdjustApplication(AssetChangeOperationRequest request) {
        // 基础参数校验
        ResponseBase validationResult = validateBasicRequest(request);
        if (!validationResult.checkIsSuccess()) {
            return validationResult;
        }

        AssetChangeApplication app = loadAndSetState(request.getApplicationId());
        app.setLatestAcceptanceComment(request.getOperationOpinion());
        app.acceptAdjust();
        assetChangeApplicationMapper.updateById(app);

        // 6. 验收需调整：通知 提交人  邮件
        AssetChangeEmailEvent emailEvent = new AssetChangeEmailEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                app.getApplicationStatus(),
                request.getOperationOpinion(),
                getCurrentOperatorInfo(app.getApplicantUserId(),request.getEid()),
                app.getEid()
        );
        eventPublisher.publishEvent(emailEvent);
        return ResponseBase.ok("验收返回需修改");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase acceptFailApplication(AssetChangeOperationRequest request) {
        // 基础参数校验
        ResponseBase validationResult = validateBasicRequest(request);
        if (!validationResult.checkIsSuccess()) {
            return validationResult;
        }

        AssetChangeApplication app = loadAndSetState(request.getApplicationId());
        app.setLatestAcceptanceComment(request.getOperationOpinion());
        app.acceptFail();
        assetChangeApplicationMapper.updateById(app);

        // 发布报告生成事件（同事务）
        AssetChangeReportEvent reportEvent = new AssetChangeReportEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                request.getOperationOpinion(),
                app.getEid()
        );
        eventPublisher.publishEvent(reportEvent);

        // 7. 验收不通过：通知  提交人  邮件
        AssetChangeEmailEvent emailEvent = new AssetChangeEmailEvent(
                this,
                app.getId(),
                app.getApplicationNumber(),
                app.getApplicationStatus(),
                request.getOperationOpinion(),
                getCurrentOperatorInfo(app.getApplicantUserId(),request.getEid()),
                app.getEid()
        );
        eventPublisher.publishEvent(emailEvent);

        return ResponseBase.ok("验收拒绝");
    }

    @Override
    public ResponseBase getAssetChangeApplicationDetail(Long applicationId) {
        try {
            log.info("开始查询资产变更申请单详情，申请单ID: {}", applicationId);

            // 参数校验
            if (applicationId == null || applicationId <= 0) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "申请单ID不能为空");
            }

            // 分步骤查询所有关联数据
            AssetChangeApplicationDetailResponse response = queryApplicationDetailStepByStep(applicationId);

            if (response.getApplication() == null) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "申请单不存在");
            }

            log.info("资产变更申请单详情查询成功，申请单ID: {}, 申请单编号: {}",
                    applicationId, response.getApplication().getApplicationNumber());

            return ResponseBase.ok(response);

        } catch (Exception e) {
            log.error("查询资产变更申请单详情失败，申请单ID: {}", applicationId, e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 分步骤查询申请单详情
     * 为了避免大查询，将查询分解为多个步骤
     */
    private AssetChangeApplicationDetailResponse queryApplicationDetailStepByStep(Long applicationId) {
        AssetChangeApplicationDetailResponse response = new AssetChangeApplicationDetailResponse();

        // 第1步：查询主表信息
        log.debug("步骤1：查询申请单主表信息，ID: {}", applicationId);
        AssetChangeApplication application = assetChangeApplicationMapper.selectById(applicationId);
        response.setApplication(application);

        if (application == null) {
            log.warn("申请单不存在，ID: {}", applicationId);
            return response;
        }

        // 第2步：查询变更资产清单
        log.debug("步骤2：查询变更资产清单，申请单ID: {}", applicationId);
        List<AssetChangeApplicationAssetList> assetList = assetChangeApplicationAssetListMapper.selectByApplicationId(applicationId);
        response.setAssetList(assetList);
        log.debug("查询到 {} 条资产清单记录", assetList != null ? assetList.size() : 0);

        // 第3步：查询执行计划
        log.debug("步骤3：查询执行计划，申请单ID: {}", applicationId);
        List<AssetChangeApplicationExecutionPlan> executionPlans = assetChangeApplicationExecutionPlanMapper.selectByApplicationId(applicationId);
        log.debug("查询到 {} 条执行计划记录", executionPlans != null ? executionPlans.size() : 0);
        Double executionComplete = assetChangeExecutePlanService.getExecutionComplete(applicationId);
        response.setExecutionComplete(executionComplete);


        // 第4步：查询操作记录
        log.debug("步骤4：查询操作记录，申请单ID: {}", applicationId);
        List<AssetChangeApplicationOperationRecord> operationRecords = assetChangeApplicationOperationRecordMapper.selectByApplicationIdOrderByTimeDesc(applicationId);
//        response.setOperationRecordList(operationRecords);
        log.debug("查询到 {} 条操作记录", operationRecords != null ? operationRecords.size() : 0);

        // 第5步：为每个执行计划查询停机状况和执行记录
        if (!CollectionUtils.isEmpty(executionPlans)) {
            List<AssetChangeApplicationDetailResponse.ExecutionPlanDetail> executionPlanDetails =
                    queryExecutionPlanDetails(executionPlans);
            response.setExecutionPlanList(executionPlanDetails);
        }

        // 设置ExecutionRecordDetail列表
         createExecutionRecordDetails(response.getExecutionPlanList(), operationRecords);
        response.setOperationRecordList(operationRecords);

        return response;
    }

    /**
     * 查询执行计划详情（包含停机状况和执行记录）
     */
    private List<AssetChangeApplicationDetailResponse.ExecutionPlanDetail> queryExecutionPlanDetails(
            List<AssetChangeApplicationExecutionPlan> executionPlans) {

        List<AssetChangeApplicationDetailResponse.ExecutionPlanDetail> executionPlanDetails = new ArrayList<>();

        // 收集所有计划ID
        List<Long> planIdList = executionPlans.stream()
                .map(AssetChangeApplicationExecutionPlan::getId)
                .collect(Collectors.toList());

        log.debug("步骤5：查询停机状况和执行记录，计划ID列表: {}", planIdList);

        // 批量查询停机状况
        Map<Long, AssetChangeExecutionPlanShutdownStatus> shutdownStatusMap = new HashMap<>();
        for (Long planId : planIdList) {
            AssetChangeExecutionPlanShutdownStatus shutdownStatus =
                    assetChangeExecutionPlanShutdownStatusMapper.selectByPlanId(planId);
            if (shutdownStatus != null) {
                shutdownStatusMap.put(planId, shutdownStatus);
            }
        }

        // 批量查询执行记录
        List<AssetChangeExecutionPlanExecutionRecord> allExecutionRecords =
                assetChangeExecutionPlanExecutionRecordMapper.selectByPlanIdList(planIdList);

        // 按计划ID分组执行记录
        Map<Long, List<AssetChangeExecutionPlanExecutionRecord>> executionRecordMap =
                allExecutionRecords.stream()
                        .collect(Collectors.groupingBy(AssetChangeExecutionPlanExecutionRecord::getPlanId));

        log.debug("查询到 {} 条停机状况记录，{} 条执行记录",
                shutdownStatusMap.size(), allExecutionRecords.size());

        // 组装执行计划详情
        for (AssetChangeApplicationExecutionPlan executionPlan : executionPlans) {
            AssetChangeApplicationDetailResponse.ExecutionPlanDetail detail =
                    new AssetChangeApplicationDetailResponse.ExecutionPlanDetail(executionPlan);

            // 设置停机状况
            detail.setShutdownStatus(shutdownStatusMap.get(executionPlan.getId()));

            // 设置执行记录
            List<AssetChangeExecutionPlanExecutionRecord> executionRecords =
                    executionRecordMap.getOrDefault(executionPlan.getId(), new ArrayList<>());
            detail.setExecutionRecordList(executionRecords);

            executionPlanDetails.add(detail);
        }



        return executionPlanDetails;
    }

    /**
     * 创建ExecutionRecordDetail列表
     */
    private void createExecutionRecordDetails(
            List<AssetChangeApplicationDetailResponse.ExecutionPlanDetail> executionPlanDetails,
            List<AssetChangeApplicationOperationRecord> operationRecords) {
        // 创建Map，key为executionRecordList中每个元素的id，value为executionPlanDetails
        Map<Long, AssetChangeExecutionPlanExecutionRecordMapping> executionRecordIdToExecutionPlanDetailsMap = new HashMap<>();
        for (AssetChangeApplicationDetailResponse.ExecutionPlanDetail executionPlanDetail : executionPlanDetails) {
            List<AssetChangeExecutionPlanExecutionRecord> executionRecordList = executionPlanDetail.getExecutionRecordList();
            if (!CollectionUtils.isEmpty(executionRecordList)) {
                for (AssetChangeExecutionPlanExecutionRecord executionRecord : executionRecordList) {
                    AssetChangeExecutionPlanExecutionRecordMapping mapping = new AssetChangeExecutionPlanExecutionRecordMapping();
                    mapping.setExecutionPlan(executionPlanDetail.getExecutionPlan());
                    mapping.setExecutionRecord(executionRecord);
                    executionRecordIdToExecutionPlanDetailsMap.put(executionRecord.getId(), mapping);
                }
            }
        }
        if (CollectionUtils.isEmpty(operationRecords)) {
            return ;
        }
        for (AssetChangeApplicationOperationRecord record : operationRecords) {
            if (LongUtil.isNotEmpty(record.getObjId())
                    && OperationType.EXECUTE.name().equals(record.getOperationType())) {
                AssetChangeExecutionPlanExecutionRecordMapping mapping = executionRecordIdToExecutionPlanDetailsMap.get(record.getObjId());
                record.setExecutionPlanDetail(mapping);
            }
        }

    }

    /**
     * 这是一个核心的辅助方法，用于从数据库加载对象并初始化其行为状态
     *
     * @param applicationId 申请单ID
     * @return 状态初始化完成的申请单对象
     */
    private AssetChangeApplication loadAndSetState(Long applicationId) {
        AssetChangeApplication assetChangeApplication = assetChangeApplicationMapper.selectById(applicationId);
        if (assetChangeApplication == null) {
            throw new RuntimeException("申请单不存在: ID=" + applicationId);
        }
        // 最关键的一步：根据数据库的字符串状态，通过工厂设置对应的行为状态对象
        assetChangeApplication.setCurrentState(stateFactory.getState(assetChangeApplication.getApplicationStatus()));
        return assetChangeApplication;
    }

    /**
     * 校验执行请求参数
     */
    private ResponseBase validatePlanRequest(AssetChangeOperationRequest request) {
        if (request == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY, "请求参数不能为空");
        }

        if (request.getApplicationId() == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY, "申请单ID不能为空");
        }

        AssetChangeExecutionPlanExecutionRecord executionRecord = request.getExecutionRecord();
        if (executionRecord == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY, "执行记录不能为空");
        }

        if (executionRecord.getPlanId() == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY, "计划ID不能为空");
        }

        if (!StringUtils.hasText(executionRecord.getExecutorUserId())) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY, "执行人用户ID不能为空");
        }

        if (!StringUtils.hasText(executionRecord.getExecutorName())) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY, "执行人名称不能为空");
        }

        if (!StringUtils.hasText(executionRecord.getPlanStatus())) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY, "计划状态不能为空");
        }

        if (executionRecord.getActualPlanStartDate() == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY, "实际计划开始日期不能为空");
        }

        if (executionRecord.getActualPlanEndDate() == null) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY, "实际计划结束日期不能为空");
        }

        // 校验开始时间不能晚于结束时间
        if (executionRecord.getActualPlanStartDate().isAfter(executionRecord.getActualPlanEndDate())) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY, "实际计划开始日期不能晚于结束日期");
        }

        return ResponseBase.ok();
    }

    /**
     * 保存执行记录
     */
    private void saveExecutionRecord(AssetChangeOperationRequest request) {
        AssetChangeExecutionPlanExecutionRecord executionRecord = request.getExecutionRecord();

        // 使用雪花ID生成主键
        executionRecord.setId(SnowFlake.getInstance().newId());
        request.setObjId(executionRecord.getId());
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        executionRecord.setCreateTime(now);
        executionRecord.setUpdateTime(now);

        try {
            int insert = assetChangeExecutionPlanExecutionRecordMapper.insert(executionRecord);
            log.info("执行记录保存成功，影响行数: {}, 记录ID: {}", insert, executionRecord.getId());
        } catch (Exception e) {
            log.error("保存执行记录失败", e);
            throw e;
        }
    }

    /**
     * 更新执行计划状态
     */
    private void updateExecutionPlanStatus(AssetChangeOperationRequest request) {
        AssetChangeExecutionPlanExecutionRecord executionRecord = request.getExecutionRecord();
        if (executionRecord == null || executionRecord.getPlanId() == null) {
            return;
        }

        Long planId = executionRecord.getPlanId();
        String newPlanStatus = executionRecord.getPlanStatus();

        // 创建执行计划对象用于更新
        AssetChangeApplicationExecutionPlan executionPlan = new AssetChangeApplicationExecutionPlan();
        executionPlan.setId(planId);
        executionPlan.setPlanStatus(newPlanStatus);
        executionPlan.setUpdateTime(LocalDateTime.now());

        // 更新执行计划状态
        int updatedRows = assetChangeApplicationExecutionPlanMapper.updateById(executionPlan);

        if (updatedRows > 0) {
            log.info("执行计划状态更新成功，计划ID: {}, 新状态: {}", planId, newPlanStatus);
        } else {
            log.warn("执行计划状态更新失败，计划ID: {}", planId);
        }
    }

    /**
     * 获取当前操作人信息
     * 可能需要从SecurityContext、Session或其他地方获取用户信息
     */
    private UserTenantInfoResponse getCurrentOperatorInfo(String userId,Long eid) {
        BaseResponse<UserTenantInfoResponse> userTenantInfoBr = aioUserFeignClient.getUserTenantInfo(userId,eid);
        if (!userTenantInfoBr.checkIsSuccess()) {
            return null;
        }
        return userTenantInfoBr.getData();
    }

    @Override
    public ResponseBase getAssetChangeApplicationPageList(AssetChangeApplicationPageRequest request) {
        try {
            log.info("开始分页查询资产变更申请单列表，请求参数: {}", request);

            // 参数校验
            if (request == null) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "请求参数不能为空");
            }

            // 处理申请进度搜索条件
            processApplicationProgressFilter(request);

            // 分页查询
            PageHelper.startPage(request.getCurrentPageNum(), request.getCurrentPageSize());
            List<AssetChangeApplicationPageResponse> list = assetChangeApplicationMapper.selectPageList(request);
            PageInfo<AssetChangeApplicationPageResponse> pageInfo = new PageInfo<>(list);

            // 设置申请进度信息
            if (!list.isEmpty()) {
                for (AssetChangeApplicationPageResponse response : list) {
                    response.setApplicationStatusAndProgress(response.getApplicationStatus());
                    Double executionComplete = assetChangeExecutePlanService.getExecutionComplete(response.getId());
                    response.setExecutionComplete(executionComplete);
                }
            }

            log.info("分页查询资产变更申请单列表成功，共查询到 {} 条记录", pageInfo.getTotal());
            return ResponseBase.ok(pageInfo);

        } catch (Exception e) {
            log.error("分页查询资产变更申请单列表失败", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 处理申请进度搜索条件
     * 将申请进度转换为对应的申请单状态列表进行搜索
     */
    private void processApplicationProgressFilter(AssetChangeApplicationPageRequest request) {
        String applicationProgress = request.getApplicationProgress();
        if (applicationProgress != null && !applicationProgress.trim().isEmpty()) {
            // 根据申请进度获取对应的申请单状态列表
            List<ApplicationStatus> statusList = ApplicationProgress.getApplicationStatusesByString(applicationProgress);

            if (!statusList.isEmpty()) {
                // 将状态列表转换为字符串列表，支持批量查询
                List<String> statusStringList = statusList.stream()
                        .map(ApplicationStatus::name)
                        .collect(Collectors.toList());
                request.setApplicationStatus(statusStringList);
                log.debug("申请进度 {} 对应申请单状态列表: {}", applicationProgress, statusStringList);
            } else {
                log.warn("无效的申请进度参数: {}", applicationProgress);
            }

            // 清空申请进度参数，避免SQL中重复使用
            request.setApplicationProgress(null);
        }
    }

    @Override
    public ResponseBase<com.digiwin.escloud.aiocmdb.model.assetchange.AssetChangeApplication> getAssetChangeApplicationDetailWithOperationRecords(Long applicationId) {
        try {
            if (applicationId == null) {
                return ResponseBase.error(ResponseCode.INVALID_PARAM, "申请单ID不能为空");
            }

            // 查询变更单基本信息
            AssetChangeApplication application = assetChangeApplicationMapper.selectById(applicationId);
            if (application == null) {
                return ResponseBase.error(ResponseCode.DATA_IS_NOT_FIND, "未找到对应的资产变更申请单");
            }

            // 查询操作记录
            List<AssetChangeApplicationOperationRecord> operationRecords = 
                assetChangeApplicationOperationRecordMapper.selectByApplicationId(applicationId);

            // 转换为common包中的对象
            com.digiwin.escloud.aiocmdb.model.assetchange.AssetChangeApplication commonApplication = 
                new com.digiwin.escloud.aiocmdb.model.assetchange.AssetChangeApplication();
            
            // 复制基本属性
            BeanUtils.copyProperties(application, commonApplication);
            
            // 转换操作记录
            if (!CollectionUtils.isEmpty(operationRecords)) {
                List<com.digiwin.escloud.aiocmdb.model.assetchange.AssetChangeApplicationOperationRecord> commonOperationRecords = 
                    operationRecords.stream().map(record -> {
                        com.digiwin.escloud.aiocmdb.model.assetchange.AssetChangeApplicationOperationRecord commonRecord = 
                            new com.digiwin.escloud.aiocmdb.model.assetchange.AssetChangeApplicationOperationRecord();
                        BeanUtils.copyProperties(record, commonRecord);
                        return commonRecord;
                    }).collect(Collectors.toList());
                
                commonApplication.setAssetChangeApplicationOperationRecordList(commonOperationRecords);
            }

            return ResponseBase.okT(commonApplication);
        } catch (Exception e) {
            log.error("查询资产变更单详情失败，applicationId: {}", applicationId, e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR, "查询资产变更单详情失败: " + e.getMessage());
        }
    }
}



