<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aioitms.collectconfig.dao.CollectConfigMapper">
    <resultMap id="collectConfigMap" type="com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig">
        <id property="id" column="id"/>
        <result property="scopeId" column="scopeId"/>
        <result property="collectCode" column="collectCode"/>
        <result property="collectName" column="collectName"/>
        <result property="collectCategory" column="collectCategory"/>
        <result property="collectDeviceType" column="collectDeviceType"/>
        <result property="description" column="description"/>
        <result property="collectVersion" column="collectVersion"/>
        <result property="collectType" column="collectType"/>
        <result property="collectorModelCode" column="collectorModelCode"/>
        <result property="collectorContent" column="collectorContent"/>
        <result property="parserModelCode" column="parserModelCode"/>
        <result property="parserContent" column="parserContent"/>
        <result property="transformsContent" column="transformsContent"/>
        <result property="sendersModelCode" column="sendersModelCode"/>
        <result property="sendersContent" column="sendersContent"/>
        <result property="isSystem" column="isSystem"/>
        <result property="isEnable" column="isEnable"/>
        <result property="isDeletedByAppServiceUnbind" column="isDeletedByAppServiceUnbind"/>
        <result property="uploadDataModelCode" column="uploadDataModelCode"/>
        <result property="execStrategyStatus" column="execStrategyStatus"/>
        <result property="interval" column="interval"/>
        <result property="uploadUrl" column="uploadUrl"/>
        <result property="platform" column="platform"/>
        <result property="__version__" column="__version__"/>
        <collection property="transformModelList" column="id" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aioitms.collectconfig.model.AiopsTransformModel" select="getTransformModelList"/>
    </resultMap>
    <!--    <select id="selectPluginConfigVersionByDeviceId" resultType="java.lang.Long">-->
    <!--        select ifnull(unix_timestamp(pluginListVersion),0) as pluginListVersion-->
    <!--        from itms_device_plugin_config_version-->
    <!--        where eid = #{eid} and deviceId = #{deviceId}-->
    <!--    </select>-->

    <!--    <select id="selectDefaultPluginConfig" resultType="java.lang.Long">-->
    <!--        select *-->
    <!--        from itms_device_plugin_config_version-->
    <!--        where eid = #{eid} and deviceId = #{deviceId}-->
    <!--    </select>-->

    <resultMap id="collectCategoryMap" type="com.digiwin.escloud.aioitms.collectconfig.model.CollectCategory">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <collection property="collectConfigList" resultMap="collectConfigMap" columnPrefix="acc_"/>
    </resultMap>

    <select id="selectDeviceCollectConfigList"
            resultType="com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig">
        <!--设备收集项授权控管逻辑:
            1.设备收集项要开启(isEnable=true)
            2.设备收集项需已对应到实际实例，且该实例为无须授权或已授权
            3.设备本身要已授权

            为了让地端重新计算版本时也能与云端相同，排序时使用collectCode+adcdId
        -->
        SELECT acc.*,
               adcd.id AS adcdId, adcd.execParamsVersion, adcd.execParamsContent, adcd.collectName AS adcdCollectName,
               ai.id AS aiId, ai.aiopsItem
        FROM aiops_device_collect adc
        LEFT JOIN aiops_device_collect_detail adcd ON adc.deviceId = #{deviceId} AND adc.id = adcd.adcId
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id AND adcd.isEnable = 1 AND acc.runtimeVersion = 'v1'
        INNER JOIN aiops_instance ai ON adcd.aiId = ai.id AND ai.aiopsAuthStatus IN ('NONE', 'AUTHED')
                                        AND EXISTS(SELECT 1 FROM aiops_instance
                                                   WHERE (
                                                         (aiopsItemType = 'DEVICE' AND aiopsItemId = #{deviceId}) or
                                                         (id = ai.id and independentAuth = 1)
                                                       )
                                                         AND aiopsAuthStatus IN ('NONE', 'AUTHED'))
        ORDER BY LENGTH(CONCAT(acc.collectCode, '.', adcd.id)), CONCAT(acc.collectCode, '.', adcd.id)
    </select>

    <select id="batchSelectDeviceEidByDeviceId" resultType="com.digiwin.escloud.aioitms.model.device.AiopsKitDevice">
        SELECT deviceId, eid
        FROM aiops_device
        WHERE 1 = 1
        <if test="deviceIds != null">
            <foreach collection="deviceIds" item="item" open=" AND deviceId IN(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="insertOrUpdateDeviceCollect">
        INSERT INTO aiops_device_collect (id, adId, deviceId, collectListVersion)
        VALUES(#{id}, #{adId}, #{deviceId}, #{collectListVersion})
        ON DUPLICATE KEY UPDATE adId = VALUES(adId), collectListVersion = VALUES(collectListVersion)
    </insert>

    <select id="selectDeviceByLastCheckInTime" resultType="com.digiwin.escloud.aioitms.model.device.AiopsKitDevice">
        SELECT deviceId, eid
        FROM aiops_device
        WHERE 1 = 1
        <if test="lastUpperLimitDate > 0">
            AND lastCheckInTime >= DATE_SUB(CURDATE(), INTERVAL #{lastUpperLimitDate} DAY)
        </if>
    </select>

    <select id="getCollectCategoryList" resultType="com.digiwin.escloud.aioitms.collectconfig.model.CollectCategory" >
        select * from aiops_collect_category a order by a.id asc
    </select>

    <select id="selectCollectCategoryListContainCollectConfig" resultMap="collectCategoryMap">
        SELECT a.*, b.id AS acc_id, b.scopeId AS acc_scopeId,
               b.collectCode AS acc_collectCode,
               b.collectName AS acc_collectName,
               b.collectCategory AS acc_collectCategory,
               b.collectDeviceType AS acc_collectDeviceType,
               b.description AS acc_description,
               b.uploadDataModelCode AS acc_uploadDataModelCode,
               b.platform acc_platform
        FROM aiops_collect_category a
        LEFT JOIN aiops_collect_config b ON a.code = b.collectCategory
        GROUP BY b.collectCode
        ORDER BY a.id, b.collectName
    </select>

    <select id="getAllCollectConfig" resultMap="collectConfigMap">
        select * from aiops_collect_config a
        where 1=1
        <if test="collectCategory != null and collectCategory !=''">
            AND ( a.collectCategory = #{collectCategory} )
        </if>
        <if test="scopeId != null and scopeId !=''">
            AND ( a.scopeId = #{scopeId} )
        </if>
        <if test="accIdList != null">
            <foreach collection="accIdList" item="item" open="AND id IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        order by a.collectCode asc
    </select>

    <select id="getTransformModelList" resultType = "com.digiwin.escloud.aioitms.collectconfig.model.AiopsTransformModel">
        select * from aiops_transform_model atm where atm.accId = #{id}
    </select>

    <select id="getCollectConfigDetail" resultMap="collectConfigMap">
        select * from aiops_collect_config a
        where id=#{id}
    </select>

    <insert id="insertCollectConfig" parameterType="com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig">
        insert into aiops_collect_config (id, scopeId, collectCode, collectName, collectCategory, collectDeviceType,
                                          description, collectVersion, collectType, collectorModelCode, collectorContent,
                                          parserModelCode, parserContent, transformsContent, sendersModelCode,
                                          sendersContent, uploadDataModelCode, execParamsModelCode, `interval`, cron,
                                          uploadUrl, platform, isSystem, isDeletedByAppServiceUnbind, sourceAccId, execStrategyStatus)
        values (#{id}, #{scopeId}, #{collectCode}, #{collectName}, #{collectCategory}, #{collectDeviceType},
                #{description}, #{collectVersion}, #{collectType}, #{collectorModelCode}, #{collectorContent},
                #{parserModelCode}, #{parserContent}, #{transformsContent}, #{sendersModelCode}, #{sendersContent},
                #{uploadDataModelCode}, #{execParamsModelCode}, #{interval}, #{cron}, #{uploadUrl}, #{platform},
                #{isSystem}, #{isDeletedByAppServiceUnbind}, #{sourceAccId}, #{execStrategyStatus})
    </insert>

    <insert id="batchInsertOrUpdateAcc">
        INSERT INTO aiops_collect_config (id, scopeId, collectCode, collectName, collectCategory, collectDeviceType,
                                          description, collectVersion, collectType, collectorModelCode,
                                          collectorContent, parserModelCode, parserContent, transformsContent,
                                          sendersModelCode, sendersContent, uploadDataModelCode, execParamsModelCode,
                                          `interval`, cron, uploadUrl, platform, isSystem, isDeletedByAppServiceUnbind,
                                          sourceAccId, execStrategyStatus, runtimeVersion)
        <foreach collection="list" item="item" open=" VALUES (" separator="), (" close=")">
            #{item.id}, #{item.scopeId}, #{item.collectCode}, #{item.collectName}, #{item.collectCategory},
            #{item.collectDeviceType}, #{item.description}, #{item.collectVersion}, #{item.collectType},
            #{item.collectorModelCode}, #{item.collectorContent}, #{item.parserModelCode}, #{item.parserContent},
            #{item.transformsContent}, #{item.sendersModelCode}, #{item.sendersContent}, #{item.uploadDataModelCode},
            #{item.execParamsModelCode}, #{item.interval}, #{item.cron}, #{item.uploadUrl}, #{item.platform},
            #{item.isSystem}, #{item.isDeletedByAppServiceUnbind}, #{item.sourceAccId}, #{item.execStrategyStatus},
            #{item.runtimeVersion}
        </foreach>
        ON DUPLICATE KEY UPDATE scopeId = VALUES(scopeId), collectCode = VALUES(collectCode),
                                collectName = VALUES(collectName), collectCategory = VALUES(collectCategory),
                                collectDeviceType = VALUES(collectDeviceType), description = VALUES(description),
                                collectVersion = VALUES(collectVersion), collectType = VALUES(collectType),
                                collectorModelCode = VALUES(collectorModelCode),
                                collectorContent = VALUES(collectorContent), parserModelCode = VALUES(parserModelCode),
                                parserContent = VALUES(parserContent), transformsContent = VALUES(transformsContent),
                                sendersModelCode = VALUES(sendersModelCode), sendersContent = VALUES(sendersContent),
                                uploadDataModelCode = VALUES(uploadDataModelCode),
                                execParamsModelCode = VALUES(execParamsModelCode), `interval` = VALUES(`interval`),
                                cron = VALUES(cron), uploadUrl = VALUES(uploadUrl), platform = VALUES(platform),
                                isSystem = VALUES(isSystem),
                                isDeletedByAppServiceUnbind = VALUES(isDeletedByAppServiceUnbind),
                                sourceAccId = VALUES(sourceAccId), execStrategyStatus = VALUES(execStrategyStatus),
                                runtimeVersion = VALUES(runtimeVersion)
    </insert>

    <update id="updateCollectConfigCollectVersion">
        update aiops_collect_config a set a.collectVersion = #{collectVersion} where a.id = #{id}
    </update>

    <delete id="deleteTransformModel">
        DELETE FROM aiops_transform_model
        WHERE accId = #{accId}
        <if test="atmIdList != null">
            <foreach collection="atmIdList" item="item" open="AND id IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>
    <select id="selectAtmIdListByAccId" resultType="java.lang.Long">
        SELECT id
        FROM aiops_transform_model
        WHERE accId = #{accId}
    </select>
    <insert id="batchInsertOrUpdateTransformModel" parameterType="com.digiwin.escloud.aioitms.collectconfig.model.AiopsTransformModel">
        INSERT INTO aiops_transform_model(id, accId, modelCode, modelContent)
        <foreach collection="list" item="item" open="VALUES (" separator="), (" close=")">
            #{item.id}, #{item.accId}, #{item.modelCode}, #{item.modelContent}
        </foreach>
        ON DUPLICATE KEY UPDATE accId = VALUES(accId), modelCode = VALUES(modelCode), modelContent = VALUES(modelContent)
    </insert>
    <update id="updateCollectConfig" parameterType="com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig">
        update aiops_collect_config
        set scopeId = #{scopeId}, collectCode = #{collectCode}, collectName = #{collectName},
            collectCategory = #{collectCategory}, collectDeviceType = #{collectDeviceType}, description = #{description},
            collectVersion = #{collectVersion}, collectType = #{collectType}, collectorModelCode = #{collectorModelCode},
            collectorContent = #{collectorContent}, parserModelCode = #{parserModelCode},
            parserContent = #{parserContent}, transformsContent = #{transformsContent},
            sendersModelCode = #{sendersModelCode}, sendersContent = #{sendersContent}, isSystem = #{isSystem},
            isDeletedByAppServiceUnbind = #{isDeletedByAppServiceUnbind}, uploadDataModelCode = #{uploadDataModelCode},
            execParamsModelCode = #{execParamsModelCode}, `interval` = #{interval}, cron = #{cron},
            uploadUrl = #{uploadUrl}, platform = #{platform}, execStrategyStatus = #{execStrategyStatus},
            runtimeVersion = CASE WHEN #{runtimeVersion} IS NULL THEN runtimeVersion ELSE #{runtimeVersion} END,
            isEdgeStorageEnabled = #{isEdgeStorageEnabled}
        where id =#{id}
    </update>
    <update id="updateBasicAcc">
        update aiops_collect_config
        set collectCode = #{collectCode}, collectName = #{collectName},
            description = #{description}, platform = #{platform}
        where id = #{id}
    </update>
    <update id="modifyCollectConfigStatus" parameterType="com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig">
        update aiops_collect_config a set a.isEnable = #{isEnable} where a.id = #{id}
    </update>

    <select id="checkCollectCodeIsExist" resultType="com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig">
        select *
        from aiops_collect_config acc
        where 1=1
        <if test="accId != null and accId > 0">
            and acc.id = #{accId}
        </if>
        <if test="collectCode != null and collectCode !=''">
            and acc.collectCode = #{collectCode}
        </if>
        <if test="scopeId != null and scopeId !=''">
            and acc.scopeId =#{scopeId}
        </if>
        limit 1
    </select>
    <delete id="deleteCollectConfig">
        delete from aiops_collect_config where id = #{id}
    </delete>
    <delete id="deleteCollectAppMapping">
        DELETE acam
        FROM aiops_collect_app_mapping acam
        INNER JOIN aiops_product_app apa ON apa.id = acam.apaId
        INNER JOIN supplier_product sp ON sp.id = apa.spId
        WHERE 1 != 1
        <trim prefix=" OR (" prefixOverrides="AND" suffix=")">
            <if test="accId != null and accId > 0">
                AND acam.accId = #{accId}
            </if>
            <if test="spId != null and spId > 0">
                AND sp.id = #{spId}
            </if>
        </trim>

    </delete>

    <select id="selectCollectConfigExecParamsModelCode" resultType="java.lang.String">
        SELECT execParamsModelCode
        FROM aiops_collect_config
        WHERE id = #{accId}
    </select>

    <update id="updateCollectConfigByMap">
        UPDATE aiops_collect_config
        SET ${columnName} = #{value}
        WHERE id = #{accId}
    </update>

    <select id="selectAccIdListByMap" resultType="java.lang.Long">
        SELECT acc.id
        FROM aiops_collect_config acc
        <where>
            <if test="collectCode != null and collectCode !=''">
                and acc.collectCode = #{collectCode}
            </if>
            <if test="collectCategory != null and collectCategory !=''">
                AND acc.collectCategory = #{collectCategory}
            </if>
            <if test="scopeId != null and scopeId !=''">
                AND acc.scopeId = #{scopeId}
            </if>
            <if test="accIdList != null">
                <foreach collection="accIdList" item="item" open=" AND id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAccByMap" resultType="com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig">
        SELECT
        <choose>
            <when test="specialColumns != null">
                <foreach collection="specialColumns" item="item" separator=", ">
                    ${item}
                </foreach>
            </when>
            <otherwise>
                acc.*
            </otherwise>
        </choose>
        FROM aiops_collect_config acc
        <where>
            <if test="collectCode != null and collectCode !=''">
                and acc.collectCode = #{collectCode}
            </if>
            <if test="collectCategory != null and collectCategory !=''">
                AND acc.collectCategory = #{collectCategory}
            </if>
            <if test="scopeId != null and scopeId !=''">
                AND acc.scopeId = #{scopeId}
            </if>
            <if test="accIdList != null">
                <foreach collection="accIdList" item="item" open=" AND id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="accId != null and accId > 0">
                AND acc.id = #{accId}
            </if>
            <if test="runtimeVersion != null and runtimeVersion != ''">
                AND acc.runtimeVersion = #{runtimeVersion}
            </if>
        </where>
    </select>
</mapper>