package com.digiwin.escloud.aiobasic.edr.service.edr.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiobasic.edr.constant.EdrConst;
import com.digiwin.escloud.aiobasic.edr.constant.EdrEventStatus;
import com.digiwin.escloud.aiobasic.edr.constant.OfConst;
import com.digiwin.escloud.aiobasic.edr.dao.EdrCustomerMapper;
import com.digiwin.escloud.aiobasic.edr.dao.EdrEventMapper;
import com.digiwin.escloud.aiobasic.edr.dao.EdrKbMapper;
import com.digiwin.escloud.aiobasic.edr.dao.EdrReportMapper;
import com.digiwin.escloud.aiobasic.edr.model.aio.*;
import com.digiwin.escloud.aiobasic.edr.model.base.*;
import com.digiwin.escloud.aiobasic.edr.model.base.edr.*;
import com.digiwin.escloud.aiobasic.edr.model.base.fine.SendStatus;
import com.digiwin.escloud.aiobasic.edr.model.base.fine.SetPolicyPermissionParam;
import com.digiwin.escloud.aiobasic.edr.model.edr.*;
import com.digiwin.escloud.aiobasic.edr.service.edr.IEdrCustomerService;
import com.digiwin.escloud.aiobasic.edr.service.edr.IEdrReportService;
import com.digiwin.escloud.aiobasic.edr.service.edr.impl.report.EdrReportRecord;
import com.digiwin.escloud.aiobasic.edr.service.edr.impl.report.EdrReportRecordV2;
import com.digiwin.escloud.aiobasic.edr.util.*;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrCustomerV2Mapper;
import com.digiwin.escloud.aiobasic.edrv2.service.impl.EdrReportV2Service;
import com.digiwin.escloud.aiobasic.mail.IMailSendService;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogSaveParam;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogType;
import com.digiwin.escloud.aiobasic.operatelog.service.impi.OperateLogService;
import com.digiwin.escloud.aiobasic.sync.model.tenant.Tenant;
import com.digiwin.escloud.aiobasic.util.EasyExcelUtil;
import com.digiwin.escloud.aiomail.MailAttachmentInfo;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioBasicFeignClient;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.feign.IssueServiceFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.model.excel.SimpleWriteData;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import io.reactivex.Observable;
import io.reactivex.schedulers.Schedulers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.*;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Slf4j
@RefreshScope
@Service
public class EdrReportService implements IEdrReportService, ParamCheckHelp  {

    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private String defaultSid;
    @Value("${flume.address}")
    private String flumeAddress;
    @Value("${aiobasic.address}")
    private String aiobasicAddress;
    @Autowired
    private EdrReportMapper edrReportMapper;
    @Autowired
    private EdrCustomerMapper edrCustomerMapper;
    @Autowired
    private EdrCustomerV2Mapper edrCustomerV2Mapper;
    @Autowired
    private EdrKbMapper edrKbMapper;
    @Autowired
    private EdrUtils edrUtils;
    @Autowired
    private AggsUtils aggsUtils;
    @Autowired
    private AggsV2Utils aggsV2Utils;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private RestHighLevelClient restHighLevelClient;
    @Autowired
    private ItemFactoryService itemFactoryService;
    @Autowired
    private IMailSendService mailSendService;
    @Autowired
    private EdrEventMapper edrEventMapper;
    @Autowired
    private ReportItemFactoryService reportItemFactoryService;
    @Autowired
    private ReportMailFactoryService reportMailFactoryService;
    @Autowired
    private AutoGenerateReportFactoryService autoGenerateReportFactoryService;
    @Autowired
    private IssueServiceFeignClient issueService;
    @Autowired
    private IEdrCustomerService edrCustomerService;
    @Autowired
    private EdrReportV2Service edrReportV2Service;
    @Autowired
    private OperateLogService operateLogService;
    @Autowired
    private EdrReportRecord edrReportRecord;
    @Autowired
    private EdrReportRecordV2 edrReportRecordV2;
    @Autowired
    AioItmsFeignClient aioItmsFeignClient;
    @Resource
    AioUserFeignClient aioUserFeignClient;
    @Value("${edr.recommendation:Known bad}")
    private String recommendation;
    @Value("${edr.baseScore:9.0}")
    private float baseScore;
    @Value("${edr.vulnerability:CRITICAL}")
    private String vulnerability;
    @Value("${elasticsearch.index.suffix:}")
    private String indexSuffix;
    @Value("${service.area}")
    private String connectArea;
    @Value("${mis.root}")
    private String misRoot;
    private final ExecutorService executorService = Executors.newFixedThreadPool(4);

    private static final String SYSTEM = "系統";

    @Override
    public List<ReportRecord> getReportRecords(String serviceCode, String sendStatus, String type, String orgCnNames,
                                               String userName, String sendTimeStart, String sendTimeEnd, String reportGenerateTimeStart,
                                               String reportGenerateTimeEnd, int page, int size, String modelVersion, Long id) {
        switch (modelVersion) {
            case "1.0":
                return edrReportRecord.getReportRecords(serviceCode, sendStatus, type, orgCnNames, userName,
                        sendTimeStart, sendTimeEnd, reportGenerateTimeStart, reportGenerateTimeEnd, page, size,
                        modelVersion, id);
            case "2.0":
                return edrReportRecordV2.getReportRecords(serviceCode, sendStatus, type, orgCnNames, userName,
                        sendTimeStart, sendTimeEnd, reportGenerateTimeStart, reportGenerateTimeEnd, page, size,
                        modelVersion, id);
            default:
                return null;
        }
    }

    @Override
    public ReportRecord getReportRecordDetail(long id) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        ReportRecord reportRecord = edrReportMapper.getReportRecordDetail(map);
        if (reportRecord != null) {
            ReportRecordSendLog sendLog = edrReportMapper.getLatestSendLog(id);
            if (sendLog != null) {
                sendLog.setReceiverList(edrReportMapper.getReceiverList(sendLog.getId()));
            }
            reportRecord.setSendLog(sendLog);
        }

        return reportRecord;
    }

    @Override
    public void saveEdrMailLog(String serviceCode, String reportId, String datasource) {
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        JSONObject jsonHead = new JSONObject();
        jsonHead.put("namenode", "namenode.example.com");
        jsonHead.put("datanode", "random_datanode.example.com");
        jsonObject.put("headers", jsonHead);

        JSONObject jsonBody = new JSONObject();
        jsonBody.put("eid", "");
        jsonBody.put("collectConfigId", "");
        DateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        jsonBody.put("collectedTime", sdf2.format(new Date()));
        jsonBody.put("deviceId", reportId);
        jsonBody.put("uploadDataModelCode", "EdrMailLog");
        jsonBody.put("deviceCollectDetailId", "");

        JSONObject dataContentJo = new JSONObject();
        dataContentJo.put("loginId", "");
        dataContentJo.put("userId", "");
        dataContentJo.put("userName", "");
        dataContentJo.put("ServiceCode", serviceCode);
        dataContentJo.put("datasource", datasource);
        dataContentJo.put("reportId", reportId);

        JSONArray dataContentJA = new JSONArray();
        dataContentJA.add(dataContentJo);
        jsonBody.put("dataContent", JSON.toJSONString(dataContentJA));
        jsonObject.put("body", JSON.toJSONString(jsonBody));

        jsonArray.add(jsonObject);
        insertEdrMailLog(jsonArray);

    }

    public String insertEdrMailLog(JSONArray jsonArray) {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(30000);
        factory.setReadTimeout(30000);

        RestTemplate restTemplate = new RestTemplate(factory);
        return restTemplate.postForObject(flumeAddress, jsonArray, String.class);
    }

    @Override
    public List<Map<String, String>> getReportTypes() {
        return edrReportMapper.getReportTypes();
    }

    @Override
    public int getReportRecordCount(String serviceCode, String sendStatus, String type, String orgCnNames,
                                    String userName, String sendTimeStart, String sendTimeEnd, String reportGenerateTimeStart,
                                    String reportGenerateTimeEnd, String modelVersion, Long id) {
        switch (modelVersion) {
            case "1.0":
                return edrReportRecord.getReportRecordCount(serviceCode, sendStatus, type, orgCnNames, userName,
                        sendTimeStart, sendTimeEnd, reportGenerateTimeStart, reportGenerateTimeEnd, modelVersion, id);
            case "2.0":
                return edrReportRecordV2.getReportRecordCount(serviceCode, sendStatus, type, orgCnNames, userName,
                        sendTimeStart, sendTimeEnd, reportGenerateTimeStart, reportGenerateTimeEnd, modelVersion, id);
            default:
                return 0;
        }
    }

    private EdrReportData getReportData(long orgId, ReportGenerateType reportGenerateType, List<CustomerOrgMap> orgs, ReportRecord reportRecord) {
        long sid = reportRecord.getSid();
        String serviceCode = reportRecord.getServiceCode();
        String[] orgCnNames = reportRecord.getOrgCnNames().split(",");

        EdrReportData edrReportData = new EdrReportData(SnowFlake.getInstance().newId(), sid, reportRecord.getId(), serviceCode,
                reportRecord.getCustomerName());
        //EVENTS STATISTICS
        List<HashMap<String, Long>> eventMaps = aggsUtils.getAggsByOneField(commonUtils.getIndex(EdrConst.EVENT_IDX, sid), "lastSeen", orgId,
                "classification.keyword", 10, reportRecord);
        edrReportData.setEventStatistics(commonUtils.buildBaseSummary(eventMaps));

        //DESTINATIONS 科维暂时不需要这部分数据
//        Script eventScript = new Script("doc['serverId'].value +'" + EdrConst.FIELD_SPLIT +
//                "'+doc['eventEdrId'].value");
//        List<HashMap<String, Long>> destMaps = aggsUtils.getAggsByMultiFieldCardinal(commonUtils.getIndex(EdrConst.EVENT_DEST_IDX, sid), "firstSeen", orgName,
//                "countryName.keyword", 50, eventScript, reportRecord);
//        reportData.setDestinations(destMaps);

        //MOST TARGETED DEVICES
        Script script = new Script("doc['serverId'].value +'" + EdrConst.FIELD_SPLIT +
                "'+doc['collectorEdrId'].value+'" + EdrConst.FIELD_SPLIT +
                "'+doc['device.keyword'].value");
        HashMap<String, Object> deviceMap = aggsUtils.getAggsByMultiField(commonUtils.getIndex(EdrConst.EVENT_COLLECTOR_IDX, sid), "lastSeen", orgId,
                script, 5, reportRecord);
        List<HashMap<String, Long>> collectorMaps = (List<HashMap<String, Long>>) deviceMap.get("mostTargetedDevices");
        edrReportData.setMostTargetedDevices(commonUtils.buildBaseSummary(collectorMaps));
        HashMap<String, HashMap<String, Long>> collectorCfMaps = (HashMap<String, HashMap<String, Long>>) deviceMap.get("deviceCfDetail");
        edrReportData.setDeviceCfDetail(commonUtils.buildBaseDetailSummary(collectorCfMaps));

        //MOST TARGETED PROCESSES
        String[] classifications = {"Malicious", "Suspicious", "Inconclusive", "Likely Safe", "PUP"};
        TermsQueryBuilder classificationQb = QueryBuilders.termsQuery("classification.keyword", classifications);
        List<HashMap<String, Long>> processMaps = aggsUtils.getAggsByMultiFieldCardinal(commonUtils.getIndex(EdrConst.EVENT_COLLECTOR_IDX, sid), "lastSeen", orgId,
                "process.keyword", 5, script, classificationQb, reportRecord, null);
        edrReportData.setMostTargetedProcesses(commonUtils.buildBaseSummary(processMaps));

        List<String> processes = processMaps.stream().map(o -> {
            Set<String> keys = o.keySet();
            String process = keys.toArray(new String[]{})[0];
            if ("sumOtherDocCount".equals(process) || "totalCount".equals(process))
                return "";
            return process;
        }).filter(o -> !StringUtils.isEmpty(o)).collect(Collectors.toList());
        HashMap<String, HashMap<String, Long>> processCfMaps = new HashMap<>();
        processes.stream().forEach(o -> {
            TermQueryBuilder processesQb = QueryBuilders.termQuery("process.keyword", o);
            List<HashMap<String, Long>> processDetailMaps = aggsUtils.getAggsByMultiFieldCardinal(commonUtils.getIndex(EdrConst.EVENT_COLLECTOR_IDX, sid), "lastSeen", orgId,
                    "classification.keyword", 5, script, classificationQb, reportRecord, processesQb);
            HashMap<String, Long> cfMap = new HashMap<>();
            processDetailMaps.stream().forEach(x -> {
                Set<String> keys = x.keySet();
                String cf = keys.toArray(new String[]{})[0];
                Long cfValue = x.get(cf);
                cfMap.put(cf, cfValue);
            });
            processCfMaps.put(o, cfMap);
        });
        edrReportData.setProcessCfDetail(commonUtils.buildBaseDetailSummary(processCfMaps));


        //COMMUNICATION CONTROL
        Script scriptProduct = new Script("doc['product.keyword'].value");
        TermQueryBuilder versionQb = QueryBuilders.termQuery("version.keyword", "");
        BoolQueryBuilder commonQueryBuild;
        //1.Unknown Vendors
        commonQueryBuild = edrUtils.getCommonQueryBuild(commonUtils.getIndex(EdrConst.PRODUCT_IDX, sid), "lastConnectionTime", orgId, reportRecord);
        String[] unknownVendor = {"Unknown Vendor (Signed)", "Unknown Vendor (Unsigned)"};
        TermsQueryBuilder unknownVendorsQb = QueryBuilders.termsQuery("vendor.keyword", unknownVendor);
        long unknownVendorCount = aggsV2Utils.getAggCountByMultiFieldCardinal(commonUtils.getIndex(EdrConst.PRODUCT_IDX, sid), scriptProduct, commonQueryBuild.mustNot(versionQb).must(unknownVendorsQb));
        HashMap<String, Long> unknownVendorsMap = new HashMap<>();
        unknownVendorsMap.put("Unknown Vendors", unknownVendorCount);

        //2.Low Reputation
        commonQueryBuild = edrUtils.getCommonQueryBuild(commonUtils.getIndex(EdrConst.PRODUCT_IDX, sid), "lastConnectionTime", orgId, reportRecord);
        TermsQueryBuilder lowReputationQb = QueryBuilders.termsQuery("recommendation.keyword", recommendation.split(","));
        long lowReputationCount = aggsV2Utils.getAggCountByMultiFieldCardinal(commonUtils.getIndex(EdrConst.PRODUCT_IDX, sid), scriptProduct, commonQueryBuild.mustNot(versionQb).must(lowReputationQb));
        HashMap<String, Long> lowReputationMap = new HashMap<>();
        lowReputationMap.put("Low Reputation", lowReputationCount);

        //3.Critical Vulnerability
        commonQueryBuild = edrUtils.getCommonQueryBuild(commonUtils.getIndex(EdrConst.PRODUCT_IDX, sid), "lastConnectionTime", orgId, reportRecord);
        TermsQueryBuilder criticalQb = QueryBuilders.termsQuery("severity.keyword", vulnerability.split(","));
        long criticalCount = aggsV2Utils.getAggCountByMultiFieldCardinal(commonUtils.getIndex(EdrConst.PRODUCT_IDX, sid), scriptProduct, commonQueryBuild.mustNot(versionQb).must(criticalQb));
        HashMap<String, Long> criticalMap = new HashMap<>();
        criticalMap.put("Critical Vulnerability", criticalCount);

        HashMap<String, Long> totalCountMap = new HashMap<>();
        totalCountMap.put("totalCount", lowReputationCount + unknownVendorCount + criticalCount);
        List<HashMap<String, Long>> communicationmaps = new ArrayList<>();
        communicationmaps.add(unknownVendorsMap);
        communicationmaps.add(lowReputationMap);
        communicationmaps.add(criticalMap);
        communicationmaps.add(totalCountMap);
        edrReportData.setCommunicationControl(commonUtils.buildBaseSummary(communicationmaps));

        //LICENSE STATUS
        edrReportData.setLicenseStatus(orgs);
        if (ReportGenerateType.MULTI.equals(reportGenerateType)) {
            CustomerOrgMap customerOrgMap = orgs.stream().filter(o -> orgId == o.getOrgId()).findAny().orElseGet(() -> new CustomerOrgMap());
            edrReportData.setLicenseStatus(Stream.of(customerOrgMap).collect(Collectors.toList()));
            edrReportData.setOrgCnName(customerOrgMap.getLocation());
        }

        return edrReportData;
    }


    private void generateReportData(ReportRecord reportRecord) {
        long sid = reportRecord.getSid();
        String serviceCode = reportRecord.getServiceCode();
//        int reportGenerateType = reportRecord.getReportGenerateType();
        ReportGenerateType reportGenerateType = ReportGenerateType.fromIndex(reportRecord.getReportGenerateType());
        String[] orgCnNames = reportRecord.getOrgCnNames().split(",");
        long[] orgIds = Arrays.stream(reportRecord.getOrgIds().split(",")).mapToLong(Long::parseLong).toArray();

        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", sid);
        map.put("serviceCode", serviceCode);
        map.put("orgIds", orgIds);
        List<CustomerOrgMap> orgs = edrCustomerMapper.getOrgs(map);
        orgs.stream().map(CustomerOrgMap::getOrg)
                .filter(Objects::nonNull)
                .filter(o -> LongUtil.isNotEmpty(o.getId()))
                .filter(o -> o.getSyncStatus() != null && !o.getSyncStatus().equals(-1))
                .forEach(org -> {
                    EsOrgSystemSummary esOrgSystemSummary = edrUtils.getEsOrgSystemSummary(org.getId(), sid);
                    if (Objects.isNull(esOrgSystemSummary)) {
                        return;
                    }
                    JSONObject orgSystemSummary = esOrgSystemSummary.getOrgSystemSummary();
                    if (orgSystemSummary.containsKey("collectorsState")) {
                        org.setCollectorsState(orgSystemSummary.getJSONObject("collectorsState"));
                    }
                });
        List<EdrReportData> edrReportDatas = new ArrayList<>();
        //开会确认先用同步
        switch (reportGenerateType) {
            case ONE: {
                EdrReportData edrReportData = getReportData(0, reportGenerateType, orgs, reportRecord);
                edrReportDatas.add(edrReportData);
                break;
            }
            case MULTI: {
                IntStream.range(0, orgIds.length).forEach(j -> {
                    EdrReportData edrReportData = getReportData(orgIds[j], reportGenerateType, orgs, reportRecord);
                    edrReportDatas.add(edrReportData);
                });
                break;
            }
            default:
        }
        if (edrReportDatas.size() > 0)
            try {
                HashMap<String, Object> supplementMap = new HashMap<>();
                supplementMap.put("sid", sid);
                supplementMap.put("serviceCode", serviceCode);
                TenantSupplement tenantSupplement = edrCustomerMapper.getTenantSupplement(supplementMap);
                Optional.ofNullable(tenantSupplement).ifPresent(o -> {
                    edrReportDatas.stream().forEach(x -> x.setRemarks(o.getRemarks()));
                });
                commonUtils.saveIdxData(commonUtils.getIndex(EdrConst.EDR_REPORT_DATA_NEW_IDX, null), edrReportDatas);
                commonUtils.refreshIndex(commonUtils.getIndex(EdrConst.EDR_REPORT_DATA_NEW_IDX, null));
            } catch (Exception ex) {
                log.error("saveIdxData " + commonUtils.getIndex(EdrConst.EDR_REPORT_DATA_NEW_IDX, null), ex);
            }
    }

    @Override
    public long generateReportRecord(ReportRecord reportRecord) {
        long id = SnowFlake.getInstance().newId();
        reportRecord.setId(id);
        reportRecord.setSid(RequestUtil.getEsHeaderSid(defaultSid));
        reportRecord.setReportGenerateTime(new Date());
        edrReportMapper.generateReportRecord(reportRecord);
        if ("1.0".equals(reportRecord.getModelVersion())) {
            generateReportData(reportRecord);
        }
        if ("2.0".equals(reportRecord.getModelVersion()) && Objects.equals(1, reportRecord.getType())) {
            String reportStartTime = DateUtil.getSomeDateFormatString(DateUtil.parseToLocalDateTime(reportRecord.getReportStartTime()), DateUtil.DATE_FORMATTER);
            String reportEndTime = DateUtil.getSomeDateFormatString(DateUtil.parseToLocalDateTime(reportRecord.getReportEndTime()), DateUtil.DATE_FORMATTER);
            String generateTime = DateUtil.getSomeDateFormatString(DateUtil.parseToLocalDateTime(reportRecord.getReportGenerateTime()), DateUtil.DATE_TIME_FORMATTER);
            edrReportV2Service.generateReport(reportRecord.getId(), reportStartTime, reportEndTime, generateTime, RequestUtil.getHeaderSid(), reportRecord.getOrgIds(), false);
        }

        // 保存附件
        saveReportFiles(reportRecord);

        return id;
    }

    private Boolean isTodayFirstDayOfMonth() {
        Calendar today = Calendar.getInstance();
        return today.get(Calendar.DAY_OF_MONTH) == 1;
    }

    private String plusDay(Integer day) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, day-1);
        cal.set(Calendar.HOUR_OF_DAY, 12);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(cal.getTime());
    }

    private Date getLastWeekFirstDay() {
        Calendar cal = Calendar.getInstance();
        Integer day = cal.get(Calendar.DAY_OF_WEEK);
        if (day == Calendar.SUNDAY) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        cal.add(Calendar.WEEK_OF_YEAR, -1);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);

        return cal.getTime();
    }

    private Date getLastWeekLastDay() {
        Calendar cal = Calendar.getInstance();
        Integer day = cal.get(Calendar.DAY_OF_WEEK);
        if (day == Calendar.SUNDAY) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        cal.add(Calendar.WEEK_OF_YEAR, -1);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
        cal.add(Calendar.DAY_OF_WEEK, 1);
        return cal.getTime();
    }

    private Date getLastMonthFirstDay() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -1);
        cal.set(Calendar.DAY_OF_MONTH, 1);

        return cal.getTime();
    }

    private Date getLastMonthLastDay() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return cal.getTime();
    }

    private Date getLastQuarterFirstDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, ((int) cal.get(Calendar.MONTH) / 3) * 3 - 3);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    private Date getLastQuarterLastDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, ((int) cal.get(Calendar.MONTH) / 3) * 3 - 1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum((Calendar.DAY_OF_MONTH)));

        return cal.getTime();
    }


    @Override
    public void autoGenerateReportRecord(AutoSendParam param) {
        Boolean isTodayFirstDayOfMonth;
        if (Objects.nonNull(param) && Objects.nonNull(param.getDayOfMonth()) && param.getDayOfMonth().equals("1")) {
            isTodayFirstDayOfMonth = true;
        } else {
            isTodayFirstDayOfMonth = isTodayFirstDayOfMonth();
        }

        //1 查所有的自动寄信设置
        Map<String, Object> map = new HashMap<>();
        Long sid = RequestUtil.getEsHeaderSid(defaultSid);
        map.put("sid", sid);
        map.put("status", 1);
        if (Optional.ofNullable(param).isPresent()) {
            map.put("serviceCodeList", param.getServiceCodeList()); //非必填
        }
        List<ReportRecordAutoSet> autoSets = edrReportMapper.getReportRecordAutoSets(map);
        if (CollectionUtils.isEmpty(autoSets)) {
            return;
        }

        // 調整為異步及多執行續處理
        Observable.fromIterable(autoSets)
                .subscribeOn(Schedulers.io())
                .subscribe(autoSet -> {
                            if (autoSet.getStatus() == 0) { //未开启
                                return;
                            }
                            // 執行生成報告
                            autoGenerateReportFactoryService.generateReportData(sid, autoSet, isTodayFirstDayOfMonth);
                        }, Throwable::printStackTrace
                );
    }

    @Override
    public void autoSend() {
        //1 查所有的自动寄信设置
        Map<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getEsHeaderSid(defaultSid));
        map.put("status", 1);
        map.put("type", 1);
        List<ReportRecordAutoSet> autoSets = edrReportMapper.getReportRecordAutoSets(map);
        if (CollectionUtils.isEmpty(autoSets)) {
            return;
        }
        //2 遍历循环
        Calendar cal = Calendar.getInstance();
        int currentMonth = cal.get(Calendar.MONTH) + 1; //当前月
        int currentDay = cal.get(Calendar.DAY_OF_MONTH); //当前日
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String date = sdf.format(new Date());
        Date lastMonthFirstDay = getLastMonthFirstDay();
        Date lastMonthLastDay = getLastMonthLastDay();
        Date lastQuarterFirstDay = getLastQuarterFirstDay();
        Date lastQuarterLastDay = getLastQuarterLastDay();
        Map<String, List<CustomerOrgMap>> authMap = new HashMap<>();
        for (int i = 0; i < autoSets.size(); i++) {
            ReportRecordAutoSet autoSet = autoSets.get(i);
            Date startTime = null;
            Date endTime = null;
            String serviceCode = autoSet.getServiceCode();
            if (autoSet.getStatus() == 0) {//未开启
                continue;
            }
            String cycle = autoSet.getCycle();
            if (StringUtils.isEmpty(cycle)) {
                continue;
            }
            if (Cycle.month.toString().equals(cycle)) { //月
                int day = autoSet.getDay();
                if (currentDay == day) {
                    //满足月+日的条件，
                    startTime = lastMonthFirstDay;
                    endTime = lastMonthLastDay;
                }
            } else if (Cycle.quarter.toString().equals(cycle)) {//季度
                if (currentDay == 25 && (currentMonth == 1 || currentMonth == 4 || currentMonth == 7 || currentMonth == 11)) {
                    //满足月+日的条件，
                    startTime = lastQuarterFirstDay;
                    endTime = lastQuarterLastDay;
                }
            } else {

            }
            try {
                if (startTime != null && endTime != null) {
                    //「端点防护」订阅模组，已到期 则不发送
                    List<CustomerOrgMap> ofOrgs = new ArrayList<>();
                    if (!authMap.containsKey(serviceCode)) {
                        ReportQryParam reportQryParam = new ReportQryParam();
                        reportQryParam.setServiceCode(serviceCode);
                        Object object = reportItemFactoryService.getReportItemData("auth", reportQryParam);
                        ofOrgs = (List<CustomerOrgMap>) (object);
                        authMap.put(serviceCode, ofOrgs);
                    } else {
                        ofOrgs = authMap.get(serviceCode);
                    }

                    if (!CollectionUtils.isEmpty(ofOrgs)) {
                        CustomerOrgMap customerOrgMap = ofOrgs.get(0);
                        Org org = customerOrgMap.getOrg();
                        String expirationDate = sdf.format(org.getExpirationDate());

                        if (date.compareTo(expirationDate) <= 0) { //没过期 可以自动发送邮件
                            Map<String, Object> pMap = new HashMap<>();
                            pMap.put("serviceCode", serviceCode);
                            pMap.put("auto", 1);
                            pMap.put("reportStartTime", sdf.format(startTime));
                            pMap.put("reportEndTime", sdf.format(endTime));
                            pMap.put("siteIds", autoSet.getOrgIds());
                            pMap.put("modelVersion", autoSet.getModelVersion());
                            ReportRecord rm = edrReportMapper.getReportRecordDetail(pMap);

                            sendReportMail(rm, ofOrgs);
                            updateSendStatus(rm.getId(), "2.0".equals(rm.getModelVersion()) ? SendStatus.S1Sended.toString() : SendStatus.Sended.toString());
                            updateSendLog(rm.getSendLog().getId());

                            // 處理紀錄
                            String eid = edrReportMapper.getEidByServiceCode(serviceCode);
                            saveAutoProcessRecord(AutoProcessRecord.builder()
                                    .id(StringUtil.toString(autoSet.getId()))
                                    .eid(eid)
                                    .processUserId("")
                                    .processUserName(SYSTEM)
                                    .enable(autoSet.getStatus())
                                    .operation("SEND")
                                    .createTime(DateUtil.getDate())
                                    .build());

                            // 發送紀錄
                            operateLogService.saveRecord(OperateLogSaveParam.builder()
                                    .eid(StringUtil.toString(autoSet.getEid()))
                                    .processUserId(autoSet.getProcessUserId())
                                    .processUserName(autoSet.getProcessUserName())
                                    .operateType("aiops_edr_send_mail")
                                    .startTime(DateUtil.getNowFormatString(DATE_TIME_FORMATTER))
                                    .modelVersion(autoSet.getModelVersion())
                                    .autoflag(true)
                                    .reciverMail(rm.getSendLog().getReceiverList().stream()
                                            .map(ReportReceiver::getReceiverMail)
                                            .map(StringUtil::toString)
                                            .collect(Collectors.joining(",")))
                                    .build()
                            );
                        }
                    }
                }
            } catch (Exception ex) {
                log.error(ex.toString());
            }
        }
    }

    @Override
    public BaseResponse getReportRecordAutoSetDetail(String serviceCode, String type, Long id, String modelVersion) {
        Map<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getEsHeaderSid(defaultSid));
        map.put("serviceCode", serviceCode);
        map.put("type", type);
        map.put("id", id);
        map.put("modelVersion", modelVersion);
        if (id != null) {
            return BaseResponse.ok(edrReportMapper.getReportRecordAutoSetDetail(map).get(0));
        }
        return BaseResponse.ok(edrReportMapper.getReportRecordAutoSetDetail(map));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse autoSet(ReportRecordAutoSet autoSet) {

        // 取得sid
        autoSet.setSid(RequestUtil.getEsHeaderSid(defaultSid));

        // 判定授權是否到期
        Boolean isExpary = false;
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String aiopsItem;
        if (autoSet.getModelVersion().equals("1.0")){
            aiopsItem = "EDR";
        } else if (autoSet.getModelVersion().equals("2.0")) {
            aiopsItem = "S1EDR";
        } else if (autoSet.getModelVersion().equals("ORACLE")) {
            aiopsItem = "ORACLEDB";
        } else {
            aiopsItem = "MSSQLDB";
        }
        BaseResponse<List<Map<String, Object>>> authData = aioUserFeignClient.getTenantModuleContractClassDetailByAiopsItemListV2(
                autoSet.getSid(),
                edrReportMapper.getEid(autoSet.getServiceCode()),
                Collections.singletonList(aiopsItem));
        if (authData.getCode().equals("0")) {
            List<Map<String, Object>> auth = authData.getData();
            isExpary = (auth != null && !auth.isEmpty() && sdf.format(new Date()).compareTo(Objects.toString(auth.get(0).get("endDate"))) > 0);
        }
        if (isExpary) {
            return BaseResponse.error(ResponseCode.AUTHORIZATION_HAS_EXPIRED);
        }

        // 傳入id為0表示新增配置，需要newId
        if (autoSet.getId() == 0) {
            autoSet.setId(SnowFlake.getInstance().newId());
        }
        //校验 同一客户 + 报告类型 + 模組(edr 1.0 | 2.0) 不能重复; 同一客户 + 报告类型 + 模組(dataBase ORACLE | MSSQL) + sourceId 不能重复
        Map<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getEsHeaderSid(defaultSid));
        map.put("serviceCode", autoSet.getServiceCode());
        map.put("type", autoSet.getType());
        map.put("modelVersion", autoSet.getModelVersion());
        List<String> orgIds = Arrays.stream((autoSet.getOrgIds().split(","))).collect(Collectors.toList());
        map.put("orgIds", orgIds);
        map.put("source", autoSet.getSource());
        map.put("sourceId", autoSet.getSourceId());
        // 查詢已存在的配置
        List<ReportRecordAutoSet> exists = edrReportMapper.getReportRecordAutoSetDetail(map);

        // 判斷配置是否已存在
        if (exists != null && exists.stream().anyMatch(e -> e.getId() != autoSet.getId())) {
            // edr
            if ("edr".equals(autoSet.getSource())) {
                List<String> existOrgCnNames = new ArrayList<>();
                List<String> orgCnNames = Arrays.stream((autoSet.getOrgCnNames().split(","))).collect(Collectors.toList());
                IntStream.range(0, orgIds.size())
                        .forEach(index -> {
                            if (exists.stream().anyMatch(e -> e.getOrgIds().contains(orgIds.get(index)))) {
                                existOrgCnNames.add(orgCnNames.get(index));
                            }
                        });
                if (!CollectionUtils.isEmpty(existOrgCnNames)) {
                    return BaseResponse.dynamicError(existOrgCnNames, ResponseCode.EDR_AUTO_SET_EXIST);
                }
            }
            // dataBase
            if ("dataBase".equals(autoSet.getSource())) {
                ReportRecordAutoSet existsItem = exists.get(0);
                Map<String, Object> result = edrReportMapper.getDbInstanceIpAndName(existsItem.getId(), existsItem.getServiceCode());
                HashMap<String, Object> dbInstanceIpAndName = new HashMap<>();

                // 明確設置每個字段，無論是否為 null
                dbInstanceIpAndName.put("id", result.get("id"));
                dbInstanceIpAndName.put("sourceId", result.get("sourceId"));
                dbInstanceIpAndName.put("instanceName", result.get("instanceName"));
                dbInstanceIpAndName.put("instanceDisplayName", result.get("instanceDisplayName"));
                dbInstanceIpAndName.put("instanceIpAddress", result.get("instanceIpAddress"));

                return BaseResponse.error(ResponseCode.DATABASE_AUTO_SET_EXIST, dbInstanceIpAndName);
            }
            return BaseResponse.error(ResponseCode.AUTO_SET_EXIST);
        }

        // 更新自動寄信配置
        int result = edrReportMapper.autoSet(autoSet);
        if (result == 0) {
            return BaseResponse.error(ResponseCode.SET_VERIFY);
        }

        // 刪除接收人
        edrReportMapper.deleteAutoSetReceivers(autoSet.getId());

        // 若傳入接收人不為空，新增接收人
        if (!CollectionUtils.isEmpty(autoSet.getReportRecordAutoSetReceivers())) {
            Map<String, Object> map1 = new HashMap<>();
            map1.put("errasId", autoSet.getId());
            List<Long> list = autoSet.getReportRecordAutoSetReceivers().stream().filter(o -> o.getReceivers() != null).map(k -> k.getReceivers().getId()).collect(Collectors.toList());
            map1.put("list", list);
            edrReportMapper.insertAutoSetReceivers(map1);
        }

        return BaseResponse.ok(autoSet.getId());
    }

    private long editSaveAndSendReportRecord(ReportRecord reportRecord) {

        ReportRecordSendLog sendLog = reportRecord.getSendLog();
        if (sendLog == null) {
            sendLog = new ReportRecordSendLog();
        }
        //查询单头的旧状态,如果是已发送状态，这时候需要新增单身的，需要设置sendLog.id 为0
        String sendStatus = edrReportMapper.getReportRecordStatus(reportRecord.getId());
        if (SendStatus.Sended.toString().equals(sendStatus)) {
            sendLog.setId(0L);
        }
        //1 更新单头
        reportRecord.setSid(RequestUtil.getEsHeaderSid(defaultSid));
        if (Objects.isNull(reportRecord.getReportGenerateTime()) && !StringUtils.isEmpty(reportRecord.getUrl())) {
            reportRecord.setReportGenerateTime(new Date());
        }

        boolean isS1EDR = Objects.equals("2.0", reportRecord.getModelVersion());
        boolean isImmediateSend = "0".equals(reportRecord.getSendMethod()); //报告寄送方式 0 立即寄送 | 1 预约寄送
        String appointmentStatus = isS1EDR ? SendStatus.S1Appointment.toString() : SendStatus.Appointment.toString();
        String sendedStatus = isS1EDR ? SendStatus.S1Sended.toString() : SendStatus.Sended.toString();
        if (isImmediateSend) {
            // 立即寄送
            reportRecord.setSendStatus(sendedStatus);
            sendLog.setSendTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        } else {
            // 預約寄送
            reportRecord.setSendStatus(appointmentStatus);
        }

        if (!StringUtils.isEmpty(reportRecord.getUrl())) {
            reportRecord.setUrl(reportRecord.getUrl().replace("#rid", String.valueOf(reportRecord.getId())));
        } else {
            reportRecord.setReportGenerateTime(null);
        }
        edrReportMapper.editReportRecord(reportRecord);

        if (!isImmediateSend && (reportRecord.getType() == 8 || reportRecord.getType() == 15)) {
            updateInspectReportSend(reportRecord.getId(), SendStatus.dbAppointment.toString());
        }

        //更新生成的数据
        if (!isS1EDR && reportRecord.getType() == 1) {
            updateGenerateReportData(reportRecord);
        } else {
            deleteGenerateReportData(reportRecord);
        }

        //2.更新单身
        sendLog.setErrId(reportRecord.getId());
        if (sendLog.getId() == 0L) {
            sendLog.setId(SnowFlake.getInstance().newId());
            edrReportMapper.saveReportSendLog(sendLog);
        } else {
            edrReportMapper.editReportSendLog(sendLog);
        }

        //3 保存子单身
        edrReportMapper.deleteReportSendLogReceivers(sendLog.getId());
        if (!CollectionUtils.isEmpty(sendLog.getReceiverList())) {
            ReportRecordSendLogReceivers sendLogReceivers = new ReportRecordSendLogReceivers();
            sendLogReceivers.setErrslId(sendLog.getId());
            List<ReportReceiver> uniqueReceivers = new ArrayList<>(sendLog.getReceiverList().stream()
                    .filter(Objects::nonNull)
                    .flatMap(r -> Arrays.stream(r.getReceiverMail().split(";"))
                            .filter(StringUtils::isNotBlank)
                            .map(email -> new AbstractMap.SimpleEntry<>(email.trim(), r)))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> existing
                    ))
                    .values());
            sendLogReceivers.setReceiverList(uniqueReceivers);

            edrReportMapper.saveReportSendLogReceivers(sendLogReceivers);
        }

        // 4 如果是立即寄送，需要发送邮件
        if ("0".equals(reportRecord.getSendMethod()))  //报告寄送方式 0 立即寄送 | 1 预约寄送
        {
            List<CustomerOrgMap> ofOrgs = Collections.emptyList();
            if (reportRecord.getType() != 8 && reportRecord.getType() != 15) {
                ReportQryParam reportQryParam = new ReportQryParam();
                reportQryParam.setServiceCode(reportRecord.getServiceCode());
                Object object = reportItemFactoryService.getReportItemData("auth", reportQryParam);
                ofOrgs = (List<CustomerOrgMap>) (object);
            }
            sendReportMail(reportRecord, ofOrgs);
        }

        // 保存附件
        saveReportFiles(reportRecord);

        // 刪除附件
        removeReportFiles(reportRecord);

        if (reportRecord.getType() != 8) {
            // 發送紀錄
            Long eid = edrCustomerV2Mapper.getEidByServiceCode(reportRecord.getServiceCode());
            operateLogService.saveRecord(OperateLogSaveParam.builder()
                    .eid(StringUtil.toString(eid))
                    .processUserId(reportRecord.getUserId())
                    .processUserName(reportRecord.getUserName())
                    .operateType("aiops_edr_send_mail")
                    .startTime(DateUtil.getNowFormatString(DATE_TIME_FORMATTER))
                    .modelVersion(reportRecord.getModelVersion())
                    .autoflag(false)
                    .reciverMail(reportRecord.getSendLog().getReceiverList().stream()
                            .map(ReportReceiver::getReceiverMail)
                            .map(StringUtil::toString)
                            .collect(Collectors.joining(",")))
                    .build()
            );
        }
        return reportRecord.getId();
    }

    public long insertSaveAndSendReportRecord(ReportRecord reportRecord, boolean auto, Long sid) {
        Long headerSid = RequestUtil.getHeaderSid();
        if (headerSid.equals(Long.valueOf(0))) {
            headerSid = sid;
        }

        ReportRecordSendLog sendLog = reportRecord.getSendLog();
        if (sendLog == null) {
            sendLog = new ReportRecordSendLog();
        }

        //1 保存单头
        long id = SnowFlake.getInstance().newId();
        reportRecord.setId(id);
        reportRecord.setSid(RequestUtil.getEsHeaderSid(defaultSid));
        if (!StringUtils.isEmpty(reportRecord.getUrl())) {
            reportRecord.setReportGenerateTime(new Date());
        }

        boolean isS1EDR = Objects.equals("2.0", reportRecord.getModelVersion());
        boolean isImmediateSend = "0".equals(reportRecord.getSendMethod()); //报告寄送方式 0 立即寄送 | 1 预约寄送
        String appointmentStatus = isS1EDR ? SendStatus.S1Appointment.toString() : SendStatus.Appointment.toString();
        String sendedStatus = isS1EDR ? SendStatus.S1Sended.toString() : SendStatus.Sended.toString();
        if (isImmediateSend) {
            if (auto) {
                // 立即寄送 + 自動發送
                reportRecord.setSendStatus(appointmentStatus);
                sendLog.setSendTime("");
            } else {
                // 立即寄送 + 非自動發送
                reportRecord.setSendStatus(sendedStatus);
                sendLog.setSendTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            }
        } else {
            // 預約寄送：只設定預約狀態
            reportRecord.setSendStatus(appointmentStatus);
        }

        if (!StringUtils.isEmpty(reportRecord.getUrl())) {
            reportRecord.setUrl(reportRecord.getUrl().replace("#rid", String.valueOf(id)));
        }
        edrReportMapper.generateReportRecord(reportRecord);

        //若有帶入資料庫運維報告id，則寫入edr報告id至資料庫運維報告表中
        if (LongUtil.isNotEmpty(reportRecord.getReportId())) {
            edrReportMapper.updateDbReportStatus(reportRecord.getReportId(), reportRecord.getId());
        }

        if (!auto && !isImmediateSend && (reportRecord.getType() == 8 || reportRecord.getType() == 15)) {
            updateInspectReportSend(reportRecord.getId(), SendStatus.dbAppointment.toString());
        }

        //2.保存单身
        long detailId = SnowFlake.getInstance().newId();

        sendLog.setId(detailId);
        sendLog.setErrId(reportRecord.getId());
        edrReportMapper.saveReportSendLog(sendLog);

        //3 保存子单身
        if (!CollectionUtils.isEmpty(sendLog.getReceiverList())) {
            ReportRecordSendLogReceivers sendLogReceivers = new ReportRecordSendLogReceivers();
            sendLogReceivers.setErrslId(sendLog.getId());
            List<ReportReceiver> uniqueReceivers = new ArrayList<>(sendLog.getReceiverList().stream()
                    .filter(Objects::nonNull)
                    .flatMap(r -> Arrays.stream(r.getReceiverMail().split(";"))
                            .filter(StringUtils::isNotBlank)
                            .map(email -> new AbstractMap.SimpleEntry<>(email.trim().toLowerCase(), r)))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> existing
                    ))
                    .values());
            sendLogReceivers.setReceiverList(uniqueReceivers);

            edrReportMapper.saveReportSendLogReceivers(sendLogReceivers);
        }

        // 4 如果是立即寄送，需要发送邮件
        if (!auto) {
            if ("0".equals(reportRecord.getSendMethod()))  //报告寄送方式 0 立即寄送 | 1 预约寄送
            {
                List<CustomerOrgMap> ofOrgs = Collections.emptyList();
                if (reportRecord.getType() != 8 && reportRecord.getType() != 15) {
                    ReportQryParam reportQryParam = new ReportQryParam();
                    reportQryParam.setServiceCode(reportRecord.getServiceCode());
                    Object object = reportItemFactoryService.getReportItemData("auth", reportQryParam);
                    ofOrgs = (List<CustomerOrgMap>) (object);
                }
                sendReportMail(reportRecord, ofOrgs);
            }
        }

        // 生成的数据
        if (!isS1EDR && (reportRecord.getType() == 1 || reportRecord.getType() == 5)) {
            generateReportData(reportRecord);
        }
        // 生成的数据
        if (isS1EDR && (reportRecord.getType() == 1)) {
            String reportStartTime = DateUtil.getSomeDateFormatString(DateUtil.parseToLocalDateTime(reportRecord.getReportStartTime()), DateUtil.DATE_FORMATTER);
            String reportEndTime = DateUtil.getSomeDateFormatString(DateUtil.parseToLocalDateTime(reportRecord.getReportEndTime()), DateUtil.DATE_FORMATTER);
            String generateTime = DateUtil.getSomeDateFormatString(DateUtil.parseToLocalDateTime(reportRecord.getReportGenerateTime()), DateUtil.DATE_TIME_FORMATTER);
            edrReportV2Service.generateReport(reportRecord.getId(), reportStartTime, reportEndTime, generateTime, headerSid, reportRecord.getOrgIds(), true);
        }

        // 保存附件
        saveReportFiles(reportRecord);

        if (reportRecord.getType() != 8 && reportRecord.getType() != 15) {
            // 發送紀錄
            Long eid = edrCustomerV2Mapper.getEidByServiceCode(reportRecord.getServiceCode());
            operateLogService.saveRecord(OperateLogSaveParam.builder()
                    .eid(StringUtil.toString(eid))
                    .processUserId(reportRecord.getUserId())
                    .processUserName(reportRecord.getUserName())
                    .operateType("aiops_edr_send_mail")
                    .startTime(DateUtil.getNowFormatString(DATE_TIME_FORMATTER))
                    .modelVersion(reportRecord.getModelVersion())
                    .autoflag(false)
                    .reciverMail(reportRecord.getSendLog().getReceiverList().stream()
                            .map(ReportReceiver::getReceiverMail)
                            .map(StringUtil::toString)
                            .collect(Collectors.joining(",")))
                    .build()
            );
        }
        return reportRecord.getId();
    }

    public long insertReportRecord(ReportRecord reportRecord) {
        //1 保存单头
        long id = SnowFlake.getInstance().newId();
        reportRecord.setId(id);

        reportRecord.setSid(RequestUtil.getEsHeaderSid(defaultSid));
        if (!StringUtils.isEmpty(reportRecord.getUrl())) {
            reportRecord.setReportGenerateTime(new Date());
        }

        reportRecord.setSendStatus(SendStatus.Draft.toString());
        if (!StringUtils.isEmpty(reportRecord.getUrl())) {
            reportRecord.setUrl(reportRecord.getUrl().replace("#rid", String.valueOf(id)));
        }
        edrReportMapper.generateReportRecord(reportRecord);

        //若有帶入資料庫運維報告id，則寫入edr報告id至資料庫運維報告表中
        if (LongUtil.isNotEmpty(reportRecord.getReportId())) {
            edrReportMapper.updateDbReportStatus(reportRecord.getReportId(), reportRecord.getId());
        }

        //2.保存单身
        long detailId = SnowFlake.getInstance().newId();
        ReportRecordSendLog sendLog = reportRecord.getSendLog();
        if (sendLog == null) {
            sendLog = new ReportRecordSendLog();
        }

        sendLog.setId(detailId);
        sendLog.setErrId(reportRecord.getId());
//        sendLog.setSendTime("");
        edrReportMapper.saveReportSendLog(sendLog);

        //3 保存子单身
        if (!CollectionUtils.isEmpty(sendLog.getReceiverList())) {
            ReportRecordSendLogReceivers sendLogReceivers = new ReportRecordSendLogReceivers();
            sendLogReceivers.setErrslId(sendLog.getId());
            sendLogReceivers.setReceiverList(sendLog.getReceiverList().stream().filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList()));

            edrReportMapper.saveReportSendLogReceivers(sendLogReceivers);
        }

        // 生成数据
        Boolean isS1EDR = Objects.equals("2.0", reportRecord.getModelVersion());
        if (!isS1EDR && reportRecord.getType() == 1) {
            generateReportData(reportRecord);
        }

        // 保存附件
        saveReportFiles(reportRecord);

        return reportRecord.getId();
    }

    public long editReportRecord(ReportRecord reportRecord) {
        ReportRecordSendLog sendLog = reportRecord.getSendLog();
        if (sendLog == null) {
            sendLog = new ReportRecordSendLog();
        }
        //查询单头的旧状态,如果是已发送状态，这时候需要新增单身的，需要设置sendLog.id 为0
        String sendStatus = edrReportMapper.getReportRecordStatus(reportRecord.getId());
        if (reportRecord.getType() == 8) {
            updateInspectReportSend(reportRecord.getId(), SendStatus.dbDraft.toString());
        }

        //1 更新单头
        reportRecord.setSid(RequestUtil.getEsHeaderSid(defaultSid));
        if (Objects.isNull(reportRecord.getReportGenerateTime()) && !StringUtils.isEmpty(reportRecord.getUrl())) {
            reportRecord.setReportGenerateTime(new Date());
        }
        if (StringUtils.isEmpty(reportRecord.getUrl())) {
            reportRecord.setReportGenerateTime(null);
        }
        reportRecord.setSendStatus(SendStatus.Draft.toString());
        if (!StringUtils.isEmpty(reportRecord.getUrl())) {
            reportRecord.setUrl(reportRecord.getUrl().replace("#rid", String.valueOf(reportRecord.getId())));
        } else {
            reportRecord.setReportGenerateTime(null);
        }
        edrReportMapper.editReportRecord(reportRecord);

        //2.更新单身
        if (SendStatus.Sended.toString().equals(sendStatus)) {
            sendLog.setId(0L);
        }
        sendLog.setErrId(reportRecord.getId());
        if (sendLog.getId() == 0L) {
            sendLog.setId(SnowFlake.getInstance().newId());
            edrReportMapper.saveReportSendLog(sendLog);
        } else {
            edrReportMapper.editReportSendLog(sendLog);
        }
        //2.更新子单身
        //先删联系人 在新增联系人
        edrReportMapper.deleteReportSendLogReceivers(sendLog.getId());
        if (!CollectionUtils.isEmpty(sendLog.getReceiverList())) {
            ReportRecordSendLogReceivers sendLogReceivers = new ReportRecordSendLogReceivers();
            sendLogReceivers.setErrslId(sendLog.getId());
            sendLogReceivers.setReceiverList(sendLog.getReceiverList().stream().filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList()));

            edrReportMapper.saveReportSendLogReceivers(sendLogReceivers);
        }
        // 更新生成的数据
        Boolean isS1EDR = Objects.equals("2.0", reportRecord.getModelVersion());
        if (!isS1EDR && reportRecord.getType() == 1) {
            updateGenerateReportData(reportRecord);
        } else {
            deleteGenerateReportData(reportRecord);
        }

        // 保存附件
        saveReportFiles(reportRecord);

        // 刪除附件
        removeReportFiles(reportRecord);

        return reportRecord.getId();
    }

    @Override
    public long saveReportRecord(ReportRecord reportRecord) {
        if (reportRecord.getId() == 0L) {//新增草稿
            return insertReportRecord(reportRecord);
        } else {//编辑草稿
            return editReportRecord(reportRecord);
        }
    }

    @Override
    public long saveAndSendReportRecord(ReportRecord reportRecord) {
        if (reportRecord.getId() == 0L) {//新增
            return insertSaveAndSendReportRecord(reportRecord, false, null);
        } else {//编辑
            return editSaveAndSendReportRecord(reportRecord);
        }
    }

    public int updateSendStatus(Long id, String status) {
        Map<String, Object> map = new HashMap<>();
        map.put("sendStatus", status);
        map.put("id", id);
        return edrReportMapper.updateSendStatus(map);
    }

    public int updateInspectReportSend(Long id, String status) {
        Map<String, Object> map = new HashMap<>();
        map.put("reportStatus", status);
        map.put("edrReportRecordId", id);
        map.put("reportSendTime", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        return edrReportMapper.updateInspectReportSend(map);
    }

    public int updateSendLog(Long errslId) {
        Map<String, Object> map = new HashMap<>();
        map.put("sendTime", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        map.put("id", errslId);
        return edrReportMapper.updateSendLog(map);
    }

    @Override
    public ResponseBase getAndAppointmentReport(AutoSendParam param) {
        ResponseBase responseBase = new ResponseBase();
        try {
            Boolean nonNull = Optional.ofNullable(param).isPresent();
            List<ReportRecord> unPublishReportRecords = edrReportMapper.getAndAppointmentReport(nonNull ? param.getServiceCodeList() : null);
            Map<String, List<CustomerOrgMap>> authMap = new HashMap<>();
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String date = sdf.format(new Date());
            // 調整為異步及多執行續處理
            Observable.fromIterable(unPublishReportRecords)
                    .subscribeOn(Schedulers.io())
                    .subscribe(reportRecord -> {
                                Boolean auto = reportRecord.isAuto();
                                if (auto && reportRecord.getType() == 8 &&
                                        (StringUtil.isEmpty(reportRecord.getDbId()) || StringUtil.isEmpty(reportRecord.getDeviceId()))) {
                                    return;
                                }
                                Boolean isS1EDR = "2.0".equals(reportRecord.getModelVersion());
                                ReportRecordSendLog sendLog = reportRecord.getSendLog();
                                String sentStatus = isS1EDR ? SendStatus.S1Sended.toString() : SendStatus.Sended.toString();
                                if (Objects.isNull(sendLog)) {
                                    return;
                                }
                                List<ReportReceiver> receiverList = sendLog.getReceiverList();
                                if (CollectionUtils.isEmpty(receiverList)) {
                                    return;
                                }
                                String serviceCode = reportRecord.getServiceCode();
                                String eid = StringUtil.toString(reportRecord.getEid());

                                //「端点防护」订阅模组，已到期 则不发送
                                List<CustomerOrgMap> ofOrgs = Collections.emptyList();
                                if (reportRecord.getType() != 8 && reportRecord.getType() != 15) {
                                    if (!authMap.containsKey(serviceCode + "_" + reportRecord.getModelVersion())) {
                                        ReportQryParam reportQryParam = new ReportQryParam();
                                        reportQryParam.setServiceCode(serviceCode);
                                        Object object = isS1EDR ? reportItemFactoryService.getReportItemData("authv2", reportQryParam)
                                                : reportItemFactoryService.getReportItemData("auth", reportQryParam);
                                        ofOrgs = (List<CustomerOrgMap>) (object);

                                        authMap.put(serviceCode + "_" + reportRecord.getModelVersion(), ofOrgs);
                                    } else {
                                        ofOrgs = authMap.get(serviceCode + "_" + reportRecord.getModelVersion());
                                    }

                                    //ofOrgs 这个可能为空，没法取年维日期
                                    if (auto && !CollectionUtils.isEmpty(ofOrgs)) {
                                        CustomerOrgMap customerOrgMap = ofOrgs.get(0);
                                        Org org = customerOrgMap.getOrg();
                                        String expirationDate = Objects.isNull(org.getExpirationDate()) ? "" : sdf.format(org.getExpirationDate());

                                        //自动发送 并且「端点防护1.0」订阅模组，没到期 才发送
                                        if (!isS1EDR && date.compareTo(expirationDate) > 0) {
                                            return;
                                        }
                                        //自动发送 并且「端点防护2.0」订阅模组，没到期 才发送
                                        if (isS1EDR && Strings.hasText(expirationDate) && date.compareTo(expirationDate) > 0) {
                                            return;
                                        }
                                    }

                                }
                                if (reportRecord.getType() == 8) { // 8 資料庫巡檢報告
                                    BaseResponse<List<Map<String, Object>>> dbAuthData = aioUserFeignClient.getTenantModuleContractClassDetailByAiopsItemListV2(
                                            reportRecord.getSid(),
                                            edrReportMapper.getEid(reportRecord.getServiceCode()),
                                            Collections.singletonList(reportRecord.getModelVersion().equals("ORACLE") ? "ORACLEDB" : "MSSQLDB"));
                                    if (dbAuthData.getCode().equals("0")) {
                                        List<Map<String, Object>> dbAuth = dbAuthData.getData();
                                        // 「数据库运维」订阅模组，没到期 才发送
                                        if (dbAuth == null || dbAuth.isEmpty() || date.compareTo(Objects.toString(dbAuth.get(0).get("endDate"))) > 0) {
                                            return;
                                        }
                                    }
                                }
                                // type是 "巡檢報告(8)" 或 "升版評估報告(15)" 更新檢報告發送狀態為已發送
                                if (reportRecord.getType() == 8 || reportRecord.getType() == 15) {
                                    updateInspectReportSend(reportRecord.getId(), SendStatus.dbSended.toString());
                                    reportRecord.setReportId(edrReportMapper.getDbReportRecordId(reportRecord.getId()));
                                }
                                updateSendStatus(reportRecord.getId(), sentStatus);
                                updateSendLog(sendLog.getId());

                                log.info(DateFormatUtils.format(new Date(), "yyyy-MM-dd hh:mm:ss") + " getAndAppointmentReport:" + sendLog.getId());
                                sendReportMail(reportRecord, ofOrgs);

                                // 升版評估報告預約寄送成功後紀錄LOG
                                if (reportRecord.getType() == 15) {
                                    // 郵件發送log
                                    operateLogService.saveRecord(OperateLogSaveParam.builder()
                                            .eid(eid)
                                            .processUserId("")
                                            .processUserName(SYSTEM)
                                            .reportId(StringUtil.toString(reportRecord.getReportId()))
                                            .operateType(OperateLogType.Send2MIS.getCode())
                                            .startTime(DateUtil.getNowFormatString(DATE_TIME_FORMATTER))
                                            .receiverMails(receiverList.stream()
                                                    .map(ReportReceiver::getReceiverMail)
                                                    .map(StringUtil::toString)
                                                    .collect(Collectors.joining(",")))
                                            .build()
                                    );

                                    // 開放MIS端查詢log
                                    operateLogService.saveRecord(OperateLogSaveParam.builder()
                                            .eid(eid)
                                            .processUserId("")
                                            .processUserName(SYSTEM)
                                            .reportId(StringUtil.toString(reportRecord.getReportId()))
                                            .operateType(OperateLogType.SendMail.getCode())
                                            .startTime(DateUtil.getNowFormatString(DATE_TIME_FORMATTER))
                                            .receiverMails(receiverList.stream()
                                                    .map(ReportReceiver::getReceiverMail)
                                                    .map(StringUtil::toString)
                                                    .collect(Collectors.joining(",")))
                                            .build()
                                    );
                                }

                                // 自動寄信相關處理
                                if (auto) {
                                    Map<String, Object> map = new HashMap<>();
                                    map.put("type", reportRecord.getType());
                                    map.put("serviceCode", reportRecord.getServiceCode());
                                    map.put("orgIds", reportRecord.getOrgIds());
                                    map.put("modelVersion", reportRecord.getModelVersion());
                                    map.put("sourceId", reportRecord.getDbId());
                                    map.put("deviceId", reportRecord.getDeviceId());
                                    ReportRecordAutoSet autoSet = edrReportMapper.getReportRecordAutoSet(map);

                                    // 處理紀錄
                                    saveAutoProcessRecord(AutoProcessRecord.builder()
                                            .id(StringUtil.toString(autoSet.getId()))
                                            .eid(eid)
                                            .processUserId("")
                                            .processUserName(SYSTEM)
                                            .enable(autoSet.getStatus())
                                            .operation("SEND")
                                            .createTime(DateUtil.getDate())
                                            .build());

                                    if (reportRecord.getType() != 8) {
                                        // 發送紀錄
                                        operateLogService.saveRecord(OperateLogSaveParam.builder()
                                                .eid(StringUtil.toString(autoSet.getEid()))
                                                .processUserId(autoSet.getProcessUserId())
                                                .processUserName(autoSet.getProcessUserName())
                                                .operateType("aiops_edr_send_mail")
                                                .startTime(DateUtil.getNowFormatString(DATE_TIME_FORMATTER))
                                                .modelVersion(autoSet.getModelVersion())
                                                .autoflag(true)
                                                .reciverMail(receiverList.stream()
                                                        .map(ReportReceiver::getReceiverMail)
                                                        .map(StringUtil::toString)
                                                        .collect(Collectors.joining(",")))
                                                .build()
                                        );
                                    }
                                }
                            }, Throwable::printStackTrace
                    );
            responseBase.setCode("0");
        } catch (Exception e) {
            e.printStackTrace();
            responseBase.setCode("1");
            responseBase.setErrMsg(e.getMessage());
        }
        return responseBase;
    }

    private void sendReportMail(ReportRecord rm, List<CustomerOrgMap> ofOrgs) {
        try {
            if (rm == null) {
                return;
            }
            ReportRecordSendLog sendLog = rm.getSendLog();
            if (sendLog == null) {
                return;
            }
            List<ReportReceiver> receiverList = sendLog.getReceiverList();
            if (CollectionUtils.isEmpty(receiverList)) {
                return;
            }
            List<String> realReceivers = receiverList.stream().map(ReportReceiver::getReceiverMail)
                    .filter(StringUtils::isNotBlank).map(x -> x.split(";")).flatMap(Arrays::stream)
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(realReceivers)) {
                return;
            }
            //发送邮件
            String subject = reportMailFactoryService.getReportMailTitle(rm, ofOrgs);
            String content = reportMailFactoryService.getReportMailContent(rm, ofOrgs);
            List<MailAttachmentInfo> mailist = reportMailFactoryService.getMailAttachmentInfoList(rm, ofOrgs);
            MailSourceType mailSourceType = reportMailFactoryService.getMailSourceType(rm, ofOrgs);
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            try {
                executorService.execute(() -> mailSendService.sendEdrReportMail(rm.getServiceCode(), realReceivers,
                        subject, content, "CN".equals(connectArea) ? "zh-CN" : "zh-TW", mailist, mailSourceType));
            } finally {
                executorService.shutdown();
            }
        } catch (Exception e) {
            log.error(e.toString());
        }
    }

    /*@Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse sendReport(ReportRecord rm, List<CustomerOrgMap> ofOrgs) {
        long sid = RequestUtil.getHeaderSid();
        rm.setSid(sid);
        rm.setSendTime(new Date());
        BaseResponse res = new BaseResponse();
        if (StringUtils.isEmpty(rm.getReceivers())) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg("the receivers is empty");
            return res;
        }
        String receivers = Arrays.stream(rm.getReceivers().split(";")).filter(k-> !StringUtils.isEmpty(k)).distinct().collect(Collectors.joining(";"));
        rm.setReceivers(receivers);
        edrReportMapper.saveReportSendLog(rm);

        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String reportStartTime = sdf.format(rm.getReportStartTime());
        String reportEndTime = sdf.format(rm.getReportEndTime());

        String misAddress = misRoot + "#/info-security/endpoint-protection/spectaculars";
        String expirationDate = "未知";
        //已安装的：取安装的 尚可安装的：授权-安装
        int serversInUse = 0; //安装的
        int serversNotInUse = 0;

        int workstationsInUse = 0;
        int workstationsNotInUse = 0;

        if("CN".equals(connectArea)){
            expirationDate = "未知";
        }
        if(!CollectionUtils.isEmpty(ofOrgs)){
            CustomerOrgMap customerOrgMap = ofOrgs.get(0);
            Org org = customerOrgMap.getOrg();
            expirationDate = sdf.format(org.getExpirationDate());
            serversInUse = org.getServersInUse();
            serversNotInUse = org.getServersNotInUse();
            workstationsInUse = org.getWorkstationsInUse();
            workstationsNotInUse = org.getWorkstationsNotInUse();
        }

        String[] attachments = {""};

        Optional.ofNullable(rm.getUrl()).ifPresent(o -> {
            String fileName = String.format("【%s至%s】月資安MDR定期維護報告內容", reportStartTime, reportEndTime);
            attachments[0] = String.format("<p><span style=\"font-weight: bold;\">點擊查看：</span><a href=\"%s\">%s</a>&nbsp;&nbsp;",
                    rm.getUrl(), fileName) + "</p>\n";
            if("CN".equals(connectArea)){
                fileName = String.format("【%s至%s】月资安MDR定期维护报告內容", reportStartTime, reportEndTime);
                attachments[0] = String.format("<p><span style=\"font-weight: bold;\">点击查看：</span><a href=\"%s\">%s</a>&nbsp;&nbsp;",
                        rm.getUrl(), fileName) + "</p>\n";
            }


        });

        //发送邮件
        String subject = String.format("【鼎新數智】企業運維資安MDR維護報告_%s_%s_%s", reportStartTime, reportEndTime, rm.getCustomerName()); //默认
        if("CN".equals(connectArea)){
            subject = String.format("【鼎捷软件】企业运维资安MDR维护报告_%s_%s_%s", reportStartTime, reportEndTime, rm.getCustomerName());
        }
        String content = String.format("<p><span style=\"font-weight: bold;\">貴公司資安MDR報告已生成，報告時間區間為 <span style=\"color: blue\">【%s至%s】</span></span>", reportStartTime, reportEndTime) +
                attachments[0] +
                "<p style=\"line-height: 28px;\">" + rm.getDescription() + "</p>" +
                "<div style=\"height:20px\"></div>" +
                String.format("<p>貴公司所購買的MDR服務使用截止日期為 <span style=\"color: red\">【%s】</span>。", expirationDate) +
                String.format("<p style=\"font-size:14px;\"><span style=\"font-weight: bold;\">資安MDR目前已安裝設備數量：%s</span> 台伺服器  <span style=\"font-weight: bold;\">%s</span> 台工作站</p>", serversInUse, workstationsInUse) +
                String.format("<p style=\"font-size:14px;\"><span style=\"font-weight: bold;\">資安MDR尚可安裝設備數量：%s</span> 台伺服器  <span style=\"font-weight: bold;\">%s</span> 台工作站</p>", serversNotInUse, workstationsNotInUse) +
                "<div style=\"height:10px\"></div>" +
                String.format("<p style=\"line-height: 28px;\">詳細風險內容可登入AI智管家企業運維平臺進行查看，平台登入步驟：<a href=\"%s\"><span style=\"font-weight: bold;color: orange\">[登入/註冊服務雲管家]->[系統運維]->[智能運維]->[資訊安全]</span></a></p>", misAddress) +
                "<p style=\"background: #E7E36C;line-height: 32px;\">*有任何問題歡迎來電至 0809-081-668 或是加入鼎新數智科技系統維護官方LINE@帳號(@065vuhrb)*</p>" +
                "<tr ><td height=20px></td></tr>"; //默认
        if("CN".equals(connectArea)){
            content = String.format("<p><span style=\"font-weight: bold;\">贵公司资安MDR报告已生成，报告时间区间为 <span style=\"color: blue\">【%s至%s】</span></span>", reportStartTime, reportEndTime) +
                    attachments[0] +
                    "<p style=\"line-height: 28px;\">" + rm.getDescription() + "</p>" +
                    "<div style=\"height:20px\"></div>" +
                    String.format("<p>贵公司所购买的MDR服务使用截止日期为 <span style=\"color: red\">【%s】</span>。", expirationDate) +
                    String.format("<p style=\"font-size:14px;\"><span style=\"font-weight: bold;\">资安MDR目前已安装设备数量：%s</span> 台服务器  <span style=\"font-weight: bold;\">%s</span> 台工作站</p>", serversInUse, workstationsInUse) +
                    String.format("<p style=\"font-size:14px;\"><span style=\"font-weight: bold;\">资安MDR尚可安装设备数量：%s</span> 台服务器  <span style=\"font-weight: bold;\">%s</span> 台工作站</p>", serversNotInUse, workstationsNotInUse) +
                    "<div style=\"height:10px\"></div>" +
                    String.format("<p style=\"line-height: 28px;\">详细风险内容可登入AI智管家企业运维平台进行查看，平台登入步骤：<a href=\"%s\"><span style=\"font-weight: bold;color: orange\">[登入/注册服务云管家]->[系统运维]->[智能运维]->[资讯安全]</span></a></p>", misAddress) +
                    "<tr ><td height=20px></td></tr>"; //默认
        }
        List<String> receiverList = Arrays.stream(rm.getReceivers().split(";")).distinct().collect(Collectors.toList());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            String finalSubject = subject;
            String finalContent = content;
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    mailSendService.sendEdrReportMail(rm.getServiceCode(), receiverList, finalSubject, finalContent,"CN".equals(connectArea) ? "zh-CN" : "zh-TW");
                }
            });
        } finally {
            executorService.shutdown();
        }
        res.setCode(ResponseCode.SUCCESS.toString());
        return res;
    }*/

    @Override
    public boolean deleteReportRecord(long id) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            long sid = RequestUtil.getHeaderSid();
            executorService.execute(() -> {
                edrUtils.deleteIdxDataById(commonUtils.getIndex(EdrConst.EDR_REPORT_DATA_NEW_IDX, null), id);

                //EDR2.0報告刪除
                edrUtils.deleteEDRv2IdxDataById(EdrConst.EDRV2_ES_REPORT_IDX, id);
            });
        } catch (Exception ex) {
            log.error("deleteReportRecord", ex);
        } finally {
            executorService.shutdown();
        }
        boolean result = edrReportMapper.deleteReportRecord(id) > 0;
        if (result) {
            edrReportMapper.deleteReportRecordLog(id);
            edrReportMapper.deleteReportReceivers(id);

            //EDR2.0附件刪除
            edrReportMapper.deleteReportFiles(id);
        }
        return result;
    }

    public void updateGenerateReportData(ReportRecord reportRecord) {
        deleteGenerateReportData(reportRecord);
        generateReportData(reportRecord);
    }

    public void deleteGenerateReportData(ReportRecord reportRecord) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(() -> {
                edrUtils.deleteIdxDataById(commonUtils.getIndex(EdrConst.EDR_REPORT_DATA_NEW_IDX, null), reportRecord.getId());
            });
        } catch (Exception ex) {
            log.error("deleteReportRecord", ex);
        } finally {
            executorService.shutdown();
        }
    }

    @Override
    public boolean updateFieldValue(SecType secType, String fieldName, String oldValue, String newValue) {
        String tableName, esFieldName;
        List<String> esIdx;
        HashMap<String, Object> map = new HashMap<>();
        switch (secType) {
            case EDR:
                tableName = "edr_org";
                esFieldName = "organization";
                esIdx = Stream.of(commonUtils.getIndex(EdrConst.EVENT_IDX, RequestUtil.getEsHeaderSid(defaultSid)),
                        commonUtils.getIndex(EdrConst.EVENT_COLLECTOR_IDX, RequestUtil.getEsHeaderSid(defaultSid)),
                        commonUtils.getIndex(EdrConst.PRODUCT_IDX, RequestUtil.getEsHeaderSid(defaultSid))).collect(Collectors.toList());
                break;
            case OPENFIND:
                tableName = "openfind_org";
                esFieldName = "doMain";
                esIdx = Stream.of(commonUtils.getIndex(OfConst.SUMMARY_IDX, RequestUtil.getEsHeaderSid(defaultSid)),
                        commonUtils.getIndex(OfConst.APT_IDX, RequestUtil.getEsHeaderSid(defaultSid)),
                        commonUtils.getIndex(OfConst.VIRUS_IDX, RequestUtil.getEsHeaderSid(defaultSid)),
                        commonUtils.getIndex(OfConst.SPAM_IDX, RequestUtil.getEsHeaderSid(defaultSid))).collect(Collectors.toList());
                break;
            default:
                tableName = "edr_org";
                esFieldName = "organization";
                esIdx = Stream.of(commonUtils.getIndex(EdrConst.EVENT_IDX, RequestUtil.getEsHeaderSid(defaultSid)),
                        commonUtils.getIndex(EdrConst.EVENT_COLLECTOR_IDX, RequestUtil.getEsHeaderSid(defaultSid)),
                        commonUtils.getIndex(EdrConst.PRODUCT_IDX, RequestUtil.getEsHeaderSid(defaultSid))).collect(Collectors.toList());
                break;
        }
        map.put("tableName", tableName);
        map.put("fieldName", fieldName);
        map.put("oldValue", oldValue);
        map.put("newValue", newValue);
        boolean res = edrReportMapper.updateFieldValue(map) > 0;
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            long sid = RequestUtil.getHeaderSid();
            executorService.execute(() -> {
                esIdx.stream().forEach(idx -> {
                    edrUtils.updateIdxDataByField(idx, esFieldName, oldValue, newValue);
                });
            });
        } catch (Exception ex) {
            log.error("updateFieldValue", ex);
        } finally {
            executorService.shutdown();
        }
        return res;
    }

    private List<EdrReportData> getReportAllData(long reportRecordId) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder().
                from(0).
                query(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("reportRecordId", reportRecordId))).
                timeout(new TimeValue(60, TimeUnit.SECONDS));
        try {
            SearchResponse response = restHighLevelClient.search(new SearchRequest(commonUtils.getIndex(EdrConst.EDR_REPORT_DATA_NEW_IDX, null)).source(sourceBuilder), RequestOptions.DEFAULT);
            SearchHits hits = response.getHits();
            List<EdrReportData> reportDatas = new ArrayList<>();
            for (SearchHit hit : hits) {
                EdrReportData reportData = JSON.parseObject(hit.getSourceAsString(), EdrReportData.class);
                reportDatas.add(reportData);
            }
            return reportDatas;
        } catch (IOException ex) {
            log.error("getReportAllData", ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<EdrReportData> getReportData(long reportRecordId) {
        return getReportAllData(reportRecordId);
    }

    @Override
    public Object getReportItemData(long reportTempId, String item) {
        List<EdrReportData> reportAllData = getReportAllData(reportTempId);
        //因目前公司帆软无法使用json插件，故需要重新组装json
        return itemFactoryService.getReportItemData(item, reportAllData);
    }

    @Override
    public void convertDataToNew(int size) {
        List<ReportData> allReportData = commonUtils.getAllReportData(commonUtils.getIndex(EdrConst.EDR_REPORT_DATA_IDX, null), size, ReportData.class);
        List<EdrReportData> edrReportDataList = new ArrayList<>();
        allReportData.stream().forEach(o -> {
            EdrReportData edrReportData = new EdrReportData();
            BeanUtils.copyProperties(o, edrReportData);
            edrReportData.setEventStatistics(commonUtils.buildBaseSummary(o.getEventStatistics()));
            edrReportData.setMostTargetedDevices(commonUtils.buildBaseSummary(o.getMostTargetedDevices()));
            edrReportData.setDeviceCfDetail(commonUtils.buildBaseDetailSummary(o.getDeviceCfDetail()));
            edrReportData.setMostTargetedProcesses(commonUtils.buildBaseSummary(o.getMostTargetedProcesses()));
            edrReportData.setProcessCfDetail(commonUtils.buildBaseDetailSummary(o.getProcessCfDetail()));
            edrReportData.setCommunicationControl(commonUtils.buildBaseSummary(o.getCommunicationControl()));
            edrReportDataList.add(edrReportData);
        });
        if (CollectionUtils.isEmpty(edrReportDataList)) {
            return;
        }
        String index = commonUtils.getIndex(EdrConst.EDR_REPORT_DATA_NEW_IDX, null);
        try {
            commonUtils.saveIdxData(index, edrReportDataList);
        } catch (Exception ex) {
            log.error("saveIdxData " + index, ex);
            return;
        }
        commonUtils.refreshIndex(index);
    }

    private String getMaxCf(Set<String> cfs) {
        if (cfs.contains("Malicious")) {
            return "Malicious";
        }
        if (cfs.contains("Suspicious")) {
            return "Suspicious";
        }
        if (cfs.contains("PUP")) {
            return "PUP";
        }
        if (cfs.contains("Inconclusive")) {
            return "Inconclusive";
        }
        if (cfs.contains("Likely Safe")) {
            return "Likely Safe";
        }
        if (cfs.contains("Safe")) {
            return "Safe";
        }
        return "Inconclusive";
    }

    private RangeQueryBuilder getSeenRangeQueryBuilder(String seenField, Date startDateTime, Date endDateTime) {
        RangeQueryBuilder seenRangeQb = null;
        if (startDateTime != null && endDateTime == null) {
            String startTime = DateFormatUtils.format(startDateTime, DateUtils.DATE_TIME_FORMATTER);
            seenRangeQb = QueryBuilders.rangeQuery(seenField).from(startTime).format(DateUtils.DATE_TIME_FORMATTER).timeZone("+08:00");
        } else if (startDateTime == null && endDateTime != null) {
            String endTime = DateFormatUtils.format(endDateTime, DateUtils.DATE_TIME_FORMATTER);
            seenRangeQb = QueryBuilders.rangeQuery(seenField).to(endTime).format(DateUtils.DATE_TIME_FORMATTER).timeZone("+08:00");
        } else if (startDateTime != null && endDateTime != null) {
            String startTime = DateFormatUtils.format(startDateTime, DateUtils.DATE_TIME_FORMATTER);
            String endTime = DateFormatUtils.format(endDateTime, DateUtils.DATE_TIME_FORMATTER);
            seenRangeQb = QueryBuilders.rangeQuery(seenField).from(startTime).to(endTime).format(DateUtils.DATE_TIME_FORMATTER).timeZone("+08:00");
        }
        return seenRangeQb;
    }

    @Override
    public ResponseBase getEventList(EventQryParam eventQryParam, Long sid) {
        try {
            String[] resCols;
            String contentKeyWord;
            String idxName;
            Script script;
            if (eventQryParam.getViewType() == 1) {
                resCols = new String[]{"device"};
                contentKeyWord = "deviceLower.keyword";
                idxName = commonUtils.getIndex(EdrConst.EVENT_COLLECTOR_IDX, sid);
                script = new Script("doc['serverId'].value +'" + EdrConst.FIELD_SPLIT +
                        "'+doc['collectorEdrId'].value +'" + EdrConst.FIELD_SPLIT +
                        "'+doc['device.keyword'].value");
            } else {
                resCols = new String[]{"process"};
                contentKeyWord = "processLower.keyword";
                idxName = commonUtils.getIndex(EdrConst.EVENT_IDX, sid);
                script = new Script("doc['process.keyword'].value +'" + EdrConst.FIELD_SPLIT +
                        "'+doc['serverId'].value +'" + EdrConst.FIELD_SPLIT +
                        "'+doc['serviceCode.keyword'].value");
            }

            List<WildcardQueryBuilder> wildcardQbs = new ArrayList<>();
            List<MatchPhraseQueryBuilder> matchQbs = new ArrayList<>();
            List<TermsQueryBuilder> termsQbs = new ArrayList<>();
            List<TermsQueryBuilder> termsNotQbs = new ArrayList<>();
            List<TermsQueryBuilder> allTermsQbs = new ArrayList<>();
            List<TermsQueryBuilder> unsolvedTermsNotQbs = new ArrayList<>();
            List<RangeQueryBuilder> rangeQbs = new ArrayList<>();
            if (!StringUtils.isEmpty(eventQryParam.getContent())) {
                wildcardQbs.add(QueryBuilders.wildcardQuery(contentKeyWord, "*" + eventQryParam.getContent().toLowerCase() + "*"));
            }
            // EDR組織名稱清單
            if (!CollectionUtils.isEmpty(eventQryParam.getOrgNameList())) {
                for (String org : eventQryParam.getOrgNameList()) {
                    wildcardQbs.add(QueryBuilders.wildcardQuery("organizationLower.keyword", "*" + org.toLowerCase() + "*"));
                }
            }
            // EDR組織名稱
            if (!StringUtils.isEmpty(eventQryParam.getOrganization())) {
                wildcardQbs.add(QueryBuilders.wildcardQuery("organizationLower.keyword", "*" + eventQryParam.getOrganization().toLowerCase() + "*"));
                //termsQbs.add(QueryBuilders.termsQuery("organization.keyword", "*" + eventQryParam.getOrganization() + "*"));
            }
            // 事件ID
            if (!StringUtils.isEmpty(eventQryParam.getEventId())) {
                termsQbs.add(QueryBuilders.termsQuery("eventId", new long[]{Long.valueOf(eventQryParam.getEventId())}));
                //termsQbs.add(QueryBuilders.termsQuery("eventId.keyword", "*" + eventQryParam.getEventId() + "*"));
            }
            //客服代號，可傳可不傳，可模糊搜尋
            if (!StringUtils.isEmpty(eventQryParam.getServiceCode())) {
                if (eventQryParam.getServiceCode().length() == 8) {
                    // 表示有輸入完整客代
                    termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", eventQryParam.getServiceCode()));
                } else {
                    wildcardQbs.add(QueryBuilders.wildcardQuery("serviceCode.keyword", "*" + eventQryParam.getServiceCode() + "*"));
                }
                //termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", eventQryParam.getServiceCode()));
                //termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", "*" + eventQryParam.getServiceCode() + "*"));
            }
            // 客戶簡稱
            if (!StringUtils.isEmpty(eventQryParam.getCustomerName())) {
                // 找出客服代號
                HashMap<String, Object> map = new HashMap<>();
                map.put("CustomerName", eventQryParam.getCustomerName());
                map.put("sid", sid);
                List<String> serviceCodeList = edrKbMapper.getServiceCodeByCustomerName(map);
                if (!serviceCodeList.isEmpty()) {
                    termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", serviceCodeList));
                } else {
                    return ResponseBase.ok(new EdrEventMainGetRes(null, 0));
                }
            }
            //案件編號，由案件編號找出對應的事件Id
            if (!StringUtils.isEmpty(eventQryParam.getIssueCode())) {
                // 找出事件Id
                HashMap<String, Object> map = new HashMap<>();
                map.put("IssueCode", eventQryParam.getIssueCode());
                List<Long> eventIdList = edrKbMapper.getEventIdFromEventKb(map);
                if (!eventIdList.isEmpty()) {
                    termsQbs.add(QueryBuilders.termsQuery("eventId", eventIdList));
                } else {
                    return ResponseBase.ok(new EdrEventMainGetRes(null, 0));
                }
            }
            //未處理狀態事件查詢條件
            allTermsQbs.addAll(termsQbs);
            //事件狀態，全部的話，不做處理。已立案、暫不處理、已解決則找出對應的事件ID。未解決，則找出不為NULL的eventId，其需要用mustNot
            if (!CollectionUtils.isEmpty(eventQryParam.getEventStatus())) {
                List<TermsQueryBuilder> termsQbs_eventId = new ArrayList<>();
                final boolean[] nonUnsolved = {false};
                List<Long> termsQbsEventIdList = new ArrayList<>();
                List<Long> termsNotQbsEventIdList = new ArrayList<>();
                eventQryParam.getEventStatus().stream().forEach(x -> {
                    if (EdrEventStatus.UNSOLVED.toLowerCase().equals(x.toLowerCase())) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("IssueStatus", x);
                        termsNotQbsEventIdList.addAll(edrKbMapper.getEventIdFromEventKb(map));
                        if (!termsNotQbsEventIdList.isEmpty()) {
                            termsNotQbs.add(QueryBuilders.termsQuery("eventId", termsNotQbsEventIdList));
                            unsolvedTermsNotQbs.add(QueryBuilders.termsQuery("eventId", termsNotQbsEventIdList));
                        }
                    } else if (EdrEventStatus.SOLVED.toLowerCase().equals(x.toLowerCase()) ||
                            EdrEventStatus.CREATEISSUE.toLowerCase().equals(x.toLowerCase()) ||
                            EdrEventStatus.NOTPROCESSEDYET.toLowerCase().equals(x.toLowerCase()) ||
                            EdrEventStatus.CLOSEISSUE.toLowerCase().equals(x.toLowerCase()) ||
                            EdrEventStatus.INVALIDED.toLowerCase().equals(x.toLowerCase())) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("IssueStatus", x);
                        List<Long> dataList = edrKbMapper.getEventIdFromEventKb(map);
                        //如果是查詢 solved + invalided，需查詢作廢狀態前一筆為已完成
                        if (EdrEventStatus.SOLVED.equals(eventQryParam.getEventStatus().get(0)) &&
                                EdrEventStatus.INVALIDED.toLowerCase().equals(x.toLowerCase())) {
                            dataList = dataList.stream()
                                    .filter(eventId -> LongUtil.isNotEmpty(edrKbMapper.getSolvedEventIdFromEventKbDetail(eventId)))
                                    .collect(Collectors.toList());
                        }
                        //如果是查詢 unsolved + invalided，需查詢過去狀態不為已完成的
                        if (EdrEventStatus.UNSOLVED.equals(eventQryParam.getEventStatus().get(0)) &&
                                EdrEventStatus.INVALIDED.toLowerCase().equals(x.toLowerCase())) {
                            dataList = dataList.stream()
                                    .filter(eventId -> LongUtil.isEmpty(edrKbMapper.getSolvedEventIdFromEventKbDetail(eventId)))
                                    .collect(Collectors.toList());
                        }
                        termsQbsEventIdList.addAll(dataList);
                        nonUnsolved[0] = true;
                    }
                });
                if (!termsQbsEventIdList.isEmpty()) {
                    termsQbs_eventId.add(QueryBuilders.termsQuery("eventId", termsQbsEventIdList));
                }
                if (nonUnsolved[0] == true && termsQbs_eventId.size() <= 0) {
                    return ResponseBase.ok(new EdrEventMainGetRes(null, 0));
                } else if (nonUnsolved[0] == true && termsQbs_eventId.size() > 0) {
                    termsQbs.addAll(termsQbs_eventId);
                    unsolvedTermsNotQbs.addAll(termsQbs_eventId);
                }
            }
            //放行狀態，全部的話不做處理。已放行則找出對應的事件ID。未放行則找出部為NULL的事件ID，且其需要用mustNot
            if (!CollectionUtils.isEmpty(eventQryParam.getReleaseStatus())) {
                List<TermsQueryBuilder> termsQbs_eventId = new ArrayList<>();
                final boolean[] nonRelease = {false};
                eventQryParam.getReleaseStatus().stream().forEach(x -> {
                    if ("nonRelease".toLowerCase().equals(x.toLowerCase())) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("ReleaseStatus", x);
                        List<Long> eventIdList = edrKbMapper.getEventIdFromEventKb(map);
                        if (!eventIdList.isEmpty()) {
                            termsNotQbs.add(QueryBuilders.termsQuery("eventId", eventIdList));
                            unsolvedTermsNotQbs.add(QueryBuilders.termsQuery("eventId", eventIdList));
                        }
                    } else if ("release".toLowerCase().equals(x.toLowerCase())) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("ReleaseStatus", x);
                        List<Long> eventIdList = edrKbMapper.getEventIdFromEventKb(map);
                        nonRelease[0] = true;
                        if (!eventIdList.isEmpty()) {
                            termsQbs_eventId.add(QueryBuilders.termsQuery("eventId", eventIdList));
                            allTermsQbs.add(QueryBuilders.termsQuery("eventId", eventIdList));
                        }
                    }
                });
                if (nonRelease[0] == true && termsQbs_eventId.size() <= 0) {
                    return ResponseBase.ok(new EdrEventMainGetRes(null, 0));
                } else if (nonRelease[0] == true && termsQbs_eventId.size() > 0) {
                    termsQbs.addAll(termsQbs_eventId);
                    allTermsQbs.addAll(termsQbs_eventId);
                }
            }

            if (!CollectionUtils.isEmpty(eventQryParam.getGrades())) {
                termsQbs.add(QueryBuilders.termsQuery("classification.keyword", eventQryParam.getGrades()));
                allTermsQbs.add(QueryBuilders.termsQuery("classification.keyword", eventQryParam.getGrades()));
            }

            RangeQueryBuilder lastSeenRangeQb = getSeenRangeQueryBuilder("lastSeen", eventQryParam.getStartLastSeen(), eventQryParam.getEndLastSeen());
            RangeQueryBuilder firstSeenRangeQb = getSeenRangeQueryBuilder("firstSeen", eventQryParam.getStartFirstSeen(), eventQryParam.getEndFirstSeen());
            Optional.ofNullable(lastSeenRangeQb).ifPresent(o -> {
                rangeQbs.add(o);
            });
            Optional.ofNullable(firstSeenRangeQb).ifPresent(o -> {
                rangeQbs.add(o);
            });

            //事件數量
            int aggsTotalCount;
            if (Objects.nonNull(eventQryParam.getEventStatus().get(0)) && EdrEventStatus.UNSOLVED.equals(eventQryParam.getEventStatus().get(0))) {
                aggsTotalCount = aggsUtils.getAggsTotalCount(idxName, script, allTermsQbs, wildcardQbs, rangeQbs, unsolvedTermsNotQbs);
            } else {
                aggsTotalCount = aggsUtils.getAggsTotalCount(idxName, script, termsQbs, wildcardQbs, rangeQbs, termsNotQbs);
            }

            if (aggsTotalCount == 0) {
                return ResponseBase.ok(new EdrEventMainGetRes(null, 0));
            }
            LinkedHashMap<String, Object> aggsPages = aggsUtils.getAggsPages(idxName, script, resCols, eventQryParam.getSortFields(), termsQbs, wildcardQbs, rangeQbs, termsNotQbs,
                    eventQryParam.getPage(), eventQryParam.getSize());

            //未處理事件例外查詢
            if (Objects.nonNull(eventQryParam.getEventStatus().get(0)) && EdrEventStatus.UNSOLVED.equals(eventQryParam.getEventStatus().get(0))) {
                //設備查詢
                LinkedHashMap<String, Object> aggsPages2 = aggsUtils.getAggsPages(idxName, script, resCols, eventQryParam.getSortFields(), allTermsQbs, wildcardQbs, rangeQbs, unsolvedTermsNotQbs,
                        eventQryParam.getPage(), eventQryParam.getSize());
                aggsPages.putAll(aggsPages2);
            }

            List<EdrEventMain> edrEventMains = new ArrayList<>();
            for (String key : aggsPages.keySet()) {
                if (StringUtils.isEmpty(key)) {
                    continue;
                }
                String[] values = key.split(EdrConst.FIELD_SPLIT);
                if (values == null || values.length == 0) {
                    continue;
                }
                String name;
                EdrEventMain edrEventMain;
                List<TermsQueryBuilder> termsQueryBuilders = new ArrayList<>();
                if (Objects.nonNull(eventQryParam.getEventStatus().get(0)) && EdrEventStatus.UNSOLVED.equals(eventQryParam.getEventStatus().get(0))) {
                    termsQueryBuilders.addAll(allTermsQbs);
                } else {
                    termsQueryBuilders.addAll(termsQbs);
                }
                HashMap<String, Object> bucketMap = (HashMap) aggsPages.getOrDefault(key, new HashMap<>());
                long docCount = (long) bucketMap.getOrDefault("docCount", 0L);
                Double seenValue = (Double) bucketMap.getOrDefault("lastSeen", 0D);
                Date seenDate = new Date(seenValue.longValue());
                String serviceCode = "";
                String organization = "";
                HashMap<String, Object> data = new HashMap<>();
                if (eventQryParam.getViewType() == 1) {
                    long serverId = Long.valueOf(values[0]);
                    long collectorEdrId = Long.valueOf(values[1]);
                    name = values[2];
                    termsQueryBuilders.add(QueryBuilders.termsQuery("serverId", new long[]{serverId}));
                    termsQueryBuilders.add(QueryBuilders.termsQuery("collectorEdrId", new long[]{collectorEdrId}));
                    termsQueryBuilders.add(QueryBuilders.termsQuery("deviceLower.keyword", name.toLowerCase()));

                    data = aggsUtils.getIdxDataList(idxName, 1, 10, DeviceEventDetail.class,
                            "lastSeen", termsQueryBuilders, wildcardQbs, rangeQbs, termsNotQbs);

                    //未處理事件例外查詢
                    if (Objects.nonNull(eventQryParam.getEventStatus().get(0)) && EdrEventStatus.UNSOLVED.equals(eventQryParam.getEventStatus().get(0))) {
                        //設備查詢
                        HashMap<String, Object> data2 = aggsUtils.getIdxDataList(idxName, 1, 10, DeviceEventDetail.class,
                                "lastSeen", termsQueryBuilders, wildcardQbs, rangeQbs, unsolvedTermsNotQbs);
                        data.putAll(data2);
                    }

                    List<DeviceEventDetail> deviceEventDetails = (List<DeviceEventDetail>) data.getOrDefault("dataList", new ArrayList<>());
                    DeviceEventDetail deviceEventDetail2 = deviceEventDetails.stream().filter(x -> serverId == x.getServerId() && name.equals(x.getDevice())).findFirst().orElse(null);
                    if (!Objects.isNull(deviceEventDetail2)) {
                        serviceCode = deviceEventDetail2.getServiceCode();
                        organization = deviceEventDetail2.getOrganization();
                    }
                    edrEventMain = new EdrEventMain(key, name, docCount, seenDate, serviceCode, organization);
                } else {
                    name = values[0];
                    long serverId = Long.valueOf(values[1]);
                    String servicecode = values[2];
                    termsQueryBuilders.add(QueryBuilders.termsQuery("serverId", new long[]{serverId}));
                    termsQueryBuilders.add(QueryBuilders.termsQuery("processLower.keyword", name.toLowerCase()));
                    termsQueryBuilders.add(QueryBuilders.termsQuery("serviceCode.keyword", servicecode));
                    long eventCount = aggsUtils.getAggsCountByFieldCardinal(idxName, "eventEdrId", termsQueryBuilders, wildcardQbs, rangeQbs, termsNotQbs);
                    //edrEventMain = new EdrEventMain(key, name, eventCount, seenDate);
                    data = aggsUtils.getIdxDataList(idxName, 1, 10, ProcessEventDetail.class,
                            "lastSeen", termsQueryBuilders, wildcardQbs, rangeQbs, unsolvedTermsNotQbs);

                    //未處理事件例外查詢
                    if (Objects.nonNull(eventQryParam.getEventStatus().get(0)) && EdrEventStatus.UNSOLVED.equals(eventQryParam.getEventStatus().get(0))) {
                        //設備查詢
                        HashMap<String, Object> data2 = aggsUtils.getIdxDataList(idxName, 1, 10, DeviceEventDetail.class,
                                "lastSeen", termsQueryBuilders, wildcardQbs, rangeQbs, unsolvedTermsNotQbs);
                        data.putAll(data2);
                    }

                    List<ProcessEventDetail> processEventDetail = (List<ProcessEventDetail>) data.getOrDefault("dataList", new ArrayList<>());
                    ProcessEventDetail processEventDetail2 = processEventDetail.stream().filter(x -> name.equals(x.getProcess())).findFirst().orElse(null);
                    if (!Objects.isNull(processEventDetail2)) {
                        serviceCode = processEventDetail2.getServiceCode();
                        organization = processEventDetail2.getOrganization();
                    }

                    edrEventMain = new EdrEventMain(key, name, docCount, seenDate, serviceCode, organization);

                    HashMap<String, Object> map = new HashMap<>();
                    map.put("sid", sid);
                    map.put("processCode", name);
                    edrEventMain.setProcessKb(edrKbMapper.getProcessKb(map));
                }
                HashMap<String, Object> customerNameMap = new HashMap<>();
                customerNameMap.put("sid", sid);
                customerNameMap.put("serviceCode", serviceCode);
//                edrEventMain.setCustomerName(edrKbMapper.getCustomerName(customerNameMap));
                Tenant tenantInfo = edrKbMapper.getCustomerInfo(customerNameMap);
                if (!ObjectUtils.isEmpty(tenantInfo)) {
                    edrEventMain.setCustomerName(tenantInfo.getName());
                    edrEventMain.setCustomerFullNameCH(StringUtils.isNotBlank(tenantInfo.getCustomerFullNameCH()) ? tenantInfo.getCustomerFullNameCH() : tenantInfo.getName());
                    edrEventMain.setCustomerFullNameEN(tenantInfo.getCustomerFullNameEN());
                }
                HashMap<String, Long> aggsCountByField = aggsUtils.getAggsCountByField(idxName, "classification.keyword",
                        10, termsQueryBuilders, wildcardQbs, rangeQbs);

                Set<String> cfs = aggsCountByField.keySet();
                edrEventMain.setEventGrade(getMaxCf(cfs));
//                Object firstSeen = aggsUtils.getAggsMinValueByField(idxName, "firstSeen", termsQueryBuilders,
//                        wildcardQbs, rangeQbs);
//                edrEventMain.setFirstSeen(new Date(((Double) firstSeen).longValue()));
                edrEventMains.add(edrEventMain);
            }
            return ResponseBase.ok(new EdrEventMainGetRes(edrEventMains, aggsTotalCount));
        } catch (Exception e) {
            log.error("getEventList", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public ResponseBase getEventDetailList(EventQryParam eventQryParam, String id, Long sid) {
        try {
            String contentKeyWord;
            String idxName;
            List<WildcardQueryBuilder> wildcardQbs = new ArrayList<>();
            List<TermsQueryBuilder> termsQbs = new ArrayList<>();
            List<TermsQueryBuilder> termsNotQbs = new ArrayList<>();
            List<TermsQueryBuilder> allTermsQbs = new ArrayList<>();
            List<TermsQueryBuilder> unsolvedTermsNotQbs = new ArrayList<>();
            List<RangeQueryBuilder> rangeQbs = new ArrayList<>();
            List<String> values = new ArrayList<>();
            if (StringUtils.isNotBlank(id)) {
                values.addAll(Arrays.asList(id.split(EdrConst.FIELD_SPLIT)));
            } else {
                values.addAll(Arrays.asList(eventQryParam.getId().split(EdrConst.FIELD_SPLIT)));
            }
            if (eventQryParam.getViewType() == 1) {
                contentKeyWord = "deviceLower.keyword";
                idxName = commonUtils.getIndex(EdrConst.EVENT_COLLECTOR_IDX, sid);
                long serverId = Long.valueOf(values.get(0));
                long collectorEdrId = Long.valueOf(values.get(1));
                String device = values.get(2);
                termsQbs.add(QueryBuilders.termsQuery("serverId", new long[]{serverId}));
                termsQbs.add(QueryBuilders.termsQuery("collectorEdrId", new long[]{collectorEdrId}));
                termsQbs.add(QueryBuilders.termsQuery("deviceLower.keyword", device.toLowerCase()));
            } else {
                contentKeyWord = "processLower.keyword";
                idxName = commonUtils.getIndex(EdrConst.EVENT_IDX, sid);
                String process = values.get(0);
                long serverId = Long.valueOf(values.get(1));
                String servicecode = values.get(2);
                termsQbs.add(QueryBuilders.termsQuery("serverId", new long[]{serverId}));
                termsQbs.add(QueryBuilders.termsQuery("processLower.keyword", process.toLowerCase()));
                termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", servicecode));
            }
            if (!StringUtils.isEmpty(eventQryParam.getContent())) {
                wildcardQbs.add(QueryBuilders.wildcardQuery(contentKeyWord, "*" + eventQryParam.getContent().toLowerCase() + "*"));
            }
            //termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", eventQryParam.getServiceCode()));
            // EDR組織名稱
            if (!StringUtils.isEmpty(eventQryParam.getOrganization())) {
                wildcardQbs.add(QueryBuilders.wildcardQuery("organizationLower.keyword", "*" + eventQryParam.getOrganization().toLowerCase() + "*"));
                //termsQbs.add(QueryBuilders.termsQuery("organization.keyword", "*" + eventQryParam.getOrganization() + "*"));
            }
            // EDR組織名稱清單
            if (!CollectionUtils.isEmpty(eventQryParam.getOrgNameList())) {
                for (String org : eventQryParam.getOrgNameList()) {
                    wildcardQbs.add(QueryBuilders.wildcardQuery("organizationLower.keyword", "*" + org.toLowerCase() + "*"));
                }
            }
            // 事件ID
            if (!StringUtils.isEmpty(eventQryParam.getEventId())) {
                termsQbs.add(QueryBuilders.termsQuery("eventId", new long[]{Long.valueOf(eventQryParam.getEventId())}));
                //termsQbs.add(QueryBuilders.termsQuery("eventId.keyword", "*" + eventQryParam.getEventId() + "*"));
            }
            //客服代號，可傳可不傳，可模糊搜尋
            if (!StringUtils.isEmpty(eventQryParam.getServiceCode())) {
                if (eventQryParam.getServiceCode().length() == 8) {
                    // 表示有輸入完整客代
                    termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", eventQryParam.getServiceCode()));
                } else {
                    wildcardQbs.add(QueryBuilders.wildcardQuery("serviceCode.keyword", "*" + eventQryParam.getServiceCode() + "*"));
                }
                //termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", eventQryParam.getServiceCode()));
                //termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", "*" + eventQryParam.getServiceCode() + "*"));
            }
            // 客戶簡稱
            if (!StringUtils.isEmpty(eventQryParam.getCustomerName())) {
                // 找出客服代號
                HashMap<String, Object> map = new HashMap<>();
                map.put("sid", sid);
                map.put("CustomerName", eventQryParam.getCustomerName());
                List<String> serviceCodeList = edrKbMapper.getServiceCodeByCustomerName(map);
                if (!serviceCodeList.isEmpty()) {
                    termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", serviceCodeList));
                } else {
                    HashMap<String, Object> dataMap = new HashMap<>();
                    dataMap.put("dataList", new ArrayList<>());
                    dataMap.put("totalCount", 0);
                    return ResponseBase.ok(dataMap);
                }
            }
            //案件編號，由案件編號找出對應的事件Id
            if (!StringUtils.isEmpty(eventQryParam.getIssueCode())) {
                // 找出事件Id
                HashMap<String, Object> map = new HashMap<>();
                map.put("IssueCode", eventQryParam.getIssueCode());
                List<Long> eventIdList = edrKbMapper.getEventIdFromEventKb(map);
                if (!eventIdList.isEmpty()) {
                    termsQbs.add(QueryBuilders.termsQuery("eventId", eventIdList));
                } else {
                    HashMap<String, Object> dataMap = new HashMap<>();
                    dataMap.put("dataList", new ArrayList<>());
                    dataMap.put("totalCount", 0);
                    return ResponseBase.ok(dataMap);
                }
            }
            //未處理狀態事件查詢條件
            allTermsQbs.addAll(termsQbs);
            //事件狀態，全部的話，不做處理。已立案、暫不處理、已解決則找出對應的事件ID。未解決，則找出不為NULL的eventId，其需要用mustNot
            if (!CollectionUtils.isEmpty(eventQryParam.getEventStatus())) {
                List<TermsQueryBuilder> termsQbs_eventId = new ArrayList<>();
                final boolean[] nonUnsolved = {false};
                List<Long> termsQbsEventIdList = new ArrayList<>();
                List<Long> termsNotQbsEventIdList = new ArrayList<>();
                eventQryParam.getEventStatus().stream().forEach(x -> {
                    if (EdrEventStatus.UNSOLVED.toLowerCase().equals(x.toLowerCase())) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("IssueStatus", x);
                        termsNotQbsEventIdList.addAll(edrKbMapper.getEventIdFromEventKb(map));
                        if (!termsNotQbsEventIdList.isEmpty()) {
                            termsNotQbs.add(QueryBuilders.termsQuery("eventId", termsNotQbsEventIdList));
                            unsolvedTermsNotQbs.add(QueryBuilders.termsQuery("eventId", termsNotQbsEventIdList));
                        }
                    } else if (EdrEventStatus.SOLVED.toString().toLowerCase().equals(x.toLowerCase()) ||
                            EdrEventStatus.CREATEISSUE.toLowerCase().equals(x.toLowerCase()) ||
                            EdrEventStatus.NOTPROCESSEDYET.toLowerCase().equals(x.toLowerCase()) ||
                            EdrEventStatus.CLOSEISSUE.toLowerCase().equals(x.toLowerCase()) ||
                            EdrEventStatus.INVALIDED.toLowerCase().equals(x.toLowerCase())) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("IssueStatus", x);
                        termsQbsEventIdList.addAll(edrKbMapper.getEventIdFromEventKb(map));
                        nonUnsolved[0] = true;
                    }
                });
                if (!termsQbsEventIdList.isEmpty()) {
                    termsQbs_eventId.add(QueryBuilders.termsQuery("eventId", termsQbsEventIdList));
                }
                if (nonUnsolved[0] == true && termsQbs_eventId.size() <= 0) {
                    HashMap<String, Object> dataMap = new HashMap<>();
                    dataMap.put("dataList", new ArrayList<>());
                    dataMap.put("totalCount", 0);
                    return ResponseBase.ok(dataMap);
                } else if (nonUnsolved[0] == true && termsQbs_eventId.size() > 0) {
                    termsQbs.addAll(termsQbs_eventId);
                }
            }
            //放行狀態，全部的話不做處理。已放行則找出對應的事件ID。未放行則找出部為NULL的事件ID，且其需要用mustNot
            if (!CollectionUtils.isEmpty(eventQryParam.getReleaseStatus())) {
                List<TermsQueryBuilder> termsQbs_eventId = new ArrayList<>();
                final boolean[] nonRelease = {false};
                eventQryParam.getReleaseStatus().stream().forEach(x -> {
                    if ("nonRelease".toLowerCase().equals(x.toLowerCase())) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("ReleaseStatus", x);
                        List<Long> eventIdList = edrKbMapper.getEventIdFromEventKb(map);
                        if (!eventIdList.isEmpty()) {
                            termsNotQbs.add(QueryBuilders.termsQuery("eventId", eventIdList));
                            unsolvedTermsNotQbs.add(QueryBuilders.termsQuery("eventId", eventIdList));
                        }
                    } else if ("release".toLowerCase().equals(x.toLowerCase())) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("ReleaseStatus", x);
                        List<Long> eventIdList = edrKbMapper.getEventIdFromEventKb(map);
                        nonRelease[0] = true;
                        if (!eventIdList.isEmpty()) {
                            termsQbs_eventId.add(QueryBuilders.termsQuery("eventId", eventIdList));
                            allTermsQbs.add(QueryBuilders.termsQuery("eventId", eventIdList));
                        }
                    }
                });
                if (nonRelease[0] == true && termsQbs_eventId.size() <= 0) {
                    HashMap<String, Object> dataMap = new HashMap<>();
                    dataMap.put("dataList", new ArrayList<>());
                    dataMap.put("totalCount", 0);
                    return ResponseBase.ok(dataMap);
                } else if (nonRelease[0] == true && termsQbs_eventId.size() > 0) {
                    termsQbs.addAll(termsQbs_eventId);
                    allTermsQbs.addAll(termsQbs_eventId);
                }
            }

            if (!CollectionUtils.isEmpty(eventQryParam.getGrades())) {
                termsQbs.add(QueryBuilders.termsQuery("classification.keyword", eventQryParam.getGrades()));
                allTermsQbs.add(QueryBuilders.termsQuery("classification.keyword", eventQryParam.getGrades()));
            }
            RangeQueryBuilder lastSeenRangeQb = getSeenRangeQueryBuilder("lastSeen", eventQryParam.getStartLastSeen(), eventQryParam.getEndLastSeen());
            RangeQueryBuilder firstSeenRangeQb = getSeenRangeQueryBuilder("firstSeen", eventQryParam.getStartFirstSeen(), eventQryParam.getEndFirstSeen());
            Optional.ofNullable(lastSeenRangeQb).ifPresent(o -> {
                rangeQbs.add(o);
            });
            Optional.ofNullable(firstSeenRangeQb).ifPresent(o -> {
                rangeQbs.add(o);
            });
            String destIdx = commonUtils.getIndex(EdrConst.EVENT_DEST_IDX, sid);
            String collectIdx = commonUtils.getIndex(EdrConst.EVENT_COLLECTOR_IDX, sid);
            String eventIdx = commonUtils.getIndex(EdrConst.EVENT_IDX, sid);
            HashMap<String, Object> data = new HashMap<>();
            HashMap<String, Object> data2 = new HashMap<>();
            if (eventQryParam.getViewType() == 1) {
                data = aggsUtils.getIdxDataList(idxName, eventQryParam.getPage(), eventQryParam.getSize(), DeviceEventDetail.class,
                        "lastSeen", termsQbs, wildcardQbs, rangeQbs, termsNotQbs);

                //未處理事件例外查詢
                if (Objects.nonNull(eventQryParam.getEventStatus().get(0)) && EdrEventStatus.UNSOLVED.equals(eventQryParam.getEventStatus().get(0))) {
                    data2 = aggsUtils.getIdxDataList(idxName, eventQryParam.getPage(), eventQryParam.getSize(), DeviceEventDetail.class,
                            "lastSeen", allTermsQbs, wildcardQbs, rangeQbs, unsolvedTermsNotQbs);
                    data.putAll(data2);
                }

                List<DeviceEventDetail> deviceEventDetails = (List<DeviceEventDetail>) data.getOrDefault("dataList", new ArrayList<>());

                //事件異步處理
                List<CompletableFuture<DeviceEventDetail>> futures = new ArrayList<>();
                for (DeviceEventDetail deviceEventDetail : deviceEventDetails) {
                    CompletableFuture<DeviceEventDetail> future = CompletableFuture.supplyAsync(() -> {
                        IdsQueryBuilder idsQueryBuilder = QueryBuilders.idsQuery().addIds(deviceEventDetail.getServerId() + EdrConst.KEY_SPLIT + deviceEventDetail.getEventId());
                        List<DeviceEventDetail> idxDataByIds = commonUtils.getIdxDataByIds(eventIdx, idsQueryBuilder, DeviceEventDetail.class);
                        if (CollectionUtils.isEmpty(idxDataByIds)) {
                            return null;
                        }
                        List<String> destinations = idxDataByIds.get(0).getDestinations();
                        if (CollectionUtils.isEmpty(destinations)) {
                            return null;
                        }
                        List<TermsQueryBuilder> termsEventIdQbs = new ArrayList<>();
                        List<WildcardQueryBuilder> wildcardEventIdQbs = new ArrayList<>();
                        termsEventIdQbs.add(QueryBuilders.termsQuery("eventId", new long[]{Long.valueOf(deviceEventDetail.getEventId())}));
                        HashMap<String, Object> data3 = new HashMap<>();
                        data3 = aggsUtils.getIdxDataList(eventIdx, 1, 10, DeviceEventDetail.class,
                                "lastSeen", termsEventIdQbs, wildcardEventIdQbs, rangeQbs, termsNotQbs);
                        List<DeviceEventDetail> deviceEventDetails2 = (List<DeviceEventDetail>) data3.getOrDefault("dataList", new ArrayList<>());
                        DeviceEventDetail deviceEventDetail2 = deviceEventDetails2.stream().filter(x -> x.getEventId() == deviceEventDetail.getEventId()).findFirst().orElse(null);
                        if (!Objects.isNull(deviceEventDetail2)) {
                            deviceEventDetail.setProcessPath(deviceEventDetail2.getProcessPath());
                        }
                        deviceEventDetail.setDestCount(destinations.size());
                        deviceEventDetail.setDestinations(destinations);
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("sid", sid);
                        map.put("processCode", deviceEventDetail.getProcess());
                        map.put("serverId", deviceEventDetail.getServerId());
                        map.put("eventId", deviceEventDetail.getEventId());

                        EventKb eventKb = edrKbMapper.getEventKb(map);
                        if (Objects.nonNull(eventKb)) {
                            deviceEventDetail.setEventKb(eventKb);
                        }
                        ProcessKb processKb = edrKbMapper.getProcessKb(map);
                        if (Objects.nonNull(processKb)) {
                            deviceEventDetail.setProcessKb(processKb);
                        }
                        //第一次判斷，若為作廢則顯示前一筆案件狀態
                        if (Objects.nonNull(deviceEventDetail.getEventKb()) && EdrEventStatus.INVALIDED.equals(deviceEventDetail.getEventKb().getStatus())) {
                            EventKbDetail eventKbDetail = edrKbMapper.getEventKbDetail(map);
                            // 若過去有非作廢案件，則變更顯示案件及狀態
                            if (Objects.nonNull(eventKbDetail)) {
                                deviceEventDetail.getEventKb().setIssueCode(eventKbDetail.getIssueCode());
                                deviceEventDetail.getEventKb().setStatus(eventKbDetail.getStatus());
                            }
                        }
                        //若狀態是作廢則設為空
                        if (Objects.nonNull(deviceEventDetail.getEventKb()) && EdrEventStatus.INVALIDED.equals(deviceEventDetail.getEventKb().getStatus())) {
                            deviceEventDetail.setEventKb(null);
                        }
                        return deviceEventDetail;
                    });
                    futures.add(future);
                }
                // 等待所有異步任務完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));
                try {
                    allFutures.get(10, TimeUnit.MINUTES); //timeout等待 10min
                } catch (Exception e) {
                    return ResponseBase.error(ResponseCode.EDR_EVENTKB_DETAIL_LIST_TIMEOUT);
                }
                //最後驗證變更後，狀態是否包含在查詢條件中，若不存在則移除，數量減1
                Iterator<DeviceEventDetail> iterator = deviceEventDetails.iterator();
                while (iterator.hasNext()) {
                    DeviceEventDetail eventDetail = iterator.next();
                    boolean found = false;
                    for (String param : eventQryParam.getEventStatus()) {
                        //若查詢條件為空、EventKb為空、EventKb的狀態為空，則放行不移除
                        if (param.isEmpty() ||
                                Objects.isNull(eventDetail.getEventKb()) ||
                                Objects.isNull(eventDetail.getEventKb().getStatus())) {
                            //若查詢條件不為空且條件不為未處理且EventKb為空，則移除
                            if (!param.isEmpty() && !EdrEventStatus.UNSOLVED.equals(param) && Objects.isNull(eventDetail.getEventKb())) {
                                break;
                            }
                            found = true;
                            break;
                        }
                        //若查詢狀態條件符合EventKb狀態則表示查詢到不移除
                        if (eventDetail.getEventKb().getStatus().equals(param)) {
                            found = true;
                            break;
                        }
                    }
                    //若查詢結果為false則移除該筆eventDetail
                    if (!found) {
                        iterator.remove();
                        data.put("totalCount", IntegerUtil.objectToInteger(data.get("totalCount")) - 1);
                    }
                }
            } else {
                data = aggsUtils.getIdxDataList(idxName, eventQryParam.getPage(), eventQryParam.getSize(), ProcessEventDetail.class,
                        "lastSeen", termsQbs, wildcardQbs, rangeQbs, termsNotQbs);

                //未處理事件例外查詢
                if (Objects.nonNull(eventQryParam.getEventStatus().get(0)) && EdrEventStatus.UNSOLVED.equals(eventQryParam.getEventStatus().get(0))) {
                    data2 = aggsUtils.getIdxDataList(idxName, eventQryParam.getPage(), eventQryParam.getSize(), ProcessEventDetail.class,
                            "lastSeen", allTermsQbs, wildcardQbs, rangeQbs, unsolvedTermsNotQbs);
                    data.putAll(data2);
                }

                List<ProcessEventDetail> processEventDetails = (List<ProcessEventDetail>) data.getOrDefault("dataList", new ArrayList<>());

                //事件異步處理
                List<CompletableFuture<ProcessEventDetail>> futures = new ArrayList<>();
                for (ProcessEventDetail processEventDetail : processEventDetails) {
                    CompletableFuture<ProcessEventDetail> future = CompletableFuture.supplyAsync(() -> {
                        List<TermsQueryBuilder> termsQueryBuilders = new ArrayList<>();
                        termsQueryBuilders.add(QueryBuilders.termsQuery("eventId", new long[]{processEventDetail.getEventId()}));
                        long deviceCount = aggsUtils.getIdxDataCount(collectIdx, termsQueryBuilders, null, null);
                        processEventDetail.setDeviceCount(deviceCount);
                        processEventDetail.setDestCount(CollectionUtils.isEmpty(processEventDetail.getDestinations()) ? 0 : processEventDetail.getDestinations().size());
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("sid", sid);
                        map.put("processCode", processEventDetail.getProcess());
                        map.put("serverId", processEventDetail.getServerId());
                        map.put("eventId", processEventDetail.getEventId());

                        EventKb eventKb = edrKbMapper.getEventKb(map);
                        if (Objects.nonNull(eventKb)) {
                            processEventDetail.setEventKb(eventKb);
                        }
                        ProcessKb processKb = edrKbMapper.getProcessKb(map);
                        if (Objects.nonNull(processKb)) {
                            processEventDetail.setProcessKb(processKb);
                        }
                        //第一次判斷，若為作廢則顯示前一筆案件狀態
                        if (Objects.nonNull(processEventDetail.getEventKb()) && EdrEventStatus.INVALIDED.equals(processEventDetail.getEventKb().getStatus())) {
                            EventKbDetail eventKbDetail = edrKbMapper.getEventKbDetail(map);
                            // 若過去有非作廢案件，則變更顯示案件及狀態
                            if (Objects.nonNull(eventKbDetail)) {
                                processEventDetail.getEventKb().setIssueCode(eventKbDetail.getIssueCode());
                                processEventDetail.getEventKb().setStatus(eventKbDetail.getStatus());
                            }
                        }
                        //若狀態是作廢則設為空
                        if (Objects.nonNull(processEventDetail.getEventKb()) && EdrEventStatus.INVALIDED.equals(processEventDetail.getEventKb().getStatus())) {
                            processEventDetail.setEventKb(null);
                        }
                        return processEventDetail;
                    });
                    futures.add(future);
                }
                // 等待所有異步任務完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));
                try {
                    //timeout等待 10min
                    allFutures.get(10, TimeUnit.MINUTES);
                } catch (Exception e) {
                    return ResponseBase.error(ResponseCode.EDR_EVENTKB_DETAIL_LIST_TIMEOUT);
                }
                //最後驗證變更後，狀態是否包含在查詢條件中，若不存在則移除，數量減1
                Iterator<ProcessEventDetail> iterator = processEventDetails.iterator();
                while (iterator.hasNext()) {
                    ProcessEventDetail eventDetail = iterator.next();
                    boolean found = false;
                    for (String param : eventQryParam.getEventStatus()) {
                        //若查詢條件為空、EventKb為空、EventKb的狀態為空，則放行不移除
                        if (param.isEmpty() ||
                                Objects.isNull(eventDetail.getEventKb()) ||
                                Objects.isNull(eventDetail.getEventKb().getStatus())) {
                            //若查詢條件不為空且條件不為未處理且EventKb為空，則移除
                            if (!param.isEmpty() && !EdrEventStatus.UNSOLVED.equals(param) && Objects.isNull(eventDetail.getEventKb())) {
                                break;
                            }
                            found = true;
                            break;
                        }
                        //若查詢狀態條件符合EventKb狀態則表示查詢到不移除
                        if (eventDetail.getEventKb().getStatus().equals(param)) {
                            found = true;
                            break;
                        }
                    }
                    //若查詢結果為false則移除該筆eventDetail
                    if (!found) {
                        iterator.remove();
                        data.put("totalCount", IntegerUtil.objectToInteger(data.get("totalCount")) - 1);
                    }
                }
            }
            return ResponseBase.ok(data);
        } catch (Exception e) {
            log.error("getEventDetailList", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public ResponseBase getEventDetailListCount(EventQryParam eventQryParam) {
        Long sid = RequestUtil.getHeaderSid();

        // 異步處理多筆設備
        List<CompletableFuture<ResponseBase>> futures = new ArrayList<>();
        eventQryParam.getIds().forEach(id -> {
            CompletableFuture<ResponseBase> future = CompletableFuture.supplyAsync(() -> {
                return getEventDetailList(eventQryParam, id, sid);
            });
            futures.add(future);
        });

        // 等待所有異步任務完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));
        try {
            //timeout等待 10min
            allFutures.get(120, TimeUnit.SECONDS);
        } catch (Exception e) {
            return ResponseBase.error(ResponseCode.EDR_EVENTKB_DETAIL_COUNT_ERROR);
        }

        // 檢查是否有任務出現異常，這裡可以自訂處理方式，比如捕獲異常，記錄日誌等
        List<ResponseBase> results = new ArrayList<>();
        for (CompletableFuture<ResponseBase> future : futures) {
            try {
                ResponseBase response = future.get();  // 此處使用get()將阻塞直到任務完成，並可能拋出異常
                results.add(response);
            } catch (Exception e) {
                return ResponseBase.error(ResponseCode.EDR_EVENTKB_DETAIL_COUNT_ERROR);
            }
        }

        List<Map<String, Object>> res = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        results.forEach(result -> {
            Map<String, Object> resData = new HashMap<>();
            try {
                String jsonData = objectMapper.writeValueAsString(result.getData());
                JsonNode resultNode = objectMapper.readTree(jsonData);
                JsonNode dataListNode = resultNode.get("dataList");

                // 取第一筆 data 中的 device
                JsonNode deviceEventDetailNode = dataListNode.get(0).get("device");
                String deviceString = objectMapper.writeValueAsString(deviceEventDetailNode);

                // 取第一筆 data 中的 process
                JsonNode processEventDetailNode = dataListNode.get(0).get("process");
                String processString = objectMapper.writeValueAsString(processEventDetailNode);

                // 去掉雙引號
                deviceString = deviceString.replaceAll("\"", "");
                processString = processString.replaceAll("\"", "");

                resData.put("device", deviceString);
                resData.put("process", processString);
                resData.put("totalCount", resultNode.get("totalCount").asInt());
                res.add(resData);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        return ResponseBase.ok(res);
    }

    @Override
    public ResponseBase getEventDetailInfo(EventQryParam eventQryParam) {
        try {
            String idxName;
            List<WildcardQueryBuilder> wildcardQbs = new ArrayList<>();
            List<TermsQueryBuilder> termsQbs = new ArrayList<>();
            List<TermsQueryBuilder> termsNotQbs = new ArrayList<>();
            List<RangeQueryBuilder> rangeQbs = new ArrayList<>();

            idxName = commonUtils.getIndex(EdrConst.EVENT_COLLECTOR_IDX, RequestUtil.getHeaderSid());

            if (eventQryParam.getEventIdList().size() > 0) {
                termsQbs.add(QueryBuilders.termsQuery("eventId", eventQryParam.getEventIdList()));
            }

            if (eventQryParam.getViewType() == 1) {
                if (StringUtils.isNotEmpty(eventQryParam.getDeviceName())) {
                    termsQbs.add(QueryBuilders.termsQuery("deviceLower.keyword", eventQryParam.getDeviceName().toLowerCase()));
                }
            } else {
                if (StringUtils.isNotEmpty(eventQryParam.getProcessName())) {
                    termsQbs.add(QueryBuilders.termsQuery("processLower.keyword", eventQryParam.getProcessName().toLowerCase()));
                }
            }

            String eventIdx = commonUtils.getIndex(EdrConst.EVENT_IDX, RequestUtil.getHeaderSid());
            HashMap<String, Object> data = new HashMap<>();
            HashMap<String, Object> data2 = new HashMap<>();
            int page = 1;
            int size = 500;
            data = aggsUtils.getIdxDataList(idxName, page, size, DeviceEventDetail.class,
                    "lastSeen", termsQbs, wildcardQbs, rangeQbs, termsNotQbs);
            termsQbs.removeIf(queryBuilder -> queryBuilder.fieldName().equals("deviceLower.keyword"));
            data2 = aggsUtils.getIdxDataList(eventIdx, page, size, DeviceEventDetail.class,
                    "lastSeen", termsQbs, wildcardQbs, rangeQbs, termsNotQbs);
            log.info("getEventDetailInfo data " + size, data);
            List<DeviceEventDetail> deviceEventDetails = (List<DeviceEventDetail>) data.getOrDefault("dataList", new ArrayList<>());
            List<DeviceEventDetail> deviceEventDetails2 = (List<DeviceEventDetail>) data2.getOrDefault("dataList", new ArrayList<>());
            List<DeviceEventDetail> deviceEventDetails3 = new ArrayList<>();

            for (DeviceEventDetail deviceEventDetail : deviceEventDetails) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("sid", RequestUtil.getHeaderSid());
                map.put("processCode", deviceEventDetail.getProcess());
                map.put("serverId", deviceEventDetail.getServerId());
                map.put("eventId", deviceEventDetail.getEventId());

                EventKb eventKb = edrKbMapper.getEventKb(map);
                if (Objects.nonNull(eventKb)) {
                    deviceEventDetail.setEventKb(eventKb);
                }
                ProcessKb processKb = edrKbMapper.getProcessKb(map);
                if (Objects.nonNull(processKb)) {
                    deviceEventDetail.setProcessKb(processKb);
                }
                //第一次判斷，若為作廢則顯示前一筆案件狀態
                if (Objects.nonNull(deviceEventDetail.getEventKb()) && EdrEventStatus.INVALIDED.equals(deviceEventDetail.getEventKb().getStatus())) {
                    EventKbDetail eventKbDetail = edrKbMapper.getEventKbDetail(map);
                    // 若過去有非作廢案件，則變更顯示案件及狀態
                    if (Objects.nonNull(eventKbDetail)) {
                        deviceEventDetail.getEventKb().setIssueCode(eventKbDetail.getIssueCode());
                        deviceEventDetail.getEventKb().setStatus(eventKbDetail.getStatus());
                    }
                }
                //若狀態是作廢則設為空
                if (Objects.nonNull(deviceEventDetail.getEventKb()) && EdrEventStatus.INVALIDED.equals(deviceEventDetail.getEventKb().getStatus())) {
                    deviceEventDetail.setEventKb(null);
                }

                EdrEventRawParam param = new EdrEventRawParam();
                param.setEventId(deviceEventDetail.getEventId());
                param.setServerId(deviceEventDetail.getServerId());
                param.setOrganization(deviceEventDetail.getOrganization());
                param.setDevice(deviceEventDetail.getDevice());
                deviceEventDetail.setRawId(getLastRawId(param));
                IdsQueryBuilder idsQueryBuilder = QueryBuilders.idsQuery().addIds(deviceEventDetail.getServerId() + EdrConst.KEY_SPLIT + deviceEventDetail.getEventId());
                List<DeviceEventDetail> idxDataByIds = commonUtils.getIdxDataByIds(eventIdx, idsQueryBuilder, DeviceEventDetail.class);
                if (CollectionUtils.isEmpty(idxDataByIds)) {
                    DeviceEventDetail deviceEventDetail3 = deviceEventDetails3.stream().filter(x -> x.getEventId() == deviceEventDetail.getEventId()).findFirst().orElse(null);
                    if (Objects.isNull(deviceEventDetail3)) {
                        deviceEventDetails3.add(deviceEventDetail);
                    }
                    continue;
                }
                List<String> destinations = idxDataByIds.get(0).getDestinations();
                if (CollectionUtils.isEmpty(destinations)) {
                    DeviceEventDetail deviceEventDetail3 = deviceEventDetails3.stream().filter(x -> x.getEventId() == deviceEventDetail.getEventId()).findFirst().orElse(null);
                    if (Objects.isNull(deviceEventDetail3)) {
                        deviceEventDetails3.add(deviceEventDetail);
                    }
                    continue;
                }
                DeviceEventDetail deviceEventDetail2 = deviceEventDetails2.stream().filter(x -> x.getEventId() == deviceEventDetail.getEventId()).findFirst().orElse(null);
                if (!Objects.isNull(deviceEventDetail2)) {
                    deviceEventDetail.setProcessPath(deviceEventDetail2.getProcessPath());
                }
                deviceEventDetail.setDestCount(destinations.size());
                deviceEventDetail.setDestinations(destinations);

                DeviceEventDetail deviceEventDetail3 = deviceEventDetails3.stream().filter(x -> x.getEventId() == deviceEventDetail.getEventId()).findFirst().orElse(null);
                if (Objects.isNull(deviceEventDetail3)) {
                    deviceEventDetails3.add(deviceEventDetail);
                }
            }
            HashMap<String, Object> data3 = new HashMap<>();
            data3.put("dataList", deviceEventDetails3);
            data3.put("totalCount", deviceEventDetails3.size());

            return ResponseBase.ok(data3);
        } catch (Exception e) {
            log.error("getEventDetailInfo", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public List getEventRawDataList(EdrEventRawParam edrEventRawParam) {
        List<LinkedHashMap<String, Object>> rawDataList = edrUtils.getRawDataList(edrEventRawParam);
        for (LinkedHashMap<String, Object> map : rawDataList) {
            String destination = (String) map.getOrDefault("destination", "");
            if (StringUtils.isEmpty(destination)) {
                map.put("countryName", destination);
                continue;
            }
            boolean ipAddressByRegex = Utils.isIPAddressByRegex(destination);
            if (ipAddressByRegex) {
                map.put("countryName", GeoIpUtil.getCountryName(destination));
            } else {
                map.put("countryName", "");
            }
        }
        return rawDataList;
    }

    public String getLastRawId(EdrEventRawParam edrEventRawParam) {
        List<LinkedHashMap<String, Object>> rawDataList = edrUtils.getRawDataListNoPage(edrEventRawParam);
        if (CollectionUtils.isEmpty(rawDataList)) {
            return "";
        } else {
            /*Map<String, Object> rawMap = rawDataList.stream().sorted(new Comparator<Map<String, Object>>() {
                @Override
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    return getDateString(o2.get("lastSeen").toString()).compareTo(getDateString(o1.get("lastSeen").toString()));
                }
            }).findFirst().get();*/
            //20241118: 代码稽核 rawDataList 改为  Optional.ofNullable(rawDataList).orElse(new ArrayList<>())
            Optional<LinkedHashMap<String, Object>> rawMap = Optional.ofNullable(rawDataList).orElse(new ArrayList<>()).stream().sorted(new Comparator<Map<String, Object>>() {
                @Override
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    return getDateString(o2.get("lastSeen").toString()).compareTo(getDateString(o1.get("lastSeen").toString()));
                }
            }).findFirst();
            if(rawMap.isPresent()){
                return rawMap.get().get("rawEventId").toString();
            }
            return "";
        }
    }

    public List<Map<String, String>> getRawDataListNoPage(List<EdrEventRawParam> edrEventRawParams) {
        List<Map<String, String>> resultMap = new ArrayList<>();
        edrEventRawParams.forEach(edrEventRawParam -> {
            Map<String, String> temp = new HashMap<>();
            List<LinkedHashMap<String, Object>> rawDataList = edrUtils.getRawDataListNoPage(edrEventRawParam);
            if (CollectionUtils.isEmpty(rawDataList)) {
                temp.put("eventId", String.valueOf(edrEventRawParam.getEventId()));
                temp.put("rawId", "");
            } else {
                Map<String, Object> rawMap = rawDataList.stream().sorted(new Comparator<Map<String, Object>>() {
                    @Override
                    public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                        return getDateString(o2.get("lastSeen").toString()).compareTo(getDateString(o1.get("lastSeen").toString()));
                    }
                }).findFirst().get();
                temp.put("eventId", String.valueOf(edrEventRawParam.getEventId()));
                temp.put("rawId", rawMap.get("rawEventId").toString());
            }
            resultMap.add(temp);
        });
        return resultMap;
    }

    public String getDateString(String lastSeen) {
        if (StringUtils.isEmpty(lastSeen)) return "";
        String aaa[] = lastSeen.split(" ");
        if (aaa.length == 2) {
            String bbb[] = aaa[0].split("-");
            if (bbb.length == 3) {
                String month = "";
                if (bbb[1].equals("Des")) {
                    month = "12";
                } else if (bbb[1].equals("Nov")) {
                    month = "11";
                } else if (bbb[1].equals("Oct")) {
                    month = "10";
                } else if (bbb[1].equals("Sep")) {
                    month = "09";
                } else if (bbb[1].equals("Aug")) {
                    month = "08";
                } else if (bbb[1].equals("Jul")) {
                    month = "07";
                } else if (bbb[1].equals("Jun")) {
                    month = "06";
                } else if (bbb[1].equals("May")) {
                    month = "05";
                } else if (bbb[1].equals("Apr")) {
                    month = "04";
                } else if (bbb[1].equals("Mar")) {
                    month = "03";
                } else if (bbb[1].equals("Feb")) {
                    month = "02";
                } else if (bbb[1].equals("Jan")) {
                    month = "01";
                }
                return bbb[2] + "-" + month + "-" + bbb[0] + " " + aaa[1];
            }
        }

        return "";
    }

    @Override
    public ResponseBase checkEventIssue(List<EdrEventRawParam> edrEventRawParams) {
        //1 先获取rawId列表,每个事件，根据最近发生时间lastSeen 取最新的rawId
        List<Map<String, String>> resultMap = getRawDataListNoPage(edrEventRawParams);
        //2 根据rawId，看看是否立过案
        return issueService.checkEdrEventIssue(resultMap, RequestUtil.getHeaderToken());
    }

    @Override
    public ResponseBase getEventLatestRawId(List<EdrEventRawParam> edrEventRawParams) {
        List<Map<String, String>> resultMap = new ArrayList<>();
        try {
            // 先获取rawId列表,每个事件，根据最近发生时间lastSeen 取最新的rawId
            resultMap = getRawDataListNoPage(edrEventRawParams);
        } catch (Exception e) {
            log.error("getEventLatestRawId:" + e.toString());
        }

        return ResponseBase.ok(resultMap);
    }

    @Override
    public JSONArray getCollectorGroupList(QryParamBase qryParamBase) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("serviceCode", qryParamBase.getServiceCode());
        List<CustomerOrgMap> customerOrgMaps = edrCustomerMapper.getOrgs(map);
        JSONArray collectorGroups = new JSONArray();
        for (CustomerOrgMap customerOrgMap : customerOrgMaps) {
            Org org = customerOrgMap.getOrg();
            if (ObjectUtils.isEmpty(org)) {
                continue;
            }
            if (org.getSyncStatus() == null || org.getSyncStatus().equals(-1)) {
                continue;
            }
            JSONArray collectorGroupList = edrUtils.getCollectorGroupList(new EdrParam(customerOrgMap.getServerId(), org.getName()));
            if (!CollectionUtils.isEmpty(collectorGroupList)) {
                IntStream.range(0, collectorGroupList.size()).forEach(i -> {
                    JSONObject collectorGroup = collectorGroupList.getJSONObject(i);
                    collectorGroup.put("orgId", customerOrgMap.getOrgId());
                    collectorGroup.put("serverId", customerOrgMap.getServerId());
                });
                collectorGroups.addAll(collectorGroupList);
            }
        }
        return collectorGroups;
    }

    @Override
    public Map<String, List<Collector>> getCollectorList(String serviceCode, EdrCollectorParam edrCollectorParam) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("serviceCode", serviceCode);
        List<CustomerOrgMap> customerOrgMaps = edrCustomerMapper.getOrgs(map);
        if (CollectionUtils.isEmpty(customerOrgMaps)) {
            return null;
        }
        Map<String, List<Collector>> collectorList = new HashMap<>();
        for (CustomerOrgMap customerOrgMap : customerOrgMaps) {
            edrCollectorParam.setServerId(customerOrgMap.getServerId());
            edrCollectorParam.setOrganization(customerOrgMap.getOrg().getName());

            //判斷若查不到該組織則跳過
            Boolean checkExist = edrCustomerService.checkOrgExist(customerOrgMap.getOrg().getName());
            if (!checkExist) {
                //不存在原廠表示作廢，系統自動作廢
                if (customerOrgMap.getOrg().getSyncStatus() != null && customerOrgMap.getOrg().getSyncStatus() != -1) {
                    EdrOrgCustomerInvalidParam param = new EdrOrgCustomerInvalidParam();
                    param.setServiceCode(serviceCode);
                    param.setServerId(customerOrgMap.getServerId());
                    param.setOrgId(customerOrgMap.getOrg().getId());
                    param.setSyncStatus(-1); //-1表示作廢
                    param.setVoidReason("組織已不存在於主機，系統自動作廢");
                    param.setVoidTime(new Date());
                    param.setProcessUserName(SYSTEM);
                    edrCustomerMapper.saveCustomerInvalid(param);
                }
                continue;
            }

            List<Collector> collectors = edrUtils.getCollectorList(edrCollectorParam);
            if (CollectionUtils.isEmpty(collectors)) {
                continue;
            }
            if (edrCollectorParam.getLastSeenStart() != null) {
                collectors = collectors.stream()
                        .filter(o -> o.getLastSeenTime().after(edrCollectorParam.getLastSeenStart())).collect(Collectors.toList());
            }
            if (edrCollectorParam.getLastSeenEnd() != null) {
                collectors = collectors.stream()
                        .filter(o -> o.getLastSeenTime().before(edrCollectorParam.getLastSeenEnd())).collect(Collectors.toList());
            }
            collectors = collectors.stream()
                    .sorted(Comparator.comparing(Collector::getLastSeenTime).reversed()).collect(Collectors.toList());
            collectors = collectors.stream()
                    .sorted(Comparator.comparing(Collector::getCollectorGroupName).reversed()).collect(Collectors.toList());
            List<Long> collectIds = collectors.stream().map(o -> o.getId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collectIds)) {
                continue;
            }
            List<EdrOrgCollectorProcessRecord> processRecordLatestList = Optional.ofNullable(edrReportMapper.getProcessRecordLatestList(collectIds, customerOrgMap.getServerId()))
                    .orElseGet(() -> new ArrayList<>());
            collectors.stream().forEach(o -> {
                o.setServerId(customerOrgMap.getServerId());
                processRecordLatestList.stream()
                        .filter(x -> o.getId() == x.getCollectorId())
                        .findAny()
                        .ifPresent(x -> o.setAgentCloseTime(x.getCreateTime()));
            });
            Map<String, List<Collector>> collect = collectors.stream()
                    .collect(
                            Collectors.groupingBy(new Function<Collector, String>() {
                                @Override
                                public String apply(Collector collector) {
                                    return collector.getServerId() + "$$" + collector.getOrganization() + "$$" + collector.getCollectorGroupName();
                                }
                            }, Collectors.toList()));
            collectorList.putAll(collect);
        }
        return collectorList;
    }

    @Override
    public void operateCollector(EdrCollectorParam edrCollectorParam) {
        commonUtils.asyncRun(() -> {
            edrUtils.operateCollector(edrCollectorParam);
        });
    }

    @Override
    public List<String> getCollectorOperatingSystemList(QryParamBase qryParamBase) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("serviceCode", qryParamBase.getServiceCode());
        List<CustomerOrgMap> customerOrgMaps = edrCustomerMapper.getOrgs(map);
        List<Collector> collectors = new ArrayList<>();
        customerOrgMaps.stream().forEach(o -> {
            Org org = o.getOrg();
            if (ObjectUtils.isEmpty(org)) {
                return;
            }
            if (org.getSyncStatus() == null || org.getSyncStatus().equals(-1)) {
                return;
            }
            JSONObject orgSystemSummary = edrUtils.getOrgSystemSummary(o.getServerConf(), o.getOrg().getName());
            if (ObjectUtils.isEmpty(orgSystemSummary)) {
                return;
            }
            int workstationCollectors = orgSystemSummary.getIntValue("workstationCollectorsLicenseCapacity");
            int serverCollectors = orgSystemSummary.getIntValue("serverCollectorsLicenseCapacity");
            int iotDevices = orgSystemSummary.getIntValue("iotDevicesLicenseCapacity");
            EdrCollectorParam edrCollectorParam = new EdrCollectorParam(o.getServerId(), o.getOrg().getName(), 1, workstationCollectors + serverCollectors + iotDevices);
            List<Collector> collectorList = edrUtils.getCollectorList(edrCollectorParam);
            collectors.addAll(collectorList);
        });
        return collectors.stream().map(o -> o.getOperatingSystem()).distinct().collect(Collectors.toList());
    }

    @Override
    public ResponseBase getProductList(ProductQryParam productQryParam) {
        try {
            String[] resCols = new String[]{"idNew"};
            String contentKeyWord = "product.keyword";
            String idxName = commonUtils.getIndex(EdrConst.PRODUCT_IDX, RequestUtil.getHeaderSid());
            Script script = new Script("doc['serverId'].value +'" + EdrConst.KEY_SPLIT +
                    "'+doc['orgId'].value+'" + EdrConst.KEY_SPLIT + "'+doc['vendor.keyword'].value+'" + EdrConst.KEY_SPLIT +
                    "'+doc['product.keyword'].value");
            List<WildcardQueryBuilder> wildcardQbs = new ArrayList<>();
            List<TermsQueryBuilder> termsQbs = new ArrayList<>();
            List<RangeQueryBuilder> rangeQbs = new ArrayList<>();
            if (!StringUtils.isEmpty(productQryParam.getContent())) {
                wildcardQbs.add(QueryBuilders.wildcardQuery(contentKeyWord, "*" + productQryParam.getContent() + "*"));
            }
            if (!CollectionUtils.isEmpty(productQryParam.getVendors())) {
                termsQbs.add(QueryBuilders.termsQuery("vendor.keyword", productQryParam.getVendors()));
            }
            termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", productQryParam.getServiceCode()));
            if (!CollectionUtils.isEmpty(productQryParam.getReputations())) {
                termsQbs.add(QueryBuilders.termsQuery("recommendation.keyword", productQryParam.getReputations()));
            }
            if (!CollectionUtils.isEmpty(productQryParam.getVulnerabilities())) {
                termsQbs.add(QueryBuilders.termsQuery("severity.keyword", productQryParam.getVulnerabilities()));
            }
            RangeQueryBuilder lastConnectionRangeQb = getSeenRangeQueryBuilder("lastConnectionTime", productQryParam.getStartLastSeen(), productQryParam.getEndLastSeen());
            RangeQueryBuilder firstConnectionRangeQb = getSeenRangeQueryBuilder("firstConnectionTime", productQryParam.getStartFirstSeen(), productQryParam.getEndFirstSeen());
            Optional.ofNullable(lastConnectionRangeQb).ifPresent(o -> {
                rangeQbs.add(o);
            });
            Optional.ofNullable(firstConnectionRangeQb).ifPresent(o -> {
                rangeQbs.add(o);
            });
            BoolQueryBuilder mustQueryBuilder = commonUtils.getMustQueryBuilder(termsQbs, wildcardQbs, rangeQbs);
            HashMap<String, Object> aggTotalBucket = aggsV2Utils.getAggTotalBucket(idxName, script, mustQueryBuilder);
            int totalCount = (int) aggTotalBucket.getOrDefault("totalCount", 0);
            if (totalCount == 0) {
                return ResponseBase.ok();
            }
            List<Terms.Bucket> dataList = (List<Terms.Bucket>) aggTotalBucket.get("dataList");
            List<String> keys = dataList.stream().map(o -> o.getKeyAsString()).collect(Collectors.toList());
            termsQbs.clear();
            termsQbs.add(QueryBuilders.termsQuery("idNew.keyword", keys));
            mustQueryBuilder = commonUtils.getMustQueryBuilder(termsQbs, null, null);
            HashMap<String, Object> idxDataList = commonUtils.getIdxDataList(idxName, productQryParam.getSortFields(),
                    mustQueryBuilder, EsProduct.class, productQryParam.getPage(), productQryParam.getSize());
            List<EsProduct> esProducts = (List<EsProduct>) idxDataList.get("dataList");
            for (EsProduct esProduct : esProducts) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("sid", RequestUtil.getHeaderSid());
                map.put("productCode", esProduct.getProduct());
                String edrVendor = esProduct.getVendor();
                String vendor = edrVendor.substring(0, edrVendor.indexOf(" ("));
                map.put("vendor", vendor);
                esProduct.setProductKb(edrKbMapper.getProductKb(map));
            }
            return ResponseBase.ok(idxDataList);
        } catch (Exception e) {
            log.error("getProductList", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public ResponseBase getProductDetailList(ProductQryParam productQryParam) {
        try {
            String idxName = commonUtils.getIndex(EdrConst.PRODUCT_IDX, RequestUtil.getHeaderSid());
            List<WildcardQueryBuilder> wildcardQbs = new ArrayList<>();
            List<TermsQueryBuilder> termsQbs = new ArrayList<>();
            List<RangeQueryBuilder> rangeQbs = new ArrayList<>();
            String contentKeyWord = "product.keyword";
            String idNewKeyWord = "idNew.keyword";
            if (!StringUtils.isEmpty(productQryParam.getId())) {
                wildcardQbs.add(QueryBuilders.wildcardQuery(idNewKeyWord, productQryParam.getId() + "*"));
            }
            if (!StringUtils.isEmpty(productQryParam.getContent())) {
                wildcardQbs.add(QueryBuilders.wildcardQuery(contentKeyWord, "*" + productQryParam.getContent() + "*"));
            }
            if (!CollectionUtils.isEmpty(productQryParam.getVendors())) {
                termsQbs.add(QueryBuilders.termsQuery("vendor.keyword", productQryParam.getVendors()));
            }
            termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", productQryParam.getServiceCode()));
            if (!CollectionUtils.isEmpty(productQryParam.getReputations())) {
                termsQbs.add(QueryBuilders.termsQuery("recommendation.keyword", productQryParam.getReputations()));
            }
            if (!CollectionUtils.isEmpty(productQryParam.getVulnerabilities())) {
                termsQbs.add(QueryBuilders.termsQuery("severity.keyword", productQryParam.getVulnerabilities()));
            }
            if (!StringUtils.isEmpty(productQryParam.getPolicyName())) {
                termsQbs.add(QueryBuilders.termsQuery("decisionv2.policyName.keyword", productQryParam.getPolicyName()));
            }
            if (productQryParam.getOrgId() > 0) {
                termsQbs.add(QueryBuilders.termsQuery("orgId", new long[]{productQryParam.getOrgId()}));
            }
            RangeQueryBuilder lastConnectionRangeQb = getSeenRangeQueryBuilder("lastConnectionTime", productQryParam.getStartLastSeen(), productQryParam.getEndLastSeen());
            RangeQueryBuilder firstConnectionRangeQb = getSeenRangeQueryBuilder("firstConnectionTime", productQryParam.getStartFirstSeen(), productQryParam.getEndFirstSeen());
            Optional.ofNullable(lastConnectionRangeQb).ifPresent(o -> {
                rangeQbs.add(o);
            });
            Optional.ofNullable(firstConnectionRangeQb).ifPresent(o -> {
                rangeQbs.add(o);
            });
            BoolQueryBuilder boolQueryBuilder = commonUtils.getMustQueryBuilder(termsQbs, wildcardQbs, rangeQbs)
                    .mustNot(QueryBuilders.termQuery("version.keyword", ""));
            HashMap<String, Object> idxDataList = commonUtils.getIdxDataList(idxName, productQryParam.getSortFields(),
                    boolQueryBuilder, EsProduct.class, productQryParam.getPage(), productQryParam.getSize());
            List<EsProduct> esProducts = (List<EsProduct>) idxDataList.get("dataList");
            for (EsProduct esProduct : esProducts) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("sid", RequestUtil.getHeaderSid());
                map.put("productCode", esProduct.getProduct());
                String edrVendor = esProduct.getVendor();
                String vendor = edrVendor.substring(0, edrVendor.indexOf(" ("));
                map.put("vendor", vendor);
                map.put("productVersion", esProduct.getVersion());
                esProduct.setProductKbDetail(edrKbMapper.getProductKbDetail(map));
            }
            return ResponseBase.ok(idxDataList);
        } catch (Exception e) {
            log.error("getProductDetailList", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public boolean setProductPermission(SetPolicyPermissionParam setPolicyPermissionParam) throws Exception {
        String[] vendors = setPolicyPermissionParam.getVendors();
        String[] products = setPolicyPermissionParam.getProducts();
        String[] versions = setPolicyPermissionParam.getVersions();
        String[] policies = setPolicyPermissionParam.getPolicies();
        //修复bug把&&改为 ||
        if (vendors == null || vendors.length == 0) {
            return false;
        }
        if (products == null || products.length == 0) {
            return false;
        }
        if (versions == null || versions.length == 0) {
            return false;
        }
        if (policies == null || policies.length == 0) {
            return false;
        }
        boolean setRes = edrUtils.setPolicyPermission(setPolicyPermissionParam);
        if (!setRes) {
            return setRes;
        }
        List<String> idNews = Arrays.stream(versions)
                .map(version -> setPolicyPermissionParam.getServerId() + EdrConst.KEY_SPLIT + setPolicyPermissionParam.getOrgId() + EdrConst.KEY_SPLIT +
                        vendors[0] + (setPolicyPermissionParam.isSigned() ? " (Signed)" : " (Unsigned)") + EdrConst.KEY_SPLIT + products[0] + EdrConst.KEY_SPLIT + version)
                .collect(Collectors.toList());
        String idxName = commonUtils.getIndex(EdrConst.PRODUCT_IDX, RequestUtil.getHeaderSid());
        List<TermsQueryBuilder> termsQbs = new ArrayList<>();
        termsQbs.add(QueryBuilders.termsQuery("idNew.keyword", idNews));
        BoolQueryBuilder boolQueryBuilder = commonUtils.getMustQueryBuilder(termsQbs, null, null)
                .mustNot(QueryBuilders.termQuery("version.keyword", ""));
        HashMap<String, Object> idxDataList = commonUtils.getIdxDataList(idxName, null,
                boolQueryBuilder, EsProduct.class, 1, idNews.size());
        int totalCount = (int) idxDataList.get("totalCount");
        if (totalCount == 0) {
            return true;
        }
        List<EsProduct> esProducts = (List<EsProduct>) idxDataList.get("dataList");
        esProducts.stream().forEach(esProduct -> {
            List<String> decisions = esProduct.getDecisions();
            if (!CollectionUtils.isEmpty(decisions)) {
                List<String> newDecisions = new ArrayList<>();
                for (String decision : decisions) {
                    for (String policy : policies) {
                        if (decision.contains(policy + ":")) {
                            decision = policy + ": " + setPolicyPermissionParam.getDecision();
                            break;
                        }
                    }
                    if (!newDecisions.contains(decision)) {
                        newDecisions.add(decision);
                    }
                }
                esProduct.setDecisions(newDecisions);
            }
            List<Decision> decisionv2 = esProduct.getDecisionv2();
            if (!CollectionUtils.isEmpty(decisionv2)) {
                for (Decision decision : decisionv2) {
                    for (String policy : policies) {
                        if (decision.getPolicyName().equals(policy)) {
                            decision.setDecision(setPolicyPermissionParam.getDecision());
                        }
                    }
                }
            }
        });
        commonUtils.saveIdxData(idxName, esProducts);
        commonUtils.refreshIndex(idxName);
        return true;
    }

    @Override
    public List getProductPermissionList(QryParamBase qryParamBase) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("serviceCode", qryParamBase.getServiceCode());
        List<CustomerOrgMap> customerOrgMaps = edrCustomerMapper.getOrgs(map);
        JSONArray permissions = new JSONArray();
        String idxName = commonUtils.getIndex(EdrConst.PRODUCT_IDX, RequestUtil.getHeaderSid());
        for (CustomerOrgMap customerOrgMap : customerOrgMaps) {
            Org org = customerOrgMap.getOrg();
            if (ObjectUtils.isEmpty(org)) {
                continue;
            }
            if (org.getSyncStatus() == null || org.getSyncStatus().equals(-1)) {
                continue;
            }
            JSONArray policies = edrUtils.getPoliciesList(new EdrParam(customerOrgMap.getServerId(), org.getName()));
            IntStream.range(0, policies.size()).forEach(i -> {
                JSONObject permission = policies.getJSONObject(i);
                permission.put("orgId", customerOrgMap.getOrgId());
                permission.put("serverId", customerOrgMap.getServerId());
                List<TermsQueryBuilder> termsQbs = new ArrayList<>();
                termsQbs.add(QueryBuilders.termsQuery("serviceCode.keyword", customerOrgMap.getServiceCode()));
                termsQbs.add(QueryBuilders.termsQuery("orgId", new long[]{customerOrgMap.getOrgId()}));
                BoolQueryBuilder mustQueryBuilder = commonUtils.getMustQueryBuilder(termsQbs, null, null);

//                HashMap<String, Object> allowMap = new HashMap<>();
//                allowMap.put("policyName", permission.getString("name"));
//                allowMap.put("decision", "Allow");
//                Script allowScript = new Script(ScriptType.INLINE, "painless", "(doc['decisionv2.policyName.keyword'].value +'" + EdrConst.FIELD_SPLIT +
//                        "'+doc['decisionv2.decision.keyword'].value).equals(params.policyName+'" + EdrConst.FIELD_SPLIT + "'+params.decision)", allowMap);
//                BoolQueryBuilder allowQueryBuilder = mustQueryBuilder.filter(QueryBuilders.scriptQuery(allowScript))
//                        .mustNot(QueryBuilders.termQuery("version.keyword", ""));
                BoolQueryBuilder allowQueryBuilder = mustQueryBuilder.filter(QueryBuilders.termQuery("decisions.keyword", permission.getString("name") + ": Allow"))
                        .mustNot(QueryBuilders.termQuery("version.keyword", ""));
                long allowCount = aggsV2Utils.getIndicesDataCount(new String[]{idxName}, allowQueryBuilder);

                BoolQueryBuilder denyQueryBuilder = mustQueryBuilder.filter(QueryBuilders.termQuery("decisions.keyword", permission.getString("name") + ": Deny"))
                        .mustNot(QueryBuilders.termQuery("version.keyword", ""));
                long denyCount = aggsV2Utils.getIndicesDataCount(new String[]{idxName}, denyQueryBuilder);
                permission.put("allowCount", allowCount);
                permission.put("denyCount", denyCount);
                permission.put("totalCount", allowCount + denyCount);
            });
            if (!CollectionUtils.isEmpty(policies)) {
                permissions.addAll(policies);
            }
        }
        return permissions;
    }

    @Override
    public ResponseBase saveEventProcessRecord(List<EventRecordParam> eventRecordParamList) {
        try {
            int saveEventCount;
            if (!eventRecordParamList.isEmpty()) {
                saveEventCount = edrKbMapper.saveEventProcessRecord(eventRecordParamList);
            } else {
                saveEventCount = 0;
            }
            return ResponseBase.ok(saveEventCount);
        } catch (Exception e) {
            log.error("saveEventProcessRecord", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public List<EventProcessRecord> getEventProcessRecord(EdrEventRawParam edrEventRawParam) {
        List<EventProcessRecord> eventProcessRecordList = new ArrayList<>();
        if (!LongUtil.isEmpty(edrEventRawParam.getEventId()) && !LongUtil.isEmpty(edrEventRawParam.getServerId())) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("sid", RequestUtil.getEsHeaderSid(defaultSid));
            map.put("eventId", edrEventRawParam.getEventId());
            map.put("serverId", edrEventRawParam.getServerId());
            eventProcessRecordList = edrKbMapper.getEventProcessRecord(map);
        }
        return eventProcessRecordList;
    }

    @Override
    public ResponseBase setEventReleaseImmediately(EventReleaseParam eventReleaseParam) {
        try {
            if (!StringUtils.isEmpty(eventReleaseParam.getServiceCode()) && !StringUtils.isEmpty(eventReleaseParam.getOrganization()) && !LongUtil.isEmpty(eventReleaseParam.getEventId()) && !LongUtil.isEmpty(eventReleaseParam.getServerId())) {
                // 依ServiceCode、Organization，找出對應的配置
                // 調用edr API 做放行，調用成功則回饋放行成功，否則回饋異常
                HashMap<String, Object> map = new HashMap<>();
                map.put("serviceCode", eventReleaseParam.getServiceCode());
                map.put("organization", eventReleaseParam.getOrganization());
                map.put("serverId", eventReleaseParam.getServerId());
                map.put("sid", RequestUtil.getEsHeaderSid(defaultSid));
                CustomerOrgMap customerOrg = edrCustomerMapper.getCustomerOrgByServiceCodeAndOrganization(map);
                ServerConf serverConf = customerOrg.getServerConf();
                Org org = customerOrg.getOrg();
                if (serverConf == null || org == null)
                    return ResponseBase.error(ResponseCode.SERVER_CONF_IS_EMPTY);
                if (serverConf.getId() == 0 || org.getId() == 0)
                    return ResponseBase.error(ResponseCode.SERVER_CONF_IS_EMPTY);
                try {
                    ResponseEntity<String> res = edrUtils.eventException(customerOrg, eventReleaseParam.getEventId());
                    if (res.getStatusCode() == HttpStatus.OK) {
                        // 紀錄已放行
                        eventReleaseParam.setReleaseStatus("release");
                        eventReleaseParam.setSid(RequestUtil.getEsHeaderSid(defaultSid));
                        HashMap<String, Object> releaseMap = new HashMap<>();
                        releaseMap.put("serverId", eventReleaseParam.getServerId());
                        releaseMap.put("eventId", eventReleaseParam.getEventId());
                        releaseMap.put("sid", eventReleaseParam.getSid());
                        releaseMap.put("releaseStatus", "release");
                        releaseMap.put("releaseRemark", eventReleaseParam.getReleaseRemark());
                        Long id = edrEventMapper.getIdByEventId(releaseMap);//Long id = issueMapper.getIdByEventId(aioDBName,serverId, eventId, sid);
                        if (null == id) {
                            id = SnowFlake.getInstance().newId();
                        }
                        eventReleaseParam.setId(id);
                        releaseMap.put("id", id);
                        edrEventMapper.edrEventReleaseStatusUpdate(releaseMap);
                        return ResponseBase.ok(0);
                    } else if (res.getStatusCode() == HttpStatus.BAD_REQUEST) {
                        String errMsg = "";
                        Gson gson = new Gson();
                        JsonArray jsonArray = gson.fromJson(res.getBody(), JsonArray.class);
                        if (jsonArray != null && jsonArray.size() > 0) {
                            errMsg = jsonArray.get(0).getAsJsonObject().get("errorMessage").getAsString();
                        }
                        //return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_400, res.getBody());
                        return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_400, errMsg);
                    } else {
                        String errMsg = "";
                        Gson gson = new Gson();
                        JsonArray jsonArray = gson.fromJson(res.getBody(), JsonArray.class);
                        if (jsonArray != null && jsonArray.size() > 0) {
                            errMsg = jsonArray.get(0).getAsJsonObject().get("errorMessage").getAsString();
                        }
                        //return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_500, res.getBody());
                        return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_500, errMsg);
                    }
                } catch (Exception e) {
                    if (e instanceof HttpClientErrorException) {
                        HttpClientErrorException hcee = (HttpClientErrorException) e;
                        if (HttpStatus.BAD_REQUEST.equals(hcee.getStatusCode())) {
                            return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_400, hcee.getResponseBodyAsString());
                        } else {
                            return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_500, hcee.getResponseBodyAsString());
                        }
                    } else {
                        log.error("setEventReleaseImmediately fortonet api error", e);
                        return ResponseBase.error(e);
                    }
                    //log.error("setEventReleaseImmediately", e);
                    //return ResponseBase.error(e);
                }
                //return ResponseBase.ok(0);
            } else {
                if (StringUtils.isEmpty(eventReleaseParam.getServiceCode()) && StringUtils.isEmpty(eventReleaseParam.getOrganization()) && LongUtil.isEmpty(eventReleaseParam.getEventId()) && LongUtil.isEmpty(eventReleaseParam.getServerId())) {
                    return ResponseBase.error(ResponseCode.EVENT_INFO_IS_EMPTY);
                } else if (LongUtil.isEmpty(eventReleaseParam.getEventId()) && LongUtil.isEmpty(eventReleaseParam.getServerId())) {
                    return ResponseBase.error(ResponseCode.EVENTID_AND_SERVERID_IS_EMPTY);
                } else if (StringUtils.isEmpty(eventReleaseParam.getServiceCode())) {
                    return ResponseBase.error(ResponseCode.SERVICECODE_IS_EMPTY);
                } else if (StringUtils.isEmpty(eventReleaseParam.getOrganization())) {
                    return ResponseBase.error(ResponseCode.ORGANIZATION_IS_EMPTY);
                } else if (LongUtil.isEmpty(eventReleaseParam.getEventId())) {
                    return ResponseBase.error(ResponseCode.EVENTID_IS_EMPTY);
                } else if (LongUtil.isEmpty(eventReleaseParam.getServerId())) {
                    return ResponseBase.error(ResponseCode.SERVERID_IS_EMPTY);
                } else {
                    return ResponseBase.error(ResponseCode.SERVER_CONF_IS_EMPTY);
                }
            }
        } catch (Exception e) {
            log.error("setEventReleaseImmediately", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public ResponseBase setEventReleaseCancel(EventReleaseParam eventReleaseParam) {
        try {
            if (!StringUtils.isEmpty(eventReleaseParam.getServiceCode()) && !StringUtils.isEmpty(eventReleaseParam.getOrganization()) && !LongUtil.isEmpty(eventReleaseParam.getEventId()) && !LongUtil.isEmpty(eventReleaseParam.getServerId())) {
                // 依ServiceCode、Organization，找出對應的配置
                // 調用edr API 做放行，調用成功則回饋放行成功，否則回饋異常
                HashMap<String, Object> map = new HashMap<>();
                map.put("serviceCode", eventReleaseParam.getServiceCode());
                map.put("organization", eventReleaseParam.getOrganization());
                map.put("serverId", eventReleaseParam.getServerId());
                map.put("sid", RequestUtil.getEsHeaderSid(defaultSid));
                CustomerOrgMap customerOrg = edrCustomerMapper.getCustomerOrgByServiceCodeAndOrganization(map);
                ServerConf serverConf = customerOrg.getServerConf();
                Org org = customerOrg.getOrg();
                if (serverConf == null || org == null)
                    return ResponseBase.error(ResponseCode.SERVER_CONF_IS_EMPTY);
                if (serverConf.getId() == 0 || org.getId() == 0)
                    return ResponseBase.error(ResponseCode.SERVER_CONF_IS_EMPTY);
                try {
                    ResponseEntity<JSONArray> res = edrUtils.getEventException(customerOrg, eventReleaseParam.getEventId());
                    //   if (res.getStatusCode() == HttpStatus.OK) {
                    // 紀錄已放行
                    ResponseEntity<String> res1 = edrUtils.deleteEventException(customerOrg, res);
                    if (res1.getStatusCode() == HttpStatus.OK) {
                        eventReleaseParam.setReleaseStatus("nonRelease");
                        eventReleaseParam.setSid(RequestUtil.getEsHeaderSid(defaultSid));
                        Map<String, Object> releaseMap = new HashMap<>();
                        releaseMap.put("serverId", eventReleaseParam.getServerId());
                        releaseMap.put("eventId", eventReleaseParam.getEventId());
                        releaseMap.put("sid", eventReleaseParam.getSid());
                        releaseMap.put("releaseStatus", "nonRelease");
                        releaseMap.put("releaseRemark", eventReleaseParam.getReleaseRemark());
                        Long id = edrEventMapper.getIdByEventId(releaseMap);//Long id = issueMapper.getIdByEventId(aioDBName,serverId, eventId, sid);
                        if (null == id) {
                            id = SnowFlake.getInstance().newId();
                        }
                        eventReleaseParam.setId(id);
                        releaseMap.put("id", id);
                        edrEventMapper.edrEventReleaseStatusUpdate(releaseMap);
                        return ResponseBase.ok(0);
                    }
                    //}
                    else if (res.getStatusCode() == HttpStatus.BAD_REQUEST) {
                        String errMsg = "";
                        Gson gson = new Gson();
                        JsonArray jsonArray = gson.fromJson(res.getBody().toString(), JsonArray.class);
                        if (jsonArray != null && jsonArray.size() > 0) {
                            errMsg = jsonArray.get(0).getAsJsonObject().get("errorMessage").getAsString();
                        }
                        //return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_400, res.getBody());
                        return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_400, errMsg);
                    } else {
                        String errMsg = "";
                        Gson gson = new Gson();
                        JsonArray jsonArray = gson.fromJson(res.getBody().toString(), JsonArray.class);
                        if (jsonArray != null && jsonArray.size() > 0) {
                            errMsg = jsonArray.get(0).getAsJsonObject().get("errorMessage").getAsString();
                        }
                        //return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_500, res.getBody());
                        return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_500, errMsg);
                    }
                } catch (Exception e) {
                    if (e instanceof HttpClientErrorException) {
                        HttpClientErrorException hcee = (HttpClientErrorException) e;
                        if (HttpStatus.BAD_REQUEST.equals(hcee.getStatusCode())) {
                            return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_400, hcee.getResponseBodyAsString());
                        } else {
                            return ResponseBase.error(ResponseCode.FORTINET_API_ERROR_500, hcee.getResponseBodyAsString());
                        }
                    } else {
                        log.error("setEventReleaseImmediately fortonet api error", e);
                        return ResponseBase.error(e);
                    }
                    //log.error("setEventReleaseImmediately", e);
                    //return ResponseBase.error(e);
                }
                //return ResponseBase.ok(0);
            } else {
                if (StringUtils.isEmpty(eventReleaseParam.getServiceCode()) && StringUtils.isEmpty(eventReleaseParam.getOrganization()) && LongUtil.isEmpty(eventReleaseParam.getEventId()) && LongUtil.isEmpty(eventReleaseParam.getServerId())) {
                    return ResponseBase.error(ResponseCode.EVENT_INFO_IS_EMPTY);
                } else if (LongUtil.isEmpty(eventReleaseParam.getEventId()) && LongUtil.isEmpty(eventReleaseParam.getServerId())) {
                    return ResponseBase.error(ResponseCode.EVENTID_AND_SERVERID_IS_EMPTY);
                } else if (StringUtils.isEmpty(eventReleaseParam.getServiceCode())) {
                    return ResponseBase.error(ResponseCode.SERVICECODE_IS_EMPTY);
                } else if (StringUtils.isEmpty(eventReleaseParam.getOrganization())) {
                    return ResponseBase.error(ResponseCode.ORGANIZATION_IS_EMPTY);
                } else if (LongUtil.isEmpty(eventReleaseParam.getEventId())) {
                    return ResponseBase.error(ResponseCode.EVENTID_IS_EMPTY);
                } else if (LongUtil.isEmpty(eventReleaseParam.getServerId())) {
                    return ResponseBase.error(ResponseCode.SERVERID_IS_EMPTY);
                } else {
                    return ResponseBase.error(ResponseCode.SERVER_CONF_IS_EMPTY);
                }
            }
        } catch (Exception e) {
            log.error("setEventReleaseImmediately", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public ResponseBase setEventReleaseRecord(List<EventReleaseParam> eventReleaseParamList, boolean saveProcessRecord, boolean saveProcessRecordCreateTime) {
        try {
            int saveEventCount = 0;
            if (!eventReleaseParamList.isEmpty()) {
                List<EventRecordParam> eventRecordParamList = new ArrayList<>();
                eventReleaseParamList.stream().forEach(e -> {
                    HashMap<String, Object> releaseMap = new HashMap<>();
                    releaseMap.put("serverId", e.getServerId());
                    releaseMap.put("eventId", e.getEventId());
                    releaseMap.put("sid", e.getSid());
                    releaseMap.put("releaseStatus", e.getReleaseStatus());
                    releaseMap.put("releaseRemark", e.getReleaseRemark());
                    Long id = edrEventMapper.getIdByEventId(releaseMap);//Long id = issueMapper.getIdByEventId(aioDBName,serverId, eventId, sid);
                    if (null == id) {
                        id = SnowFlake.getInstance().newId();
                    }
                    e.setId(id);
                    if (saveProcessRecord == true) {
                        EventRecordParam eventRecordParam = new EventRecordParam();
                        eventRecordParam.setSid(e.getSid());
                        eventRecordParam.setServerId(e.getServerId());
                        eventRecordParam.setEventId(e.getEventId());
                        eventRecordParam.setProcessStatus("releaseImmediately");
                        eventRecordParam.setDescription("");
                        if (saveProcessRecordCreateTime == true) {
                            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            Date now = new Date();
                            eventRecordParam.setCreateTime(dateFormat.format(now));
                        }
                        eventRecordParamList.add(eventRecordParam);
                    }
                });
                saveEventCount = edrEventMapper.edrEventListReleaseStatusUpdate(eventReleaseParamList);
                if (saveEventCount > 0) {
                    if (saveProcessRecord == true) {
                        if (!eventRecordParamList.isEmpty()) {
                            edrKbMapper.saveEventProcessRecord(eventRecordParamList);
                        }
                    }
                }
            } else {
                return ResponseBase.ok("eventReleaseParamList is empty");
            }
            return ResponseBase.ok(saveEventCount);
        } catch (Exception e) {
            log.error("setEventReleaseRecord", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public Integer saveProcessRecord(EdrOrgCollectorProcessRecordSaveDTO dto) {
        Long sid = RequestUtil.getEsHeaderSid(defaultSid);
        Long eid = RequestUtil.getHeaderEid();
        String organization = dto.getOrganization();
        Long serverId = dto.getServerId();
        Map<String, Object> params = new HashMap<>();
        params.put("serverId", serverId);
        params.put("organization", organization);
        params.put("sid", sid);
        Long orgId = Objects.nonNull(dto.getOrgId()) ? dto.getOrgId() : edrCustomerMapper.getOrgByServerIdOrganization(params);
        dto.setOrgId(orgId);
        dto.setSid(sid);
        dto.setProcessUserId(dto.getUserId());
        dto.setProcessUserName(dto.getUserName());
        // 若eid為null或為0，則將eid設為header中的eid
        if (dto.getEid() == null || dto.getEid() == 0) {
            dto.setEid(eid);
        }
        return edrReportMapper.insertProcessRecord(dto);
    }

    @Override
    public Integer saveProcessRecords(List<EdrOrgCollectorProcessRecordSaveDTO> dtoList) {
        Long sid = RequestUtil.getEsHeaderSid(defaultSid);
        Long eid = RequestUtil.getHeaderEid();
        dtoList.forEach(dto -> {
            String organization = dto.getOrganization();
            Long serverId = dto.getServerId();
            Map<String, Object> params = new HashMap<>();
            params.put("serverId", serverId);
            params.put("organization", organization);
            params.put("sid", sid);
            Long orgId = Objects.nonNull(dto.getOrgId()) ? dto.getOrgId() : edrCustomerMapper.getOrgByServerIdOrganization(params);
            dto.setOrgId(orgId);
            dto.setSid(sid);
            dto.setProcessUserId(dto.getUserId());
            dto.setProcessUserName(dto.getUserName());
            // 若eid為null或為0，則將eid設為header中的eid
            if (dto.getEid() == null || dto.getEid() == 0) {
                dto.setEid(eid);
            }
        });
        return edrReportMapper.insertProcessRecords(dtoList);
    }

    @Override
    public ResponseBase getProcessRecord(EdrOrgCollectorProcessRecord model) {
        try {
            List<Integer> enableParam = new ArrayList<>();
            List<String> operationParam = new ArrayList<>();

            if (StringUtils.isNotEmpty(model.getOperation())) {
                // 若Operation不為空，則以逗號分隔為List
                List<String> operationList = Arrays.asList(model.getOperation().split(","));
                operationParam = operationList.stream().filter( o -> !o.contains("AGENT_")).collect(Collectors.toList());
                // 使用HashMap建立編號與狀態碼的對應
                Map<String, Integer> operationMap = new HashMap<>();
                operationMap.put("AGENT_ENABLE", 1);
                operationMap.put("AGENT_DISABLE", 0);

                enableParam = operationList.stream()
                        .filter( o -> o.contains("AGENT_"))
                        .map(operationMap::get)
                        .collect(Collectors.toList());
            }

            List<EdrOrgCollectorProcessRecord> processRecordList = edrReportMapper.getProcessRecord(model, operationParam, enableParam)
                    .stream()
                    .peek(record -> {
                        // 取得移除設備的設備名稱和IP
                        if (StringUtils.isNotEmpty(record.getOperation())
                                && StringUtils.isEmpty(record.getIP())
                                && StringUtils.isEmpty(record.getAgentName())
                                && StringUtils.isNotEmpty(record.getAgentChangeReason())
                                && Arrays.asList("REMOVE_SUCCESS", "DELETE").contains(record.getOperation())) {
                            String[] agentInfo = record.getAgentChangeReason().split(",");
                            record.setAgentName(agentInfo[0]);
                            record.setIP(agentInfo[1]);
                        }
                    })
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(processRecordList)) {
                return ResponseBase.ok(new HashMap<>());
            }

            Optional<EdrOrgCollectorProcessRecord> optionalRecord = processRecordList.stream()
                    .sorted(Comparator.comparing(EdrOrgCollectorProcessRecord::getCreateTime).reversed())
                    .filter(x -> x.getEnable() == 0)
                    .findFirst();
            // 这一行代码直接获取 object 实例，没有必要新建 record 变量，并且使用 orElse(null) 可以消除其它的判断
            EdrOrgCollectorProcessRecord record = optionalRecord.orElse(null);

            Map<String, Object> result = new HashMap<>();
            result.put("list", processRecordList);
            result.put("latest", record);
            return ResponseBase.ok(result);
        } catch (Exception ex) {
            log.error("getProcessRecord", ex);
            return ResponseBase.error(ex);
        }
        // return edrReportMapper.getProcessRecord(model,operationList);
    }

    @Override
    public EdrOrgCollectorProcessRecord getProcessRecordLatest(EdrOrgCollectorProcessRecord model) {
        return edrReportMapper.getProcessRecordLatest(model);
    }

    @Override
    public BaseResponse setCollectorGroup(EdrCollectorSetGroupParam dto) {

        // region 參數檢查
        //確認傳入資料
        Optional<BaseResponse> optResponse = checkParamIsEmpty(dto, "EdrCollectorSetGroupParam");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        // endregion

        //實際移動設備
        Boolean isSuccess = edrUtils.moveCollector(dto);
        if (!isSuccess) {
            return BaseResponse.dynamicError("serverConf NotFound", ResponseCode.UPDATE_FAILD);
        }

        //操作紀錄Log
        dto.getOriginalCollectorGroupList().stream().forEach(originGroup -> {
            originGroup.getCollectorIds().stream().forEach(collectorId -> {
                EdrOrgCollectorProcessRecordSaveDTO edrOrgCollectorProcessRecordSaveDTO = new EdrOrgCollectorProcessRecordSaveDTO();
                Long eid = Optional.ofNullable(LongUtil.objectToLong(dto.getEid())).orElse(RequestUtil.getHeaderEid());
                edrOrgCollectorProcessRecordSaveDTO.setEid(eid);
                edrOrgCollectorProcessRecordSaveDTO.setServerId(dto.getServerId());
                edrOrgCollectorProcessRecordSaveDTO.setOrganization(dto.getTargetOrganization());
                edrOrgCollectorProcessRecordSaveDTO.setCollectorId(collectorId);
                edrOrgCollectorProcessRecordSaveDTO.setUserId(dto.getUserId());
                edrOrgCollectorProcessRecordSaveDTO.setUserName(dto.getUserName());
                edrOrgCollectorProcessRecordSaveDTO.setOperation("MOVE");
                edrOrgCollectorProcessRecordSaveDTO.setOriginalGroup(originGroup.getOriginalGroup());
                edrOrgCollectorProcessRecordSaveDTO.setCurrentGroup(dto.getTargetCollectorGroup());
                saveProcessRecord(edrOrgCollectorProcessRecordSaveDTO);
            });
        });

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse deleteCollectorGroup(EdrCollectorDeleteParam dto) {

        // region 參數檢查
        //確認傳入資料
        Optional<BaseResponse> optResponse = checkParamIsEmpty(dto, "EdrCollectorDeleteParam");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        // endregion

        //檢查設備離線是否超過30天
        Date now = new Date();
        Long days = (now.getTime() - dto.getLastSeenStart().getTime()) / (1000 * 60 * 60 * 24);
        if (days < 30) {
            return BaseResponse.dynamicError("Only devices that have been offline for more than 30 days can be deleted.", ResponseCode.DELETE_FAILD);
        }

        //實際刪除設備
        Boolean isSuccess = edrUtils.deleteCollector(dto);
        if (!isSuccess) {
            return BaseResponse.dynamicError("No Collectors were found to delete.", ResponseCode.DELETE_FAILD);
        }

        //操作紀錄Log
        EdrOrgCollectorProcessRecordSaveDTO edrOrgCollectorProcessRecordSaveDTO = new EdrOrgCollectorProcessRecordSaveDTO();
        Long eid = Optional.ofNullable(LongUtil.objectToLong(dto.getEid())).orElse(RequestUtil.getHeaderEid());
        edrOrgCollectorProcessRecordSaveDTO.setEid(eid);
        edrOrgCollectorProcessRecordSaveDTO.setServerId(dto.getServerId());
        edrOrgCollectorProcessRecordSaveDTO.setOrganization(dto.getOrganization());
        edrOrgCollectorProcessRecordSaveDTO.setCollectorId(dto.getDevicesid());
        edrOrgCollectorProcessRecordSaveDTO.setUserId(dto.getUserId());
        edrOrgCollectorProcessRecordSaveDTO.setUserName(dto.getUserName());
        edrOrgCollectorProcessRecordSaveDTO.setOperation("DELETE");
        edrOrgCollectorProcessRecordSaveDTO.setOriginalGroup(dto.getOriginalGroup());
        edrOrgCollectorProcessRecordSaveDTO.setDeleteReason(dto.getDeleteReason());
        saveProcessRecord(edrOrgCollectorProcessRecordSaveDTO);

        return BaseResponse.ok();
    }

    @Override
    public void exportExcel(HttpServletResponse response, EventQryParam eventQryParam, Long sid) throws Exception {
        //儲存主事件
        List<EdrEventMain> data = new ArrayList<EdrEventMain>();
        String area = eventQryParam.getArea();
        //储存Excel的橫列資料
        List<Object> excelList = new ArrayList<>();
        //Excel的標題
        List<String> headerList = new ArrayList<>(Arrays.asList(
                "設備名稱", //0
                "事件數量", //1
                "事件等級", //2
                "最近發生時間", //3
                "程序說明", //4
                "事件ID", //5
                "應用程序", //6
                "應用程序路徑", //7
                "事件等級", //8
                "目的行為", //9
                "首次發生時間", //10
                "最近發生時間", //11
                "事件說明", //12
                "事件狀態", //13
                "放行狀態", //14
                "最近案件代號" //15
        ));
        Boolean isDevice;

        ExecutorService executor = Executors.newFixedThreadPool(4);

        /*
        for (int i = startPage; i <= endPage; i++) {
            eventQryParam.setPage(i);   //從開始到結束的每一頁都要取
            ResponseBase temp = getEventList(eventQryParam);
            EdrEventMainGetRes edrEventMainGetRes = (EdrEventMainGetRes)temp.getData();
            data.addAll(edrEventMainGetRes.getEdrEventMains());
        }
         */

        List<Future<List<EdrEventMain>>> futuresEdrEventMain = new ArrayList<>();  // 用于存储多线程的结果

        // 提交多线程任务
        IntStream.rangeClosed(eventQryParam.getPage(), eventQryParam.getPageEndNum()).forEach(i -> {
            futuresEdrEventMain.add(executor.submit(() -> {
                EventQryParam paramCopy = (EventQryParam) eventQryParam.clone();
                paramCopy.setPage(i);
                ResponseBase temp = getEventList(paramCopy, sid);
                EdrEventMainGetRes edrEventMainGetRes = (EdrEventMainGetRes) temp.getData();
                return edrEventMainGetRes.getEdrEventMains();  // 返回当前页的数据
            }));
        });

        // 收集所有线程的执行结果
        futuresEdrEventMain.forEach(future -> {
            try {
                data.addAll(future.get()); // 获取任务执行结果
            } catch (Exception e) { //20241119:代码稽核 InterruptedException | ExecutionException e 改为 Exception e
                throw new RuntimeException(e);
            }

        });

        if (eventQryParam.getViewType() == 1) {
            isDevice = true;
        } else if (eventQryParam.getViewType() == 2) {
            isDevice = false;
            headerList.set(0, "應用程序");
            headerList.add(10, "發生設備數量");
        } else {
            throw new RuntimeException("ViewType不正確");
        }

        if ("CN".equals(area)) {
            headerList = headerList.stream()
                    .map(ZhConverterUtil::toSimple)
                    .collect(Collectors.toList());
        }

        //設定查詢子事件的參數
        eventQryParam.setPage(1);
        eventQryParam.setSize(9999);

        List<Future<List<Object>>> futures = new ArrayList<>();
        int batchSize = (int) Math.ceil(data.size() / 4.0);

        for (int i = 0; i < data.size(); i += batchSize) {
            List<EdrEventMain> edrEventMains = data.subList(i, Math.min(i + batchSize, data.size()));
            Future<List<Object>> future = executor.submit(() -> getExcelFromMainEvent(area, isDevice, edrEventMains, eventQryParam, sid));
            futures.add(future);
        }

        futures.forEach(future -> {
            try {
                excelList.addAll(future.get());
            } catch (Exception e) {//20241119:代码稽核 InterruptedException | ExecutionException e 改为 Exception e
                throw new RuntimeException(e);
            }
        });
        executor.shutdown();

        SimpleWriteData simpleWriteData = new SimpleWriteData();
        Class clazz = null;
        simpleWriteData.setDataList(excelList);
        if (isDevice) {
            simpleWriteData.setFileName("TW".equals(area) ? "設備視圖" : "设备视图");
            clazz = DeviceExcel.class;
        } else {
            simpleWriteData.setFileName("TW".equals(area) ? "應用程序視圖" : "应用程序视图");
            clazz = ApplicationExcel.class;
        }

        EasyExcelUtil easyExcelUtil = new EasyExcelUtil();
        easyExcelUtil.simpleHeaderWrite(simpleWriteData, clazz, headerList.toArray(new String[0]), response);
    }

    private List<Object> getExcelFromMainEvent(String area, Boolean isDevice, List<EdrEventMain> edrEventMains, EventQryParam eventQryParam, Long sid) {
        List<Object> excelList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (isDevice) {
            edrEventMains.forEach(edrEventMain -> {
                ResponseBase<Map> detailList = getEventDetailList(eventQryParam, edrEventMain.getId(), sid);
                //取得子事件
                List<DeviceEventDetail> detail = (List) detailList.getData().get("dataList");
                detail.forEach(deviceEventDetail -> {
                    DeviceExcel deviceExcel = new DeviceExcel();
                    deviceExcel.setMainName(edrEventMain.getName());
                    deviceExcel.setEventCount(edrEventMain.getEventCount());
                    deviceExcel.setMainEventLevel(translate(edrEventMain.getEventGrade(), area));
                    deviceExcel.setMainLastSeen(dateFormat.format(edrEventMain.getLastSeen()));
                    deviceExcel.setEventId(deviceEventDetail.getEventId());
                    deviceExcel.setSubName(deviceEventDetail.getProcess());
                    deviceExcel.setProcessPath(deviceEventDetail.getProcessPath());
                    deviceExcel.setSubEventLevel(translate(deviceEventDetail.getClassification(), area));
                    deviceExcel.setDestinations(deviceEventDetail.getDestinations().size() + " destinations");
                    deviceExcel.setSubFirstSeen(dateFormat.format(deviceEventDetail.getFirstSeen()));
                    deviceExcel.setSubLastSeen(dateFormat.format(deviceEventDetail.getLastSeen()));

                    deviceExcel.setMainDesc("");
                    if (Objects.nonNull(deviceEventDetail.getProcessKb())) {
                        deviceExcel.setMainDesc(deviceEventDetail.getProcessKb().getProcessDesc());
                    }

                    deviceExcel.setSubDesc("");
                    deviceExcel.setSubEventStatus("－");
                    deviceExcel.setSubRelease(translate("nonRelease", area));
                    deviceExcel.setIssuesCode("");

                    if (Objects.nonNull(deviceEventDetail.getEventKb())) {
                        deviceExcel.setSubDesc(
                                Optional.ofNullable(
                                        deviceEventDetail.getEventKb().getEventDesc()
                                ).orElse(""));

                        deviceExcel.setSubEventStatus(
                                translate(Optional.ofNullable(
                                                deviceEventDetail.getEventKb().getStatus())
                                        .orElse("－"), area));

                        deviceExcel.setSubRelease(
                                translate(Optional.ofNullable(
                                                deviceEventDetail.getEventKb().getReleaseStatus())
                                        .orElse(translate("nonRelease", area)), area));

                        deviceExcel.setIssuesCode(
                                Optional.ofNullable(
                                        deviceEventDetail.getEventKb().getIssueCode()
                                ).orElse(""));
                    }

                    excelList.add(deviceExcel);
                });
            });
        } else {
            edrEventMains.forEach(edrEventMain -> {
                ResponseBase<Map> detailList = getEventDetailList(eventQryParam, edrEventMain.getId(), sid);
                List<ProcessEventDetail> detail = (List) detailList.getData().get("dataList");
                detail.forEach(deviceEventDetail -> {
                    ApplicationExcel applicationExcel = new ApplicationExcel();
                    applicationExcel.setMainName(edrEventMain.getName());
                    applicationExcel.setEventCount(edrEventMain.getEventCount());
                    applicationExcel.setMainEventLevel(translate(edrEventMain.getEventGrade(), area));
                    applicationExcel.setMainLastSeen(dateFormat.format(edrEventMain.getLastSeen()));
                    applicationExcel.setEventId(deviceEventDetail.getEventId());
                    applicationExcel.setSubName(deviceEventDetail.getProcess());
                    applicationExcel.setSubProcessPath(deviceEventDetail.getProcessPath());
                    applicationExcel.setSubEventLevel(translate(deviceEventDetail.getClassification(), area));
                    applicationExcel.setDestinations(deviceEventDetail.getDestinations().size() + " destinations");
                    applicationExcel.setDeviceCount(deviceEventDetail.getDeviceCount());
                    applicationExcel.setSubFirstSeen(dateFormat.format(deviceEventDetail.getFirstSeen()));
                    applicationExcel.setSubLastSeen(dateFormat.format(deviceEventDetail.getLastSeen()));

                    applicationExcel.setMainProcessDesc("");
                    if (Objects.nonNull(deviceEventDetail.getProcessKb())) {
                        applicationExcel.setMainProcessDesc(deviceEventDetail.getProcessKb().getProcessDesc());
                    }

                    applicationExcel.setSubEventDesc("");
                    applicationExcel.setSubEventStatus("－");
                    applicationExcel.setReleaseStatus(translate("nonRelease", area));
                    applicationExcel.setIssueCode("");

                    if (Objects.nonNull(deviceEventDetail.getEventKb())) {
                        applicationExcel.setSubEventDesc(
                                Optional.ofNullable(
                                                deviceEventDetail.getEventKb().getEventDesc())
                                        .orElse(""));

                        applicationExcel.setSubEventStatus(
                                translate(
                                        Optional.ofNullable(deviceEventDetail.getEventKb().getStatus())
                                                .orElse("－"), area));

                        applicationExcel.setReleaseStatus(
                                translate(
                                        Optional.ofNullable(
                                                        deviceEventDetail.getEventKb().getReleaseStatus())
                                                .orElse(translate("nonRelease", area)), area));

                        applicationExcel.setIssueCode(
                                Optional.ofNullable(
                                                deviceEventDetail.getEventKb().getIssueCode())
                                        .orElse(""));

                    }

                    excelList.add(applicationExcel);
                });
            });
        }

        return excelList;
    }

    private String translate(String English, String area) {
        switch (English) {
            //未判斷的為簡繁相同
            case "Malicious":
                return "TW".equals(area) ? "惡意的程序" : "恶意的程序";
            case "Suspicious":
                return "可疑的程序";
            case "PUP":
                return "TW".equals(area) ? "潛在危險性" : "潜在危险性";
            case "Inconclusive":
                return "TW".equals(area) ? "無明確危害" : "无明确危害";
            case "Likely Safe":
                return "TW".equals(area) ? "看起來安全" : "看起来安全";
            case "Safe":
                return "安全的程序";
            case "unsolved":
                return "－";
            case "solved":
                return "TW".equals(area) ? "已解決" : "已解决";
            case "createIssue":
                return "已立案";
            case "notProcessedYet":
                return "TW".equals(area) ? "暫不處理" : "暂不处理";
            case "release":
                return "已放行";
            case "nonRelease":
                return "未放行";
            default:
                return English;
        }
    }


    @Override
    public BaseResponse saveAutoProcessRecord(AutoProcessRecord autoProcessRecord) {
        // region 參數檢查
        Map<String, Optional<BaseResponse>> paramCheckMap = new LinkedHashMap<>();
        paramCheckMap.put("id", checkParamIsEmpty(autoProcessRecord.getId(), "id"));
        paramCheckMap.put("eid", checkParamIsEmpty(autoProcessRecord.getEid(), "eid"));
        paramCheckMap.put("processUserName", checkParamIsEmpty(autoProcessRecord.getProcessUserName(), "processUserName"));
        paramCheckMap.put("enable", checkParamIsEmpty(autoProcessRecord.getEnable(), "enable"));
        paramCheckMap.put("operation", checkParamIsEmpty(autoProcessRecord.getOperation(), "operation"));

        // 處理有哪些為空
        List<String> emptyParam = paramCheckMap.entrySet().stream()
                .filter(entry -> entry.getValue().isPresent())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(emptyParam)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, String.join(",", emptyParam));
        }
        // endregion

        // 執行保存
        if (!(edrReportMapper.saveAutoProcessRecord(autoProcessRecord) > 0)) {
            return BaseResponse.error(ResponseCode.INSERT_FAILD);
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getAutoProcessRecord(String eid, String errasId) {
        // region 參數檢查
        Map<String, Optional<BaseResponse>> paramCheckMap = new LinkedHashMap<>();
        paramCheckMap.put("id", checkParamIsEmpty(errasId, "id"));
        paramCheckMap.put("eid", checkParamIsEmpty(eid, "eid"));

        // 處理有哪些為空
        List<String> emptyParam = paramCheckMap.entrySet().stream()
                .filter(entry -> entry.getValue().isPresent())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(emptyParam)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, String.join(",", emptyParam));
        }
        // endregion

        Boolean isMis = isMis();
        String area = connectArea;

        return BaseResponse.ok(edrReportMapper.getAutoProcessRecordList(eid, errasId, isMis, area));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse autoSetDel(String id) {
        // region 參數檢查
        Optional<BaseResponse> opt = checkParamIsEmpty(id, "id");
        if (opt.isPresent()) {
            return opt.get();
        }
        // endregion

        // 刪除自動寄信排程
        Boolean isSuccess = edrReportMapper.autoSetDel(id) > 0;
        if (!isSuccess) {
            return BaseResponse.error(ResponseCode.DELETE_FAILD);
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getAutoMailSendList(AutoMailSendListParams autoMailSendListParams) {
        List<AutoMailRecords> resultList = Collections.emptyList();
        long totalCount = 0L;

        autoMailSendListParams.setIsMis(isMis());
        log.info("isMis: {}", isMis());
        autoMailSendListParams.setArea(connectArea);

        // 分頁查詢
        if (autoMailSendListParams.getPage() != null) {
            Page<AutoMailRecords> page = PageHelper.startPage(autoMailSendListParams.getPage(), autoMailSendListParams.getSize());
            edrReportMapper.getAutoMailSendList(autoMailSendListParams);
            resultList = page.getResult();
            totalCount = page.getTotal();
        } else {
            resultList = edrReportMapper.getAutoMailSendList(autoMailSendListParams);
            totalCount = resultList.size();
        }

        return BaseResponse.ok(new AutoMailSendList(resultList, totalCount));
    }

    private Boolean isMis() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // 預防非內部人員看到內部人員姓名
        if (Objects.isNull(requestAttributes)) {
            return true;
        }

        String referer = requestAttributes.getRequest().getHeader("Referer");
        // 預防非內部人員看到內部人員姓名
        if (StringUtils.isEmpty(referer)) {
            return true;
        }

        try {
            URI refererUri = new URI(referer);
            URI misRootUri = new URI(misRoot);

            // 提取網域
            String refererHost = refererUri.getHost();
            String misHost = misRootUri.getHost();

            log.info("Referer Host: {}", refererHost);
            log.info("MIS Host: {}", misHost);

            return misHost.equals(refererHost);
        } catch (URISyntaxException e) {
            log.error("Invalid URL:", e);
            return true;
        }
    }

    private void saveReportFiles(ReportRecord reportRecord) {
        Optional.ofNullable(reportRecord.getFiles()).ifPresent(o -> {
            if (o.size() <= 0) {
                return;
            }

            List<ReportFile> reportFiles = o.stream().peek(file -> {
                file.setId(SnowFlake.getInstance().newId());
                file.setSid(reportRecord.getSid());
                file.setErrId(reportRecord.getId());
            }).collect(Collectors.toList());

            edrReportMapper.saveReportFile(reportFiles);
        });
    }

    private void removeReportFiles(ReportRecord reportRecord) {
        Optional.ofNullable(reportRecord.getRemoveFiles()).ifPresent(o -> {
            if (o.size() <= 0) {
                return;
            }
            edrReportMapper.deleteReportFilesByIds(o.stream().map(ReportFile::getFileId).collect(Collectors.toList()));
        });
    }
}
