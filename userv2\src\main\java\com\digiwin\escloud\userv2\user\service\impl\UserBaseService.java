package com.digiwin.escloud.userv2.user.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.digiwin.escloud.aiouser.model.user.CountingUser;
import com.digiwin.escloud.common.constant.AioPlatformEnum;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.util.*;
import com.digiwin.escloud.integration.api.iam.req.manager.role.RoleAddOrUpdate;
import com.digiwin.escloud.integration.api.iam.req.tenant.*;
import com.digiwin.escloud.integration.api.iam.req.user.*;
import com.digiwin.escloud.integration.common.IamBaseResponse;
import com.digiwin.escloud.integration.service.IamService;
import com.digiwin.escloud.integration.service.OmcService;
import com.digiwin.escloud.integration.service.iam.PermissionCopyService;
import com.digiwin.escloud.integration.service.iam.RoleServiceImpl;
import com.digiwin.escloud.integration.service.iam.integrate.SyncIamAccountServiceHelp;
import com.digiwin.escloud.userv2.cache.SupplierCache;
import com.digiwin.escloud.userv2.cache.TenantCache;
import com.digiwin.escloud.userv2.cache.UserCache;
import com.digiwin.escloud.userv2.constant.UserV2Const;
import com.digiwin.escloud.userv2.model.IamAccount;
import com.digiwin.escloud.userv2.supplier.dao.ISupplierDao;
import com.digiwin.escloud.userv2.supplier.model.Supplier;
import com.digiwin.escloud.userv2.supplier.model.SupplierCustomerMap;
import com.digiwin.escloud.userv2.tenant.dao.ICustomerDao;
import com.digiwin.escloud.userv2.tenant.dao.ITenantDao;
import com.digiwin.escloud.userv2.tenant.model.*;
import com.digiwin.escloud.userv2.tenant.model.CustomerServiceInfo;
import com.digiwin.escloud.userv2.user.dao.IIamUserDao;
import com.digiwin.escloud.userv2.user.dao.IUserDao;
import com.digiwin.escloud.userv2.user.dao.IUserProductDao;
import com.digiwin.escloud.userv2.user.model.eservice.EServiceUser;
import com.digiwin.escloud.userv2.user.model.eservice.EServiceUserRegisterReq;
import com.digiwin.escloud.userv2.user.model.product.UserProduct;
import com.digiwin.escloud.userv2.user.model.staff.StaffAccount;
import com.digiwin.escloud.userv2.user.model.user.*;
import com.digiwin.escloud.userv2.user.model.user.AuthoredUser;
import com.digiwin.escloud.userv2.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@RefreshScope
@Service
@Slf4j
public class UserBaseService implements SyncIamAccountServiceHelp {

    @Value("${digiwin.user.defaultlanguage}")
    private String defaultLanguage;
    @Value("${digiwin.user.connectarea}")
    private String connectarea;
    @Value("${digiwin.user.defaultserviceregion}")
    private String defaultServiceRegion;
    @Value("${digiwin.user.defaulttimezone}")
    private String defaultTimeZone;
    @Value("${digiwin.token.user.verifyuserid}")
    private String fromVerifyUserId;
    @Value("${digiwin.token.tenant.id}")
    private String fromTenantId;
    @Value("${digiwin.tenant.contract.startdate:********}")
    private String contractStartDate;
    @Value("${digiwin.tenant.contract.expritydate:********}")
    private String contractExprityDate;
    @Value("${digiwin.user.address}")
    private String userAddress;
    @Autowired
    private ITenantDao tenantDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private ISupplierDao supplierDao;
    @Autowired
    private IUserProductDao userProduct;
    @Autowired
    private IamService iamService;
    @Autowired
    private OmcService omcService;
    @Autowired
    private RoleServiceImpl roleService;
    @Autowired
    private PermissionCopyService permissionCopyService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private SupplierCache supplierCache;
    @Autowired
    private IIamUserDao iamUserDao;
    @Autowired
    private UserCache userCache;
    @Autowired
    private TenantCache tenantCache;
    @Autowired
    private AioUserFeignClient aioUserFeignClient;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ICustomerDao customerDao;

    private Customer buildCustomer(IamAuthoredUser iamAuthoredUser) {
        Customer customer = new Customer();
        customer.setEid(iamAuthoredUser.getTenantSid());
        int code;
        String maxCustomerCode = tenantDao.getMaxCustomerCode();
        if (StringUtils.isEmpty(maxCustomerCode)) {
            code = 0;
        } else {
            code = Integer.parseInt(maxCustomerCode.substring(2));
        }
        customer.setCustomerCode("ES" + String.format("%010d", code + 1));
        customer.setCustomerName(iamAuthoredUser.getTenantName());

        RegisterTenantVO currentTenant = iamService.getCurrentTenant(iamAuthoredUser.getToken());
        String customerId = currentTenant.getTenant().getCustomerId();
        if (StringUtils.isEmpty(customerId)) {
            String maxServiceCode = tenantDao.getMaxServiceCode();
            if (StringUtils.isEmpty(maxServiceCode)) {
                code = 0;
            } else {
                code = Integer.parseInt(maxServiceCode);
            }
            customer.setCustomerServiceCode(String.format("%08d", code + 1));
        } else {
            customer.setCustomerServiceCode(customerId);
        }
        return customer;
    }

    private CustomerServiceInfo buildCustomerServiceInfo(String productCode, String serviceCode, IamAuthoredUser iamAuthoredUser) {
        CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
        customerServiceInfo.setProductCode(productCode);
        customerServiceInfo.setCustomerServiceCode(serviceCode);
        customerServiceInfo.setHasOwnerService(false);
        customerServiceInfo.setHasTextService(true);
        customerServiceInfo.setHasOwnerIssueService(false);
        customerServiceInfo.setContractSource(ContractSource.IAM.getIndex());
        IamBaseResponse iamBaseResponse = omcService.getTenantAuth(iamAuthoredUser.getToken(), "", iamAuthoredUser.getTenantId());
        if (!iamBaseResponse.isSuccess()) {
            customerServiceInfo.setContractStartDate(contractStartDate);
            customerServiceInfo.setContractExprityDate(contractExprityDate);
            return customerServiceInfo;
        }
        if (ObjectUtils.isEmpty(iamBaseResponse.getData())) {
            customerServiceInfo.setContractStartDate(contractStartDate);
            customerServiceInfo.setContractExprityDate(contractExprityDate);
            return customerServiceInfo;
        }

        CustomerInfo customerInfo = JSONObject.parseObject(JSONObject.toJSONString(iamBaseResponse.getData()), CustomerInfo.class);
        CustomerService iamCustomerService = customerInfo.getCustomerService().stream()
                .filter(o -> productCode.equals(o.getProductCode()))
                .findAny()
                .orElse(null);
        if (!StringUtils.isEmpty(customerInfo.getServiceCode()) && StringUtils.isEmpty(serviceCode)) {
            serviceCode = customerInfo.getServiceCode();
        }
        customerServiceInfo.setCustomerServiceCode(serviceCode);
        if (ObjectUtils.isEmpty(iamCustomerService)) {
            customerServiceInfo.setContractStartDate(contractStartDate);
            customerServiceInfo.setContractExprityDate(contractExprityDate);
            return customerServiceInfo;
        }
        customerServiceInfo.setProductCategory(iamCustomerService.getProductCategory());

        List<ProductModule> productModules = iamCustomerService.getProductModules();
        if (CollectionUtils.isEmpty(productModules)) {
            customerServiceInfo.setContractStartDate(contractStartDate);
            customerServiceInfo.setContractExprityDate(contractExprityDate);
            return customerServiceInfo;
        }
        //应用的授权信息，存储到模组合约信息表
        tenantDao.insertCustomerServiceModule(serviceCode, productCode, productModules);
        productModules.stream()
                .map(o -> o.getContractStartDate())
                .min(Comparator.comparing(String::valueOf))
                .ifPresent(o -> customerServiceInfo.setContractStartDate(o));
        productModules.stream()
                .map(o -> o.getContractExpiryDate())
                .max(Comparator.comparing(String::valueOf))
                .ifPresent(o -> customerServiceInfo.setContractExprityDate(o));
        return customerServiceInfo;
    }

    private SupplierCustomerMap buildSupplierCustomerMap(long eid) {
        SupplierCustomerMap supplierCustomerMap = new SupplierCustomerMap();
        supplierCustomerMap.setSid(RequestUtil.getHeaderSid());
        supplierCustomerMap.setEid(eid);
        supplierCustomerMap.setDefault(true);
        return supplierCustomerMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public CustomerAllInfo saveCustomerIntoEs(String productCode, IamAuthoredUser iamAuthoredUser) throws Exception {
        CustomerAllInfo customerAllInfo = new CustomerAllInfo();
        Customer esCustomer = tenantDao.getCustomer(iamAuthoredUser.getTenantSid());
        if (ObjectUtils.isEmpty(esCustomer)) {
            esCustomer = buildCustomer(iamAuthoredUser);
            Customer customerByServiceCode = tenantDao.getCustomerByServiceCode(esCustomer.getCustomerServiceCode());
            if (!ObjectUtils.isEmpty(customerByServiceCode)) {
                throw new Exception("the serviceCode is used by other tenant");
            }
            tenantDao.insertCustomer(esCustomer);
        }
        String eidKey = UserV2Const.TENANT_SID + iamAuthoredUser.getTenantSid();
        stringRedisTemplate.opsForValue().set(eidKey, JSON.toJSONString(esCustomer, SerializerFeature.WriteMapNullValue), 60, TimeUnit.DAYS);
        CustomerServiceInfo esCustomerServiceInfo = tenantDao.getCustomerServiceInfo(esCustomer.getCustomerServiceCode(), productCode);
        if (ObjectUtils.isEmpty(esCustomerServiceInfo)) {
            esCustomerServiceInfo = buildCustomerServiceInfo(productCode, esCustomer.getCustomerServiceCode(), iamAuthoredUser);
            tenantDao.insertCustomerServiceInfo(esCustomerServiceInfo);
        }
        SupplierCustomerMap esSupplierCustomerMap = supplierDao.getSupplierTenantMapBySidEid(RequestUtil.getHeaderSid(), iamAuthoredUser.getTenantSid());
        if (ObjectUtils.isEmpty(esSupplierCustomerMap)) {
            esSupplierCustomerMap = buildSupplierCustomerMap(iamAuthoredUser.getTenantSid());
            supplierDao.insertSupplierCustomerMap(esSupplierCustomerMap);
        }
        customerAllInfo.setCustomer(esCustomer);
        customerAllInfo.setCustomerServiceInfo(esCustomerServiceInfo);
        customerAllInfo.setSupplierCustomerMap(esSupplierCustomerMap);
        return customerAllInfo;
    }

    public void saveRoleInfo(String token) {
        List<RoleAddOrUpdate> roleAddOrUpdates = new ArrayList<>();
        //只添加客户的角色
//        roleAddOrUpdates.add(new RoleAddOrUpdate("cosultant", "顧問", "defaultRoleCatalog"));
//        roleAddOrUpdates.add(new RoleAddOrUpdate("customerService", "客服人員", "defaultRoleCatalog"));
//        roleAddOrUpdates.add(new RoleAddOrUpdate("customerServiceAgent", "客服代理人", "defaultRoleCatalog"));
//        roleAddOrUpdates.add(new RoleAddOrUpdate("customerServiceSupervisor", "客服主管", "defaultRoleCatalog"));
        roleAddOrUpdates.add(new RoleAddOrUpdate("endERP", "終端ERP", "defaultRoleCatalog"));
        roleAddOrUpdates.add(new RoleAddOrUpdate("endUser", "一般用户", "defaultRoleCatalog"));
        roleAddOrUpdates.add(new RoleAddOrUpdate("mis", "管理者", "defaultRoleCatalog"));
        roleAddOrUpdates.add(new RoleAddOrUpdate("rdServcie", "研發服務", "defaultRoleCatalog"));
//        roleAddOrUpdates.add(new RoleAddOrUpdate("superManager", "服務雲_超級管理員", "defaultRoleCatalog"));
        roleAddOrUpdates.add(new RoleAddOrUpdate("superUser", "進階用戶", "defaultRoleCatalog"));

        roleAddOrUpdates.add(new RoleAddOrUpdate("ITMSUser", "運維一般用戶", "defaultRoleCatalog"));
//        roleAddOrUpdates.add(new RoleAddOrUpdate("ITMSService", "運維服務", "defaultRoleCatalog"));
//        roleAddOrUpdates.add(new RoleAddOrUpdate("ITMSPlugInManager", "運維插件管理", "defaultRoleCatalog"));
//        roleAddOrUpdates.add(new RoleAddOrUpdate("ITMSMaintain", "運維工程", "defaultRoleCatalog"));
        roleAddOrUpdates.stream().forEach(o -> roleService.roleAddOrUpdate(token, o));
    }

    public void copyRolePermission(String toIamToken, AioPlatformEnum aioPlatformEnum) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    switch (aioPlatformEnum) {
                        case THIRDPARTY_CUSTOMER:
                        case SCB_DT:
                        case SCB_APP_A1:
                        case SCB_WEB_A1:
                            permissionCopyService.copyEsCloudRolePermissionToTargetTenant(
                                    fromVerifyUserId, fromTenantId, "", "", toIamToken
                            );
                            break;
                        case THIRDPARTY_CUSTOMERSERVICE:
                        case SSM:
                        case AIOSSM:
                        case EASY_TALK:
                            permissionCopyService.copyOMRolePermissionToTargetTenant(
                                    fromVerifyUserId, fromTenantId, "", "", toIamToken
                            );
                            break;
                        default:
                            permissionCopyService.copyEsCloudRolePermissionToTargetTenant(
                                    fromVerifyUserId, fromTenantId, "", "", toIamToken
                            );
                            break;
                    }

                }
            });
        } catch (Exception ex) {
            log.error("copyRolePermission", ex);
        } finally {
            executorService.shutdown();
        }
    }

    private String getCustomerCode(String serviceCode) {
        return Optional.ofNullable(tenantDao.getCustomerCodeByServiceCode(serviceCode))
                .orElseGet(() -> tenantDao.getCustomerCodeByCustomerServiceCodeOnSerae(serviceCode));
    }

    private User buildEsUser(String productCode, IamAuthoredUser iamAuthoredUser, CustomerAllInfo customerAllInfo) {
        User esUser = new User();
        boolean isDigiwinUser = false;
        if (!StringUtils.isEmpty(iamAuthoredUser.getUserId()) && iamAuthoredUser.getUserId().endsWith(UserV2Const.DIGIWIN_MAIL)) {
            isDigiwinUser = true;
        }
        boolean isDigiwinTenant = false;
        if (UserV2Const.TENANT_SID_DIGIWIN == iamAuthoredUser.getTenantSid()) {
            isDigiwinTenant = true;
        }
        if (isDigiwinUser && isDigiwinTenant) {
            esUser = userDao.getStaffUserByEmail(iamAuthoredUser.getUserId(), String.valueOf(UserType.INNER.getValue()));
            if(ObjectUtils.isEmpty(esUser)){
                esUser = buildStaffUser(iamAuthoredUser, customerAllInfo.getCustomer());
                userDao.insertUserV2(esUser);
                userDao.insertStaffAccount(esUser.getId(),iamAuthoredUser.getUserId().replace(UserV2Const.DIGIWIN_MAIL, ""));
            }
            //处理userpersonalinfo
            UserPersonalInfo userPersonalInfo = buildUserPersonalInfo(iamAuthoredUser, esUser.getId());
            StaffAccount staffAccount = userDao.selectStaffAccount(iamAuthoredUser.getUserId().replace(UserV2Const.DIGIWIN_MAIL, ""));
            if(ObjectUtil.isNotEmpty(staffAccount)){
                userPersonalInfo.setDepartmentCode(staffAccount.getDepartmentcode());
                userPersonalInfo.setWorkno(staffAccount.getWorkno());
            }

            userDao.saveUserPersonalInfo(userPersonalInfo);
            if (StringUtils.isEmpty(userDao.getUserRoleExist(esUser.getId(), "4"))) {
                userDao.insertUserRole(esUser.getId(), "4");
            }

        }else {
            esUser = userDao.getUserByUserNameAndUserType(iamAuthoredUser.getUserId(),String.valueOf(UserType.OUTER.getValue()));
            if(ObjectUtils.isEmpty(esUser)){
                esUser = buildOuterUser(iamAuthoredUser, customerAllInfo.getCustomer());
                userDao.insertUserV2(esUser);
            }
            //处理userpersonalinfo
            userDao.saveUserPersonalInfo(buildUserPersonalInfo(iamAuthoredUser, esUser.getId()));
            if (StringUtils.isEmpty(userDao.getUserRoleExist(esUser.getId(), "1"))) {
                userDao.insertUserRole(esUser.getId(), "1");
            }
            //处理mars_user_has_servicecode
            UserHasServiceCode esUserHasServiceCode = userDao.getUserHasServiceCode(esUser.getId(), customerAllInfo.getCustomer().getCustomerServiceCode());
            if (ObjectUtils.isEmpty(esUserHasServiceCode)) {
                esUserHasServiceCode = buildUserHasServiceCode(productCode, esUser.getId(), customerAllInfo.getCustomer().getCustomerServiceCode());
                userDao.insertUserHasServiceCode(esUserHasServiceCode);
            }
            //非大陆地区 鼎捷邮箱+非鼎捷租户的时候，需要记录到eserviceuser、eserviceaccount
            if (!"CN".equals(connectarea) && isDigiwinUser && !isDigiwinTenant) {
                //根据邮箱和客代查eserviceuser，存在无须处理，不存在需要新增一笔
                if(userDao.getEServiceUser(iamAuthoredUser.getUserId(),customerAllInfo.getCustomer().getCustomerServiceCode()) == 0){
                    //新增一笔，如果有历史资料，是copy历史资料，如果无历史资料，需要新增一笔
                    EServiceUser eServiceUserHis = userDao.getEServiceUserByEmail(iamAuthoredUser.getUserId());
                    if(ObjectUtil.isEmpty(eServiceUserHis)){
                        eServiceUserHis= new EServiceUser();
                        eServiceUserHis.setPermission("user");
                        eServiceUserHis.setLanguage("tw");
                        eServiceUserHis.setEmail(iamAuthoredUser.getUserId());
                    }
                    eServiceUserHis.setCustomerServiceCode(customerAllInfo.getCustomer().getCustomerServiceCode());
                    userDao.insertEServiceUser(eServiceUserHis);
                }

                //用mail及userId查是否已存在于mars_eserviceaccount,有就无须处理，没有需要新增
                userDao.updateEserviceAccount(iamAuthoredUser.getUserId(),esUser.getId());
            }
        }

        return esUser;
    }

    private User buildUser(IamAuthoredUser iamAuthoredUser, Customer customer) {
        User user = new User();
        user.setId(UuidUtil.NewEsId());
        String customerCode = getCustomerCode(customer.getCustomerServiceCode());
        user.setCustomerCode(customerCode);
        user.setUserName(iamAuthoredUser.getUserId());
        user.setPassword(DigestUtils.md5DigestAsHex(UserV2Const.DEFAULT_ES_PWD.getBytes()));
        user.setRemark("");
        user.setEmail(iamAuthoredUser.getEmail());
        user.setPhone(iamAuthoredUser.getTelephone());
        user.setCustomerServiceCode(customer.getCustomerServiceCode());
        user.setBindingtime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        return user;
    }

    private User buildStaffUser(IamAuthoredUser iamAuthoredUser, Customer customer) {
        //注册客服账户，注册前检查itcode 是否被客户占用。如果占用，客服的username存鼎捷邮箱+C，如果没有，username存itcode
        User user = new User();
        user.setId(UuidUtil.NewEsId());
        String customerCode = getCustomerCode(customer.getCustomerServiceCode());
        user.setCustomerCode(customerCode);
        user.setUserName(Optional.ofNullable(userDao.getUserByUserNameAndUserType(iamAuthoredUser.getUserId().replace(UserV2Const.DIGIWIN_MAIL, ""), String.valueOf(UserType.OUTER.getValue()))).isPresent()?iamAuthoredUser.getUserId()+"C":iamAuthoredUser.getUserId().replace(UserV2Const.DIGIWIN_MAIL, ""));
        user.setPassword("");
        user.setRemark("");
        user.setEmail(iamAuthoredUser.getEmail());
        user.setPhone(iamAuthoredUser.getTelephone());
        user.setCustomerServiceCode(customer.getCustomerServiceCode());
        user.setUserType(String.valueOf(UserType.INNER.getValue()));
        user.setBindingtime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        return user;
    }

    private User buildOuterUser(IamAuthoredUser iamAuthoredUser, Customer customer) {
        //注册客户账户，注册前，检查username=iamUserId并且userType=2是否存在，如果存在，表示客服已经注册了，需要将客服的mars_user.username更新为鼎捷邮箱+C。让客户正常注册
        User existUser = userDao.getUserByUserNameAndUserType(iamAuthoredUser.getUserId(), String.valueOf(UserType.INNER.getValue()));
        if(ObjectUtil.isNotEmpty(existUser)){
            log.info("注册客户账户时，iamUserId被客服先注册，需要修正客服的username为鼎捷邮箱+C");
            existUser.setUserName(existUser.getEmail()+"C");
            userDao.updateStaffUserName(existUser);
        }
        User user = new User();
        user.setId(UuidUtil.NewEsId());
        String customerCode = getCustomerCode(customer.getCustomerServiceCode());
        user.setCustomerCode(customerCode);
        user.setUserName(iamAuthoredUser.getUserId());
        user.setPassword(DigestUtils.md5DigestAsHex(UserV2Const.DEFAULT_ES_PWD.getBytes()));
        user.setRemark("");
        user.setEmail(iamAuthoredUser.getEmail());
        user.setPhone(iamAuthoredUser.getTelephone());
        user.setCustomerServiceCode(customer.getCustomerServiceCode());
        user.setUserType(String.valueOf(UserType.OUTER.getValue()));
        user.setBindingtime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        return user;
    }

    private UserPersonalInfo buildUserPersonalInfo(IamAuthoredUser iamAuthoredUser, String userId) {
        UserPersonalInfo userPersonalInfo = new UserPersonalInfo();
        userPersonalInfo.setUserId(userId);
        userPersonalInfo.setEmail(iamAuthoredUser.getEmail());
        userPersonalInfo.setPhone(iamAuthoredUser.getTelephone());
        userPersonalInfo.setName(iamAuthoredUser.getUserName());
        userPersonalInfo.setLanguage(defaultLanguage);
        userPersonalInfo.setServiceRegion(defaultServiceRegion);
        userPersonalInfo.setTimeZone(defaultTimeZone);
        return userPersonalInfo;
    }

    private void buildUserMapping(IamAuthoredUser iamAuthoredUser, String userId) {
        List<MappingInfo> mappingInfos = new ArrayList<>();
        MappingInfo mappingInfo = new MappingInfo();
        mappingInfo.setTenantId(iamAuthoredUser.getTenantId());
        mappingInfo.setProviderId(UserV2Const.PROVIDERID);
        mappingInfo.setVerifyUserId(userId);
        mappingInfos.add(mappingInfo);

        List<RoleInfo> roleInfos = new ArrayList<>();
        RoleInfo roleInfo = new RoleInfo();
        roleInfo.setId(UserV2Const.ROLE_ENDUSERID);
        roleInfo.setName(UserV2Const.ROLE_ENDUSERNAME);
        roleInfos.add(roleInfo);

        UserBasicInfo userBasicInfo = new UserBasicInfo();
        UserImportInfo user = new UserImportInfo();
        user.setId(iamAuthoredUser.getUserId());
        //iam必须要传name
        user.setName(iamAuthoredUser.getUserName());
        user.setEmail(iamAuthoredUser.getEmail());
        user.setTelephone(iamAuthoredUser.getTelephone());
        user.setTenantId(iamAuthoredUser.getTenantId());
        user.setTenantName(iamAuthoredUser.getTenantName());
        //从sso登入iam反馈是type 一般登入是userType
        //  默认0：一般用户 1：企业用户
        String userType = StringUtils.isEmpty(iamAuthoredUser.getUserType()) ? iamAuthoredUser.getType() : iamAuthoredUser.getUserType();
        user.setType(StringUtils.isEmpty(userType) ? "0" : userType);

        userBasicInfo.setUser(user);
        userBasicInfo.setMappingInfo(mappingInfos);
        userBasicInfo.setRoleInfo(roleInfos);
        List<UserBasicInfo> userBasicInfos = new ArrayList<>();
        userBasicInfos.add(userBasicInfo);
        log.info("buildUserMapping : " + SerializeUtil.JsonSerialize(userBasicInfos));
        iamService.importUserInTenant(userBasicInfos);
        UserMappingQueryResultVO userMappingQueryResultVO = new UserMappingQueryResultVO();
        List<UserMappingQueryResultVO> mapping = iamAuthoredUser.getMapping();
        if (mapping == null) {
            mapping = new ArrayList<>();
        }
        BeanUtils.copyProperties(mappingInfo, userMappingQueryResultVO);
        mapping.add(userMappingQueryResultVO);
    }

    private UserHasServiceCode buildUserHasServiceCode(String productCode, String userId, String serviceCode) {
        UserHasServiceCode userHasServiceCode = new UserHasServiceCode();
        userHasServiceCode.setUserId(userId);
        userHasServiceCode.setCustomerServiceCode(serviceCode);
        userHasServiceCode.setDisplayTenant(true);
        userHasServiceCode.setEffective(true);
        userHasServiceCode.setApproved(true);
        userHasServiceCode.setProcess(false);
        userHasServiceCode.setProductCode(productCode);
        return userHasServiceCode;
    }

    @Transactional(rollbackFor = Exception.class)
    public UserAllInfo saveUserIntoEs(String productCode, IamAuthoredUser iamAuthoredUser, CustomerAllInfo customerAllInfo) {
        UserAllInfo userAllInfo = new UserAllInfo();
        //处理user主表
        /*String esUserId = getEsUserIdNew(iamAuthoredUser.getUserId(), iamAuthoredUser.getTenantSid());
        User esUser = userDao.getUserByUserId(esUserId);
        if (ObjectUtils.isEmpty(esUser)) {
            esUser = buildUser(iamAuthoredUser, customerAllInfo.getCustomer());
            userDao.insertUser(esUser);
        }*/

        User esUser = buildEsUser(productCode, iamAuthoredUser, customerAllInfo);
        userAllInfo.setUser(esUser);

        //处理iam es 用户mapping关系
        buildUserMapping(iamAuthoredUser, esUser.getId());
        //处理iamaccount
        IamAccount iamAccount = userCache.getIamAccount(iamAuthoredUser.getSid());
        if (ObjectUtils.isEmpty(iamAccount)) {
            iamAccount = new IamAccount(SnowFlake.getInstance().newId(), esUser.getId(),
                    iamAuthoredUser.getSid(), iamAuthoredUser.getUserId());
            iamUserDao.saveIamAccount(iamAccount);
            stringRedisTemplate.opsForValue().set(UserV2Const.IAM_ES_USER_MAPPING_KEY + iamAuthoredUser.getSid() + "", JSON.toJSONString(iamAccount), 60, TimeUnit.DAYS);
        }
        //处理iamaccountnew
        IamAccount iamAccountNew = iamUserDao.getIamAccountNew(iamAuthoredUser.getSid(),esUser.getId());
        if (ObjectUtils.isEmpty(iamAccountNew)) {
            iamAccount = new IamAccount(SnowFlake.getInstance().newId(), esUser.getId(),
                    iamAuthoredUser.getSid(), iamAuthoredUser.getUserId());
            iamUserDao.saveIamAccountNew(iamAccount);
            stringRedisTemplate.opsForValue().set(UserV2Const.IAM_ES_USER_MAPPING_KEY + iamAuthoredUser.getSid() + "", JSON.toJSONString(iamAccount), 60, TimeUnit.DAYS);
        }

        return userAllInfo;
    }



    public UserProduct saveUserDefaultProduct(String userId, String productCode) {
        UserProduct productByUser = userProduct.getProductByUser(userId, productCode, null);
        if (ObjectUtils.isEmpty(productByUser)) {
            UserProduct userProduct = new UserProduct(userId, productCode, "1");
            this.userProduct.saveUserDefaultProduct(userProduct);
            this.userProduct.updateUserNoDefaultProduct(userProduct);
        } else {
            productByUser.setIsDefault("1");
            userProduct.saveUserDefaultProduct(productByUser);
            userProduct.updateUserNoDefaultProduct(productByUser);
        }
        return productByUser;
    }

    public DigiwinOAuthToken buildDigiwinOAuthToken(long eid, String tenantId, long userSid, User user) {
        DigiwinOAuthToken digiwinOAuthToken = new DigiwinOAuthToken();
        Customer customer = tenantCache.getCustomer(eid);
        digiwinOAuthToken.setAccessToken(UuidUtil.NewEsId());
        digiwinOAuthToken.setScope("SCP");
        digiwinOAuthToken.setCreateTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        digiwinOAuthToken.setExpireTime(DateFormatUtils.format(Utils.GetDateAfterDays(30), "yyyy-MM-dd HH:mm:ss"));
        digiwinOAuthToken.setCustomerCode(customer.getCustomerCode());
        digiwinOAuthToken.setUsername(user.getUserName());
        digiwinOAuthToken.setServiceCoe(customer.getCustomerServiceCode());
        digiwinOAuthToken.setUserId(user.getId());
        digiwinOAuthToken.setUserSid(userSid);
        digiwinOAuthToken.setEid(eid);
        digiwinOAuthToken.setSid(RequestUtil.getHeaderSid());
        digiwinOAuthToken.setTenantId(tenantId);
        return digiwinOAuthToken;
    }

    public AccessToken buildAccessToken(String userId) {
        AccessToken accessToken = new AccessToken();
        accessToken.setAccessToken(UuidUtil.NewEsId());
        accessToken.setScope("userserviceplatform");
        accessToken.setCreateTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        accessToken.setExpireTime(DateFormatUtils.format(Utils.GetDateAfterDays(30), "yyyy-MM-dd HH:mm:ss"));
        accessToken.setUserId(userId);
        return accessToken;
    }

    public AuthoredUser getAuthoredUser(IamAuthoredUser iamAuthoredUser) {
        Supplier supplier = supplierCache.getSupplier(RequestUtil.getHeaderSid());
        AuthoredUser authoredUser = new AuthoredUser();
        authoredUser.setSupplierSid(RequestUtil.getHeaderSid());
        authoredUser.setSupplierName(supplier.getName());
        BeanUtils.copyProperties(iamAuthoredUser, authoredUser);
        try {
            ResponseBase res = aioUserFeignClient.getUserOrg(false, authoredUser.getSupplierSid(), authoredUser.getTenantSid(), authoredUser.getSid());
            if (ResponseCode.SUCCESS.toString().equals(res.getCode())) {
                if (ObjectUtils.isEmpty(res.getData())) {
                    authoredUser.setOrgSid(null);
                } else {
                    authoredUser.setOrgSid(Long.valueOf(res.getData().toString()));
                }
            }
        } catch (Exception e) {
            log.error("aioUserFeignClient.getUserOrg error", e);
        }
        stringRedisTemplate.opsForValue().set(iamAuthoredUser.getToken(), JSON.toJSONString(authoredUser), iamAuthoredUser.getTokenExpiresIn(), TimeUnit.MILLISECONDS);
        return authoredUser;
    }

    public String getEsUserId(AuthoredUser authoredUser) {
        String userId;
        IamAccount iamAccount = userCache.getIamAccount(authoredUser.getSid());
        if (ObjectUtils.isEmpty(iamAccount)) {
            List<UserMappingQueryResultVO> mapping = authoredUser.getMapping();
            UserMappingQueryResultVO userMappingQueryResultVO = mapping.stream()
                    .filter(o -> UserV2Const.PROVIDERID.equals(o.getProviderId()))
                    .findAny()
                    .orElse(null);
            if (ObjectUtils.isEmpty(userMappingQueryResultVO)) {
                userId = null;
            } else {
                iamAccount = new IamAccount(SnowFlake.getInstance().newId(), userMappingQueryResultVO.getVerifyUserId(),
                        authoredUser.getSid(), authoredUser.getUserId());
                iamUserDao.saveIamAccount(iamAccount);
                iamUserDao.saveIamAccountNew(iamAccount);
                userId = userMappingQueryResultVO.getVerifyUserId();
                stringRedisTemplate.opsForValue().set(UserV2Const.IAM_ES_USER_MAPPING_KEY + authoredUser.getSid() + "", JSON.toJSONString(iamAccount), 60, TimeUnit.DAYS);
            }
        } else {
            userId = iamAccount.getUserId();
        }
        return userId;
    }

    public String getEsUserIdNew(String iamUserId, long tenantSid) {
        String userId = "";
        boolean isDigiwinUser = false;
        if (!StringUtils.isEmpty(iamUserId) && iamUserId.endsWith(UserV2Const.DIGIWIN_MAIL)) {
            isDigiwinUser = true;
        }
        boolean isDigiwinTenant = false;
        if (UserV2Const.TENANT_SID_DIGIWIN == tenantSid) {
            isDigiwinTenant = true;
        }
//        String userName = "";
        if (isDigiwinUser && isDigiwinTenant) {
            /*userName = iamUserId.replace(UserV2Const.DIGIWIN_MAIL, "");
            User userByItCode = Optional.ofNullable(userDao.getUserByUserName(userName)).orElseGet(() -> new User());
            userId = userByItCode.getId();*/
            //根据鼎捷邮箱（鼎捷用户在iam上面的iamUserId就是鼎捷邮箱）+userType=2，查是否有客服账户
            User userByEmail = Optional.ofNullable(userDao.getStaffUserByEmail(iamUserId, String.valueOf(UserType.INNER.getValue()))).orElseGet(() -> new User());
            userId = userByEmail.getId();
        /*} else if (isDigiwinUser && !isDigiwinTenant) {
            User userByIam = userDao.getUserByUserName(iamUserId);
            if (ObjectUtils.isEmpty(userByIam)) {
                userName = iamUserId.replace(UserV2Const.DIGIWIN_MAIL, "");
                User userByItCode = Optional.ofNullable(userDao.getUserByUserName(userName)).orElseGet(() -> new User());
                userId = userByItCode.getId();
            } else {
                userId = userByIam.getId();
            }
        } else {
            /*User userByIam = Optional.ofNullable(userDao.getUserByUserName(iamUserId)).orElseGet(() -> new User());
            userId = userByIam.getId();*/
        }else {
            //查username=iamUserId（服务云客户在iam上面的iamUserId就是mars_user.username）并且userType=1的用户
            User userByIam = Optional.ofNullable(userDao.getUserByUserNameAndUserType(iamUserId,String.valueOf(UserType.OUTER.getValue()))).orElseGet(() -> new User());
            userId = userByIam.getId();
        }
        return userId;
    }

    /**
     * 清除租户Id结构缓存
     *
     * @param eidList 租户Id列表
     * @return 影响key数
     */
    public Long clearEidCache(List<Long> eidList) {
        return tenantCache.clearEidCache(eidList);
    }

    public EsAuthoredUser getEsAuthoredUser(long eid, String tenantId, long userSid, UserAllInfo userAllInfo) {
        EsAuthoredUser esAuthoredUser = new EsAuthoredUser();
        User user = userAllInfo.getUser();
        log.info("user:{}", JSON.toJSONString(user));
        log.info("eid:{}", eid);
        DigiwinOAuthToken digiwinOAuthToken = buildDigiwinOAuthToken(eid, tenantId, userSid, user);
        stringRedisTemplate.opsForValue().set(digiwinOAuthToken.getAccessToken(), JSON.toJSONString(digiwinOAuthToken), 30, TimeUnit.DAYS);

        esAuthoredUser.setAccessToken(digiwinOAuthToken.getAccessToken());
        esAuthoredUser.setCustomerCode(digiwinOAuthToken.getCustomerCode());
        esAuthoredUser.setUserId(user.getId());
        esAuthoredUser.setUserName(user.getUserName());
        esAuthoredUser.setServiceCode(digiwinOAuthToken.getServiceCoe());
        esAuthoredUser.setTenantId(tenantId);
        esAuthoredUser.setEid(eid);
        esAuthoredUser.setSid(RequestUtil.getHeaderSid());
        return esAuthoredUser;
    }

    private User buildAioEsUser(CountingUser countingUser) {
        User user = new User();
        user.setId(UuidUtil.NewEsId());
        String customerCode = getCustomerCode(countingUser.getServiceCode());
        user.setCustomerCode(customerCode);
        user.setUserName(countingUser.getUserId());
        user.setPassword(DigestUtils.md5DigestAsHex(countingUser.getTenantId().getBytes()));
        try {
            if (countingUser.getPasswordHash() != null) {
                byte[] decrypt = EncryptionUtil.decrypt(countingUser.getPasswordHash(), EncryptionUtil.key.getBytes());
                user.setPassword(DigestUtils.md5DigestAsHex(decrypt));
            }
        } catch (Exception e) {
            log.error("解密 failed", e);
        }
        user.setRemark("");
        user.setEmail(countingUser.getEmail());
        user.setPhone(countingUser.getTelephone());
        user.setCustomerServiceCode(countingUser.getServiceCode());
        user.setBindingtime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        return user;
    }

    private UserPersonalInfo buildAioEsUserPersonal(CountingUser countingUser, String userId) {
        UserPersonalInfo userPersonalInfo = new UserPersonalInfo();
        userPersonalInfo.setUserId(userId);
        userPersonalInfo.setEmail(countingUser.getEmail());
        userPersonalInfo.setPhone(countingUser.getTelephone());
        userPersonalInfo.setName(countingUser.getUserName());
        userPersonalInfo.setLanguage(defaultLanguage);
        userPersonalInfo.setServiceRegion(defaultServiceRegion);
        userPersonalInfo.setTimeZone(defaultTimeZone);
        return userPersonalInfo;
    }

    public void saveAioUserDefaultProduct(String userId, String productCode) {
        UserProduct productByUser = userProduct.getProductByUser(userId, productCode, null);
        if (ObjectUtils.isEmpty(productByUser)) {
            UserProduct defaultProductByUser = userProduct.getProductByUser(userId, null, "1");
            if (ObjectUtils.isEmpty(defaultProductByUser)) {
                UserProduct userProduct = new UserProduct(userId, productCode, "1");
                this.userProduct.saveUserDefaultProduct(userProduct);
            } else {
                UserProduct userProduct = new UserProduct(userId, productCode, "0");
                this.userProduct.saveUserDefaultProduct(userProduct);
            }
        }
    }

    private void buildAioUserMapping(CountingUser countingUser, String userId) {
        List<MappingInfo> mappingInfos = new ArrayList<>();
        MappingInfo mappingInfo = new MappingInfo();
        mappingInfo.setTenantId(countingUser.getTenantId());
        mappingInfo.setProviderId(UserV2Const.PROVIDERID);
        mappingInfo.setVerifyUserId(userId);
        mappingInfos.add(mappingInfo);

        List<RoleInfo> roleInfos = new ArrayList<>();
        RoleInfo roleInfo = new RoleInfo();
        if (UserV2Const.ROLE_ENDUSERID.equals(countingUser.getRoleId())) {
            roleInfo.setId(UserV2Const.ROLE_ENDUSERID);
            roleInfo.setName(UserV2Const.ROLE_ENDUSERNAME);
            //额外设置一般客服
            RoleInfo customer = new RoleInfo();
            customer.setId(UserV2Const.CUSTOMER_SERVICE);
            customer.setName(UserV2Const.CUSTOMER_SERVICE_NAME);
            roleInfos.add(customer);
        } else {
            roleInfo.setId(UserV2Const.ROLE_MISID);
            roleInfo.setName(UserV2Const.ROLE_MISNAME);
        }
        roleInfos.add(roleInfo);
        log.info("[buildAioUserMapping] roleInfos:{}", roleInfos);
        UserBasicInfo userBasicInfo = new UserBasicInfo();
        UserImportInfo user = new UserImportInfo();
        user.setId(countingUser.getUserId());
        //iam必须要传name
        user.setName(countingUser.getUserName());
        user.setPassword(countingUser.getServiceCode());
        try {
            if (countingUser.getPasswordHash() != null) {
                byte[] decrypt = EncryptionUtil.decrypt(countingUser.getPasswordHash(), EncryptionUtil.key.getBytes());
                user.setPassword(new String(decrypt));
            }
        } catch (Exception e) {
            log.error("解密 failed", e);
        }
        user.setEmail(countingUser.getEmail());
        user.setTelephone(countingUser.getTelephone());
        user.setTenantId(countingUser.getTenantId());

        userBasicInfo.setUser(user);
        userBasicInfo.setMappingInfo(mappingInfos);
        userBasicInfo.setRoleInfo(roleInfos);
        List<UserBasicInfo> userBasicInfos = new ArrayList<>();
        userBasicInfos.add(userBasicInfo);
        log.info("buildAioUserMapping : " + SerializeUtil.JsonSerialize(userBasicInfos));
        iamService.importUserInTenant(userBasicInfos);
    }

    public ResponseBase callEServiceUserRegister(CountingUser countingUser, boolean needGuiNumber) {
        EServiceUserRegisterReq eServiceUserRegisterReq = new EServiceUserRegisterReq();
        eServiceUserRegisterReq.setEServiceUserName(countingUser.getEmail());
        eServiceUserRegisterReq.setTelephone(countingUser.getTelephone());
        eServiceUserRegisterReq.setProductCode(countingUser.getProductCode());
        eServiceUserRegisterReq.setServiceCode(countingUser.getServiceCode());
        eServiceUserRegisterReq.setUserName(countingUser.getEmail());
        eServiceUserRegisterReq.setNickName(countingUser.getUserName());
        eServiceUserRegisterReq.setMail(countingUser.getEmail());
        eServiceUserRegisterReq.setPassword(new String(Base64.getEncoder().encode(countingUser.getServiceCode().getBytes())));//默认密码
        try {
            if (countingUser.getPasswordHash() != null) {
                byte[] decrypt = EncryptionUtil.decrypt(countingUser.getPasswordHash(), EncryptionUtil.key.getBytes());
                eServiceUserRegisterReq.setPassword(new String(Base64.getEncoder().encode(decrypt)));
            }
        } catch (Exception e) {
            log.error("callEServiceUserRegister decrypt pwd error", e);
            return ResponseBase.error(e);
        }
        eServiceUserRegisterReq.setLanguage(defaultLanguage);
        String guiNumber;
        if (needGuiNumber) {
            guiNumber = StringUtils.isEmpty(countingUser.getGuiNumber()) ? customerDao.getCustomerGuiNumber(countingUser.getServiceCode()) : countingUser.getGuiNumber();
        } else {
            guiNumber = null;
        }
        //如果不传guiNumber，则不会调用eservice接口，会写入escloud-db库的eserviceuser表（写入栏位客代和邮箱），后面台湾驻派员会同步该表数据补充完整
        eServiceUserRegisterReq.setGuiNumber(guiNumber);
        eServiceUserRegisterReq.setServiceRegion(defaultServiceRegion);
        eServiceUserRegisterReq.setTimeZone(defaultTimeZone);
        String eServiceUserStr = JSON.toJSONString(eServiceUserRegisterReq);
        try {
            log.info("create EServiceUser iamUserId:{} EServiceUser:{}", countingUser.getUserId(), eServiceUserStr);
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json");
            HttpEntity<String> formEntity = new HttpEntity<String>(eServiceUserStr, headers);
            ResponseEntity<ResponseBase> res = restTemplate.postForEntity(userAddress + UserV2Const.ESERVICE_REGISTER_URL, formEntity, ResponseBase.class);
            log.info("register eservice body:{} res:{}", eServiceUserStr, JSON.toJSONString(res.getBody()));
            return res.getBody();
        } catch (Exception e) {
            log.error("register eservice body:" + eServiceUserStr + " error ", e);
            return ResponseBase.error(e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveAioUserIntoEs(CountingUser countingUser) {
        //处理user主表
        String esUserId = getEsUserIdNew(countingUser.getUserId(), countingUser.getTenantSid());
        User esUser = userDao.getUserByUserId(esUserId);
        if (ObjectUtils.isEmpty(esUser)) {
            esUser = buildAioEsUser(countingUser);
            userDao.insertUser(esUser);
        }
        //处理userrole
        if (StringUtils.isEmpty(userDao.getUserRoleExist(esUser.getId(), "1"))) {
            userDao.insertUserRole(esUser.getId(), "1");
        }
        //处理userpersonalinfo
        userDao.saveUserPersonalInfo(buildAioEsUserPersonal(countingUser, esUser.getId()));
        //处理userdefaultproductinfo
        saveAioUserDefaultProduct(esUser.getId(), countingUser.getProductCode());

        //处理iam es 用户mapping关系
        buildAioUserMapping(countingUser, esUser.getId());
        //处理iamaccount
        HashMap<String, String> userMap = new HashMap<>();
        userMap.put("id", countingUser.getUserId());
        UserInfo userInfoDetail = iamService.getUserFullInfo(RequestUtil.getHeaderToken(), userMap);
        IamAccount iamAccount = userCache.getIamAccount(userInfoDetail.getSid());
        if (ObjectUtils.isEmpty(iamAccount)) {
            iamAccount = new IamAccount(SnowFlake.getInstance().newId(), esUser.getId(),
                    userInfoDetail.getSid(), countingUser.getUserId());
            iamUserDao.saveIamAccount(iamAccount);
            iamUserDao.saveIamAccountNew(iamAccount);
            stringRedisTemplate.opsForValue().set(UserV2Const.IAM_ES_USER_MAPPING_KEY + userInfoDetail.getSid() + "", JSON.toJSONString(iamAccount), 60, TimeUnit.DAYS);
        }
        //处理mars_user_has_servicecode
        UserHasServiceCode esUserHasServiceCode = userDao.getUserHasServiceCode(esUser.getId(), countingUser.getServiceCode());
        if (ObjectUtils.isEmpty(esUserHasServiceCode)) {
            esUserHasServiceCode = buildUserHasServiceCode(countingUser.getProductCode(), esUser.getId(), countingUser.getServiceCode());
            userDao.insertUserHasServiceCode(esUserHasServiceCode);
        }
        //如果不是大陆则需要创建eservice账号
        if (!"CN".equals(defaultServiceRegion)) {
            CompletableFuture.runAsync(() -> {
                //1.先传入客服密码调用云管家接口，该接口中会调用eservice接口尝试创建eservice账号，如果创建不成功，会返回错误码
                //2.如果第一步创建eservice账号失败，则在第二步不传入客服密码（此时云管家接口不会调用eservice），直接创建eserivceuser简易账号，保证云管家能登陆使用
                ResponseBase responseBase = callEServiceUserRegister(countingUser, true);
                if (!ResponseCode.SUCCESS.isSameCode(responseBase.getCode())) {
                    callEServiceUserRegister(countingUser, false);
                }
            });
        }
        return esUser.getId();
    }

    private User buildCustomerEsUser(String serviceCode, String customerCode) {
        User user = new User();
        user.setId(UuidUtil.NewEsId());
        user.setCustomerCode(customerCode);
        user.setUserName(serviceCode);
        user.setCustomerServiceCode(serviceCode);
        user.setPassword(DigestUtils.md5DigestAsHex(serviceCode.getBytes()));
        user.setEmail(serviceCode + UserV2Const.DIGIWIN_MAIL);
        user.setBindingtime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        return user;
    }

    public void buildCustomerUserMapping(String userId, Customer customer) {
        String serviceCode = customer.getCustomerServiceCode();
        String customerName = customer.getCustomerName();
        String taxNo = customer.getTaxNo();
        List<MappingInfo> mappingInfos = new ArrayList<>();
        MappingInfo mappingInfo = new MappingInfo();
        mappingInfo.setTenantId(serviceCode);
        mappingInfo.setProviderId(UserV2Const.PROVIDERID);
        mappingInfo.setVerifyUserId(userId);
        mappingInfos.add(mappingInfo);

        List<RoleInfo> roleInfos = new ArrayList<>();
        roleInfos.add(new RoleInfo(UserV2Const.ROLE_MISID, UserV2Const.ROLE_MISNAME));
        roleInfos.add(new RoleInfo(UserV2Const.ROLE_ENDUSERID, UserV2Const.ROLE_ENDUSERNAME));
        UserBasicInfo userBasicInfo = new UserBasicInfo();
        UserImportInfo user = new UserImportInfo();
        user.setId(serviceCode);
        //iam必须要传name
        user.setName(serviceCode);
        user.setPassword(this.getTenantDefaultPassword(userId));
        user.setEmail(serviceCode + UserV2Const.DIGIWIN_MAIL);
        user.setEnterprise(true);
        user.setTenantId(serviceCode);
        user.setTenantName(customerName);
        user.setTaxNo(taxNo);
        user.setCustomId(serviceCode);

        userBasicInfo.setUser(user);
        userBasicInfo.setMappingInfo(mappingInfos);
        userBasicInfo.setRoleInfo(roleInfos);
        List<UserBasicInfo> userBasicInfos = new ArrayList<>();
        userBasicInfos.add(userBasicInfo);
        log.info("buildTenantUserMapping : " + SerializeUtil.JsonSerialize(userBasicInfos));
        iamService.importTenant(userBasicInfos);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveCustomerIntoEs(Customer customer) {
        Customer esCustomer = tenantDao.getCustomerByServiceCode(customer.getCustomerServiceCode());
        if (ObjectUtils.isEmpty(esCustomer)) {
            tenantDao.insertCustomer(customer);
        }
        Long eid = customer.getEid();
        if (eid != null && eid != 0) {
            String eidKey = UserV2Const.TENANT_SID + eid;
            stringRedisTemplate.opsForValue().set(eidKey, JSON.toJSONString(customer, SerializerFeature.WriteMapNullValue), 60, TimeUnit.DAYS);

            SupplierCustomerMap esSupplierCustomerMap = supplierDao.getSupplierTenantMapBySidEid(RequestUtil.getHeaderSid(), eid);
            if (ObjectUtils.isEmpty(esSupplierCustomerMap)) {
                esSupplierCustomerMap = buildSupplierCustomerMap(eid);
                supplierDao.insertSupplierCustomerMap(esSupplierCustomerMap);
            }
        }
        List<CustomerServiceInfo> customerServiceInfos = customer.getCustomerServiceInfos();
        if (!CollectionUtils.isEmpty(customerServiceInfos)) {
            tenantDao.batchSaveCustomerServiceInfos(customerServiceInfos);
        }
        if (!"CN".equals(defaultServiceRegion)) {
            Integer seraeExists = customerDao.getSeraeExists(customer.getCustomerServiceCode());
            if (seraeExists == null) {
                Serae serae = new Serae(customer.getCustomerServiceCode(), customer.getCustomerName(),
                        customer.getCustomerCode(), customer.getTaxNo());
                customerDao.insertSeraeSimpleInfo(serae);
            }
        }
    }

    public void importTenantInfo(Customer customer) {
        String serviceCode = customer.getCustomerServiceCode();
        String customerCode = customer.getCustomerCode();
        User esUser = userDao.getUserByUserName(serviceCode);
        if (ObjectUtils.isEmpty(esUser)) {
            esUser = buildCustomerEsUser(serviceCode, customerCode);
            userDao.insertUser(esUser);
        }
        buildCustomerUserMapping(esUser.getId(), customer);
        List<String> tenantIds = new ArrayList<>();
        tenantIds.add(serviceCode);
        List<TenantVO> tenantSimpleInfos = iamService.getTenantSimpleInfo(RequestUtil.getHeaderToken(), tenantIds);
        if (!CollectionUtils.isEmpty(tenantSimpleInfos)) {
            TenantVO tenantVO = tenantSimpleInfos.get(0);
            customer.setEid(tenantVO.getSid());
        }
        saveCustomerIntoEs(customer);
    }
}