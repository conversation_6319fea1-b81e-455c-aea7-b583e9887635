<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.userv2.user.dao.IUserDao">

    <update id="updateUserEmail">
        update mars_userpersonalinfo
        set email=#{email}
        where userid=#{userId}
    </update>

    <update id="updateUserPhone">
        update mars_userpersonalinfo
        set phone=#{phone}
        where userid=#{userId}
    </update>

    <select id="getUserPersonalInfo" resultType="com.digiwin.escloud.userv2.user.model.user.UserPersonalInfo">
        select userid,name,email,phone,language,workno,serviceRegion,timeZone
        from mars_userpersonalinfo
        where userid = #{userId}
        limit 1;
    </select>

    <select id="getCustomerCodeByServiceCode" resultType="java.lang.String">
        select ifnull(customercode, '') as serviceCode  from mars_customer where CustomerServiceCode = #{serviceCode}
        limit 1
    </select>

    <select id="getCustomerCodeByCustomerServiceCodeOnSerae" resultType="java.lang.String">
        select ifnull(AE003, '') as serviceCode  from serae where AE001 = #{serviceCode}
        limit 1
    </select>

    <insert id="insertUser" parameterType="com.digiwin.escloud.userv2.user.model.user.User">
        insert into mars_user (ID, CustomerCode, Username,Password, Remark, Email,Phone,CustomerServiceCode, bindingtime ,UserType )
        values (#{id}, #{customerCode}, #{userName},#{password}, #{remark}, #{email},#{phone},#{customerServiceCode}, #{bindingtime} ,'1')
    </insert>

    <insert id="insertUserV2" parameterType="com.digiwin.escloud.userv2.user.model.user.User">
        insert into mars_user (ID, CustomerCode, Username,Password, Remark, Email,Phone,CustomerServiceCode, bindingtime ,UserType )
        values (#{id}, #{customerCode}, #{userName},#{password}, #{remark}, #{email},#{phone},#{customerServiceCode}, #{bindingtime} ,#{userType})
    </insert>

    <insert id="insertStaffAccount">
        insert into mars_staffaccount (userid, itcode, status,approverid, regesttime, approvertime)
        values (#{userId}, #{itcode}, '2','-1', now(),now())
    </insert>
    <select id="getUserByUserName" resultType="com.digiwin.escloud.userv2.user.model.user.User">
        select id, CustomerCode as customerCode, Username as userName, Remark as remark, Email as email, Phone as phone, CustomerServiceCode  as customerServiceCode
        from mars_user
        where 1=1 and Username = #{userName}
        limit 1
    </select>

    <select id="getUserByUserNameAndUserType" resultType="com.digiwin.escloud.userv2.user.model.user.User">
        select id, CustomerCode as customerCode, Username as userName, Remark as remark, Email as email, Phone as phone, CustomerServiceCode  as customerServiceCode
        from mars_user
        where 1=1 and Username = #{userName} and userType = #{userType}
        limit 1
    </select>

    <select id="getStaffUserByEmail" resultType="com.digiwin.escloud.userv2.user.model.user.User">
        select id, CustomerCode as customerCode, Username as userName, Remark as remark, Email as email, Phone as phone, CustomerServiceCode  as customerServiceCode
        from mars_user
        where email = #{email} and userType = #{userType}
        limit 1
    </select>
    <select id="getUserByUserId" resultType="com.digiwin.escloud.userv2.user.model.user.User">
        select id, CustomerCode as customerCode, Username as userName, Remark as remark, Email as email, Phone as phone, CustomerServiceCode  as customerServiceCode
        from mars_user
        where 1=1 and id = #{userId}
        limit 1
    </select>

    <select id="getUserRoleExist" resultType="java.lang.String">
        select ifnull(userid, '') as userid from mars_userrole where userid = #{userId} and role = #{role}
        limit 1
    </select>

    <insert id="insertUserRole">
        insert into mars_userrole (userid, role) values (#{userId}, #{role})
    </insert>

    <insert id="saveUserPersonalInfo" parameterType="com.digiwin.escloud.userv2.user.model.user.UserPersonalInfo">
        insert into mars_userpersonalinfo (userid, `name`, sex, birthday, idcardcode, email, phone, qq,
        postcode, postaddress, country, province, city, `language`, diploma, politytype, department,
        arrivaldate, quitdate, retiredate, img, serviceRegion, timeZone,jobCompetencyId,jobCompetencyOfInterestId,workno,departmentCode)
        values (#{userId}, #{name}, #{sex}, #{birthday}, #{idcardcode}, #{email}, #{phone}, #{qq},
        #{postcode}, #{postaddress}, #{country}, #{province}, #{city}, #{language}, #{diploma},
        #{politytype}, #{department}, #{arrivaldate}, #{quitdate}, #{retiredate}, #{imgBse64}, #{serviceRegion}, #{timeZone},
        #{jobCompetencyId},#{jobCompetencyOfInterestId},#{workno},#{departmentCode})
        ON DUPLICATE KEY UPDATE `name` = #{name} , sex = #{sex} , birthday = #{birthday} ,
        idcardcode = #{idcardcode} , email = #{email} , phone = #{phone} , qq = #{qq} ,
        postcode = #{postcode} , postaddress = #{postaddress} , country = #{country} , province = #{province} ,
        city = #{city} , `language` = #{language} , diploma = #{diploma} , politytype = #{politytype} ,
        department = #{department} , arrivaldate = #{arrivaldate} , quitdate =#{quitdate}  ,
        retiredate =  #{retiredate}, img = #{imgBse64}, serviceRegion = #{serviceRegion}, timeZone = #{timeZone},
        jobCompetencyId = #{jobCompetencyId},jobCompetencyOfInterestId = #{jobCompetencyOfInterestId},workno = #{workno},departmentCode = #{departmentCode}
    </insert>

    <select id="getUserAllRoles" resultType="com.digiwin.escloud.userv2.user.model.user.UserRole">
        select UserID as userId,Role as role
        from mars_userrole
        where UserID=#{userId}
    </select>

    <select id="getUserHasServiceCode" resultType="com.digiwin.escloud.userv2.user.model.user.UserHasServiceCode">
        select id, userId, customerServiceCode, joinTime, isDisplayTenant, isEffective, isApproved, isProcess, productCode
        from mars_user_has_servicecode
        where userId = #{userId} and customerServiceCode=#{serviceCode}
        limit 1
    </select>

    <insert id="insertUserHasServiceCode" useGeneratedKeys="true">
        insert into mars_user_has_servicecode(userId, customerServiceCode, joinTime, isDisplayTenant, isEffective, isApproved,isProcess, productCode)
        values (#{userId}, #{customerServiceCode}, current_timestamp, #{isDisplayTenant}, #{isEffective}, #{isApproved}, #{isProcess}, #{productCode})
    </insert>
    <update id="updateStaffUserName" parameterType="com.digiwin.escloud.userv2.user.model.user.User">
        update mars_user set userName = #{userName} where id = #{id}
    </update>
    <select id="selectStaffAccount" resultType="com.digiwin.escloud.userv2.user.model.staff.StaffAccount">
        select workno,departmentcode from mars_customerservicesatff where itcode = #{itcode} limit 1
    </select>
    <select id="getEServiceUser" resultType="java.lang.Integer">
        select count(*) from eserviceuser a where a.Email = #{email} and a.CustomerServiceCode = #{serviceCode}
    </select>
    <select id="getEServiceUserByEmail" resultType="com.digiwin.escloud.userv2.user.model.eservice.EServiceUser">
        select * from eserviceuser a where a.Email = #{email} limit 1
    </select>
    <insert id="insertEServiceUser" >
        insert into eserviceuser(guid,Email,`Name`,Phone,Pwd,Permission,LastActionTime,CustomerServiceCode,salt,`Language`)
        values (#{guid},#{email},#{name},#{phone},#{pwd},#{permission},#{lastActionTime},#{customerServiceCode},#{salt},#{language})
    </insert>
    <update id="updateEserviceAccount">
        insert into mars_eserviceaccount(userid,eserviceUserName)
        values (#{userId}, #{email})
        ON DUPLICATE KEY UPDATE eserviceUserName = #{email}
    </update>
</mapper>