package com.digiwin.escloud.aiocmdb.assetchange.event.listener;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiocmdb.assetchange.event.AssetChangeEmailEvent;
import com.digiwin.escloud.aiouser.model.user.CountingUser;
import com.digiwin.escloud.aiouser.model.user.UserTenantInfoResponse;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus.*;

/**
 * 资产变更邮件事件监听器
 * 在事务提交成功后异步发送邮件通知
 */
@Component
@Slf4j
public class AssetChangeEmailEventListener {

    private static final String ACTIVATE_APPROVER = "ACTIVATE_APPROVER";
    private static final String ACTIVATE_ACCEPTER = "ACTIVATE_ACCEPTER";

    // TODO: 注入邮件服务或API调用服务
    // @Autowired
    // private IAssetChangeEmailService emailService;
    @Autowired
    private AioUserFeignClient aioUserFeignClient;

    /**
     * 监听邮件发送事件
     * 使用@TransactionalEventListener确保在事务提交后执行
     * 使用@Async确保异步执行，不影响主业务性能
     */
    @Async("assetChangeEventExecutor")
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleEmailEvent(AssetChangeEmailEvent event) {
        try {
            log.info("开始处理资产变更邮件发送事件 - 申请单ID: {}, 申请单编号: {}, 操作类型: {}", 
                    event.getApplicationId(), event.getApplicationNumber(), event.getOperationType());
            
            // 根据操作类型发送不同的邮件
            switch (event.getOperationType()) {
                case NOT_APPROVED_NAME:
                    sendNotApproveEmail(event);
                    break;
                case NOT_ACCEPTED_NAME:
                    sendNotAcceptedEmail(event);
                    break;
                default:
                    sendCommonEmail(event);
                    log.warn("未知的邮件操作类型: {}", event.getOperationType());
            }
            
            log.info("资产变更邮件发送事件处理完成 - 申请单ID: {}", event.getApplicationId());
            
        } catch (Exception e) {
            log.error("处理资产变更邮件发送事件失败 - 申请单ID: {}, 错误信息: {}", 
                    event.getApplicationId(), e.getMessage(), e);
        }
    }

    /**
     * 调用邀请api
     */
    private void sendNotApproveEmail(AssetChangeEmailEvent event) {

        if (Objects.isNull(event.getOperatorInfo())) {
            log.error("申请人信息为空，无法发送邮件");
            return;
        }
        UserTenantInfoResponse userTenantInfoResponse = event.getOperatorInfo();
        CountingUser countingUser = new CountingUser();
        countingUser.setEmail(userTenantInfoResponse.getEmail());
        countingUser.setApplicationId(event.getApplicationId());
        countingUser.setTenantSid(userTenantInfoResponse.getTenantSid());
        countingUser.setTenantId(userTenantInfoResponse.getTenantId());
        countingUser.setServiceCode(userTenantInfoResponse.getTenantId());
        countingUser.setUserName(userTenantInfoResponse.getUserName());
        countingUser.setTelephone(userTenantInfoResponse.getTelephone());
        countingUser.setUserId(userTenantInfoResponse.getUserId());
        List<CountingUser> collect = Stream.of(countingUser).collect(Collectors.toList());
        log.info("发送审批邮件 - addUserScene: {},body:{}", ACTIVATE_APPROVER, JSONObject.toJSONString(collect));
        aioUserFeignClient.doIamUserAuthorized(ACTIVATE_APPROVER, collect);
    }

    /**
     * 调用邀请api
     */
    private void sendNotAcceptedEmail(AssetChangeEmailEvent event) {
        log.info("发送验收邮件 - 申请单: {}", event.getApplicationNumber());
        List<CountingUser> countingUsers = new ArrayList<>();
//        aioUserFeignClient.doIamUserAuthorized(ACTIVATE_ACCEPTER, countingUsers);

    }

    /**
     * 发送验收通过邮件
     */
    private void sendCommonEmail(AssetChangeEmailEvent event) {
        log.info("发送给申请人的邮件 - 申请单: {}", event.getApplicationNumber());
        
        // TODO: 实现具体的邮件发送逻辑
    }

    // TODO: 添加辅助方法
    // private String getApplicantEmail(Long applicationId) { ... }
    // private String buildApproveFailEmailContent(AssetChangeEmailEvent event) { ... }
    // private EmailRequest buildEmailRequest(AssetChangeEmailEvent event) { ... }
}
