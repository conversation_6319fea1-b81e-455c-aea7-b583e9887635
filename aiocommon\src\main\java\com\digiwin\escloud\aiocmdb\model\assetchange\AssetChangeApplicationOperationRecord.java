package com.digiwin.escloud.aiocmdb.model.assetchange;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class AssetChangeApplicationOperationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("申请单ID")
    private Long applicationId;

    @ApiModelProperty("执行记录id")
    private Long objId;

    @ApiModelProperty("操作人用户ID")
    private String operatorUserId;

    @ApiModelProperty("操作人名称")
    private String operatorName;

    @ApiModelProperty("操作时间")
    private LocalDateTime operationTime;

    @ApiModelProperty("下一处理人用户ID")
    private String nextUserId;

    @ApiModelProperty("下一处理人名称")
    private String nextUserName;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("操作意见")
    private String operationOpinion;

    @ApiModelProperty("操作意见说明")
    private String operationExplanation;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
