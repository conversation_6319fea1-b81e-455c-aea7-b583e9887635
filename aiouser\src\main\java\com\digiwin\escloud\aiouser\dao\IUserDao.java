package com.digiwin.escloud.aiouser.dao;

import com.digiwin.escloud.aiouser.model.common.Invitation;
import com.digiwin.escloud.aiouser.model.org.Department;
import com.digiwin.escloud.aiouser.model.supplier.SupplierContractDevice;
import com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee;
import com.digiwin.escloud.aiouser.model.user.*;
import com.digiwin.escloud.common.model.tenant.ServiceStaffRequest;
import com.digiwin.escloud.integration.api.iam.req.user.UpdatePasswordByAccountVO;
import com.digiwin.escloud.integration.api.iam.res.manager.role.RoleInfoRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.*;

@Mapper
public interface IUserDao {
    User getUserByEmail(@Param("email") String email);

    List<User> getUserByEmails(List<String> emails);

    List<Long> userNoticeContactByUserIdList(List<String> userIdList);

    int insertInvitation(Invitation invitation);

    List<Map<String, String>> getUserSuppliers(@Param("id") String id);

    Invitation getInvitation(long id);

    List<Invitation> getInvitationByInviteType(HashMap<String, Object> map);

    Invitation getLastInvitationByUserId(HashMap<String, Object> map);

    Invitation getLastInvitationByUserInviteType(HashMap<String, Object> map);

    List<Map<Long, Integer>> getActivateUserCnt(@Param("inviteType") String inviteType,
                                                @Param("eidList") List<Long> eidList);

    int setUserActivated(HashMap<String, Object> map);

    int setUserIsNew(HashMap<String, Object> map);

    int batchUpdateInvitationNoticeTime(@Param("ids") List<Long> ids,
                                        @Param("lastNoticeTime") LocalDateTime lastNoticeTime);

    List<SupplierEmployee> getEmployees(HashMap<String, Object> map);

    long getEmployeesCount(HashMap<String, Object> map);

    SupplierEmployeeInfo getSupplierEmployeeInfo(@Param("sid") long sid,
                                                 @Param("userSid") long userSid);
    SupplierEmployeeInfo getSupplierEmployeeInfoByName(String userName);

    UserPersonInfo getUserPersonalInfo(@Param("userSid") long userSid);

    int getEmployeeByWorkNo(HashMap<String, Object> map);

    Integer getEmployeeByEmail(HashMap<String, Object> map);

    Integer getEmployeeByCol(HashMap<String, Object> map);

    int insertEmployee(SupplierEmployee supplierEmployee);

    int updateEmployee(SupplierEmployee supplierEmployee);

    SupplierEmployee getSupplierEmployee(long id);

    User selectUser(@Param("sid") long userSid);

    int insertUser(User user);

    int insertUserTenantAttention(UserTenantAttention userTenantAttention);

    Integer checkUserCanAttention(HashMap<String, Object> map);

    Integer checkTenantIsAttention(HashMap<String, Object> map);

    int updateAttentionTenantReceiveWarningMail(HashMap<String, Object> map);

    int updateUser(User user);

    int updateUserWeChat(HashMap<String, Object> map);

    int insertUserTenantMap(UserTenantMap userTenantMap);

    int updateUserTenantMapProduct(HashMap<String, Object> map);

    int insertUserPersonalInfo(UserPersonalInfo userPersonalInfo);

    Long getOrgSid(HashMap<String, Object> map);

    List<SupplierEmployee> queryEmployeeList(@Param("sid") long sid,
                                             @Param("eid") long eid,
                                             @Param("productCode") String productCode,
                                             @Param("orgSid") long orgSid,
                                             @Param("userSid") long userSid,
                                             @Param("content") String content);

    /**
     * 新增访客信息
     *
     * @param visitorUser 访客信息
     * @return 影响笔数
     */
    int insertVisitorUser(@Param("visitorUser") VisitorUser visitorUser);

    /**
     * 新增访客信息
     *
     * @param sid     访客Sid
     * @param realSid 访客真实用户Sid
     * @return 影响笔数
     */
    int updateVisitorRealSidBySid(@Param("sid") Long sid, @Param("realSid") Long realSid);

    /**
     * 透过访客用户Sid查询访客信息
     *
     * @param sid 访客用户Sid
     * @return 访客信息
     */
    VisitorUser selectVisitorUserBySid(@Param("sid") Long sid);

    int updateEmployeeActive(@Param("id") long id,
                             @Param("userSid") long userSid);

    Integer getOrgEmployeesCount(@Param("eid") long eid,
                                 @Param("orgUri") String orgUri);

    String getUserEmail(String userId);

    int updateUserEmail(@Param("userId") String userId,
                        @Param("email") String email);

    User getUserByTelephone(@Param("telephone") String telephone);

    UserTenantMap getUserTenantMap(@Param("userSid") long userSid, @Param("eid") long eid);

    int updateUserDefaultSet(HashMap<String, Object> map);
    int updateUserDefaultEidAndSid(@Param("userSid") long userSid,
                                   @Param("defaultEid") long defaultEid,
                                   @Param("defaultEidSid") long defaultEidSid,
                                   @Param("defaultSid") long defaultSid,
                                   @Param("defaultSidEid") long defaultSidEid);

    List<String> getTpUserEmails();

    Integer getEmployeeActivated(String email);

    Map<String, String> getUserPairDefaultTenant(@Param("userSid") long userSid);

    List<UserTenantAttention> getTenantAndAttention(@Param("userSid") Long userSid, @Param("sid") Long sid, @Param("attention") Integer attention);

    List<UserTenantAttention> getTenantAndAttention4Org(@Param("orgSid") Long orgSid, @Param("attention") Integer attention);

    Integer getUserExist(long userSid);

    List<User> getAttentionUserByEid(String eid);

    List<User> getAttentionAndReceiveWarningMailUserByEid(String eid);

    List<UserTenantAttention> getUserTenantAttentions(HashMap<String, Object> map);

    int getUserTenantAttentionsCount(HashMap<String, Object> map);

    List<SupplierEmployee> getEmployeeBasicInfo(HashMap<String, Object> map);

    int getEmployeeBasicInfoCount(HashMap<String, Object> map);

    int batchSaveUserTenantAttention(List<UserTenantAttention> userTenantAttention);

    List<SupplierEmployee> getTenantAttentionUsers(HashMap<String, Object> map);

    int getTenantAttentionUsersCount(HashMap<String, Object> map);

    List<SupplierEmployee> getEmployeesByDept(HashMap<String, Object> map);

    String getPwd(String userId);

    Integer getUserExistedById(String userId);

    User getUserById(String userId);

    int updatePassword(UpdatePasswordByAccountVO updatePasswordByAccountVO);

    Integer getEmailExist(String email);

    Integer getTelephoneExist(String telephone);

    Integer getUserIdExist(String userId);

    /**
     * 依据用户Sid列表查询用户名称字典
     * @param userSidList 用户Sid列表
     * @return 字典列表
     */
    List<Map<String, Object>> selectUserNameBySidList(@Param("userSidList") List<Long> userSidList);

    /**
     * 依据工号获取员工用户Sid
     * @param sid 运维商Id
     * @param workNo 工号
     * @return 用户Sid
     */
    Long selectSeUserSidByWorkNo(@Param("sid") Long sid, @Param("workNo") String workNo);

    int updateUserDefaultSupplier(User user);

    int updateSupplierContractDevice(SupplierContractDevice supplierContractDevice);

    Map<String ,Object> getISVAuthorizeCount(Map<String ,Object> map);

    Optional<MarsUser> getMarsUser(@Param("eid") String eid,
                                   @Param("dbName") String dbName);


    /**
     * 批量添加关注租户
     *
     * @param userTenantAttentionList
     */
    void batchInsertUserTenantAttention(List<UserTenantAttention> userTenantAttentionList);
    List<PermissionRole> selectDataAuthRole(@Param("eid")Long eid,@Param("roleCodeList") List<String> roleCodeList,@Param("sid")Long sid);
    List<PermissionRole> selectDataAuthRoleDetail(@Param("eid")Long eid,@Param("roleCodeList") List<String> roleCodeList,@Param("sid")Long sid);

    int batchInsertOrUpdateRole(@Param("eid") long eid, @Param("sid") long sid,
                                @Param("roleInfoResList") List<RoleInfoRes> roleInfoResList);

    List<PermissionRole> getRoleByEid(@Param("eid") long eid, @Param("sid") long sid);

    List<Department> selectDepartment();

    int updateEmployeeDeptIds(@Param("id") long id, @Param("deptIds") String deptIds);
    List<SupplierEmployee> selectDeptInfoByUserId(String userId);
    List<Department> selectManagerDepartmentByDeptIdList(@Param("deptIdList") Set<String> deptIdList);
    List<Department> selectSelfDepartmentByUserId(String userId);

    int updateEmployeeNotActive(@Param("id") long id);

    int updateRolePriority(PermissionRole role);
    ModuleStaffSyncMapping selectModuleStaffSyncMapping(@Param("staffId")Long staffId,@Param("moduleId") Long moduleId);
    List<User> selectModuleStaff(@Param("request") ServiceStaffRequest request, @Param("escloudDBName")String escloudDBName);
    int insertOrUpdateModuleStaffSyncMapping(ModuleStaffSyncMapping staffSyncMapping);
    int deleteModuleStaffSyncMapping(@Param("staffId")Long staffId,@Param("moduleId") Long moduleId);
}
