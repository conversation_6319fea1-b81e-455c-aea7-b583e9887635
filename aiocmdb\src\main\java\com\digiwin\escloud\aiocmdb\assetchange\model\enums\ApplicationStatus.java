package com.digiwin.escloud.aiocmdb.assetchange.model.enums;

import lombok.Getter;

/**
 * 申请单状态
 * 未提交、未审批、审批退回、未执行、执行中、未验收、验收退回、已结案
 */
@Getter
public enum ApplicationStatus {

    /**
     * 未提交
     */
    NOT_SUBMITTED("未提交"),


    /**
     * 未审批
     */
    NOT_APPROVED("未审批"),
    /**
     * 审批退回
     */
    APPROVAL_RETURNED("审批退回"),
    /**
     * 未执行
     */
    NOT_EXECUTED("未执行"),
    /**
     * 执行中
     */
    IN_EXECUTION("执行中"),
    /**
     * 未验收
     */
    NOT_ACCEPTED("未验收"),
    /**
     * 验收退回
     */
    ACCEPTANCE_RETURNED("验收退回"),
    /**
     * 已结案
     */
    CLOSED("已结案");

    public static final String NOT_SUBMITTED_NAME = "NOT_SUBMITTED";
    public static final String NOT_APPROVED_NAME = "NOT_APPROVED";
    public static final String APPROVAL_RETURNED_NAME = "APPROVAL_RETURNED";
    public static final String NOT_EXECUTED_NAME = "NOT_EXECUTED";
    public static final String IN_EXECUTION_NAME = "IN_EXECUTION";
    public static final String NOT_ACCEPTED_NAME = "NOT_ACCEPTED";
    public static final String ACCEPTANCE_RETURNED_NAME = "ACCEPTANCE_RETURNED";
    public static final String CLOSED_NAME = "CLOSED";

    private final String label;

    ApplicationStatus(String label) {
        this.label = label;
    }



    /**
     * 根据字符串获取对应的枚举值
     *
     * @param value 枚举名称或标签
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ApplicationStatus fromString(String value) {
        if (value == null) {
            return null;
        }

        // 先尝试按枚举名称匹配
        for (ApplicationStatus status : ApplicationStatus.values()) {
            if (status.name().equals(value)) {
                return status;
            }
        }

        // 再尝试按标签匹配
        for (ApplicationStatus status : ApplicationStatus.values()) {
            if (status.getLabel().equals(value)) {
                return status;
            }
        }

        return null;
    }


    /**
     * 判断传入的字符串是否与指定的枚举值相等
     *
     * @param value  要比较的字符串（可以是枚举名称或标签）
     * @param status 指定的枚举值
     * @return 如果匹配则返回true，否则返回false
     */
    public static boolean isEqual(String value, ApplicationStatus status) {
        if (value == null || status == null) {
            return false;
        }
        return status.name().equals(value) || status.getLabel().equals(value);
    }

}