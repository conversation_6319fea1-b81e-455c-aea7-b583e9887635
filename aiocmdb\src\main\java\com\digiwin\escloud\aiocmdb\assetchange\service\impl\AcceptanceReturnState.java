package com.digiwin.escloud.aiocmdb.assetchange.service.impl;

import com.digiwin.escloud.aiocmdb.assetchange.exception.BusinessValidationException;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.aiocmdb.assetchange.service.AssetChangeExecutePlanService;
import com.digiwin.escloud.aiocmdb.assetchange.service.BaseChangeRequestState;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component(ApplicationStatus.ACCEPTANCE_RETURNED_NAME)
public class AcceptanceReturnState extends BaseChangeRequestState {

    @Autowired
    private AssetChangeExecutePlanService assetChangeExecutePlanService;

    @Override
    public String getStateName() {
        return ApplicationStatus.ACCEPTANCE_RETURNED.name();
    }

    @Override
    public void execute(AssetChangeApplication context) {
        context.setApplicationStatus(ApplicationStatus.IN_EXECUTION.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.IN_EXECUTION.name()));
    }

    @Override
    public void submitAcceptance(AssetChangeApplication context) {
        if (assetChangeExecutePlanService.getExecutionComplete(context.getId()) == 100) {
            throw new BusinessValidationException("执行计划尚未全部完成，无法提交验收！");
        }
        context.setApplicationStatus(ApplicationStatus.NOT_ACCEPTED.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.NOT_ACCEPTED.name()));
    }
}

