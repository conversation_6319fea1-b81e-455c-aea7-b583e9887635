package com.digiwin.escloud.aioitms.collectconfig.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("收集项配置")
@Data
public class CollectConfig {
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("作用域")
    private String scopeId="DefaultConfig";
    @ApiModelProperty("收集项编号")
    private String collectCode;
    @ApiModelProperty("收集项名称")
    private String collectName;
    @ApiModelProperty("收集项类别")
    private String collectCategory;
    @ApiModelProperty("收集项设备类型")
    private String collectDeviceType;
    @ApiModelProperty("收集项描述")
    private String description;
    @ApiModelProperty("收集项md5版本")
    private String collectVersion;
    @ApiModelProperty("执行元件-收集器类型")
    private CollectType collectType;
    @ApiModelProperty("执行元件-收集器模型编号")
    private String collectorModelCode;
    @ApiModelProperty("执行元件-收集器json节点内容")
    private String collectorContent;
    @ApiModelProperty("执行元件-解析器模型编号")
    private String parserModelCode;
    @ApiModelProperty("执行元件-解析器json节点内容")
    private String parserContent;
    @ApiModelProperty("执行元件-转换器json节点内容")
    private String transformsContent;
    @ApiModelProperty("执行元件-发送器模型编号")
    private String sendersModelCode;
    @ApiModelProperty("执行元件-发送器json节点内容")
    private String sendersContent;
    @ApiModelProperty("是否是必填(系统级别)")
    private Boolean isSystem;
    @ApiModelProperty("是否启用")
    private Boolean isEnable;
    @ApiModelProperty("是否当删除应用服务后从设备上解绑")
    private Boolean isDeletedByAppServiceUnbind;
    @ApiModelProperty("数据上传模型编号")
    private String uploadDataModelCode;
    @ApiModelProperty("执行参数模型编号")
    private String execParamsModelCode;
    @ApiModelProperty("执行间隔")
    private Integer interval;
    @ApiModelProperty("cron值")
    private String cron;
    @ApiModelProperty("上传url末端路径")
    private String uploadUrl;
    @ApiModelProperty("运行平台(多个以分号(;)隔开)")
    private String platform;
    @ApiModelProperty("文件参数名称(多组以逗号(,)间隔)")
    private String fileParamNames;
    @ApiModelProperty("上传执行追踪模式")
    private Integer uploadRunTrackMode;
    @ApiModelProperty("来源收集项Id")
    private Long sourceAccId;
    @ApiModelProperty("运行版本")
    private String runtimeVersion;
    @ApiModelProperty("异动版本")
    private LocalDateTime __version__;
    @ApiModelProperty("是否已启用边缘存储")
    private Integer isEdgeStorageEnabled;

    @ApiModelProperty("是否有执行参数")
    public boolean getHasExecParams(){
        return StringUtils.isNotBlank(this.execParamsModelCode);
    }

    @ApiModelProperty("转换器列表")
    private List<AiopsTransformModel> transformModelList;
    @JsonIgnore
    @ApiModelProperty(value = "设备收集项详情主键Id", hidden = true)
    private Long adcdId;
    @JsonIgnore
    @ApiModelProperty(value = "执行参数内容(已加密的内容)", hidden = true)
    private String execParamsContent;
    @JsonIgnore
    @ApiModelProperty(value = "执行参数Md5版本", hidden = true)
    private String execParamsVersion;
    @JsonIgnore
    @ApiModelProperty(value = "设备设置的收集项名称", hidden = true)
    private String adcdCollectName;
    @JsonIgnore
    @ApiModelProperty(value = "运维实例Id", hidden = true)
    private Long aiId;
    @JsonIgnore
    @ApiModelProperty(value = "运维项目", hidden = true)
    private String aiopsItem;
    @ApiModelProperty(value = "是否固定Id，内部使用", hidden = true)
    @JsonIgnore
    private Boolean isFixedId;

    @ApiModelProperty("1:下发立即执行 2:依照间隔时间执行")
    private Integer execStrategyStatus;

    @ApiModelProperty("返回异常用")
    private String errorMsg;

    /**
     *  uploadDataModelCode 对应得starrocks 得 表名
     */
    private String sinkName;
    private String aiopsItemName;
}
