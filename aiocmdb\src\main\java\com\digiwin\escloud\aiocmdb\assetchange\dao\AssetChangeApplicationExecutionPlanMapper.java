package com.digiwin.escloud.aiocmdb.assetchange.dao;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationExecutionPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 执行计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Mapper
public interface AssetChangeApplicationExecutionPlanMapper {

    /**
     * 插入执行计划
     *
     * @param executionPlan 执行计划
     * @return 影响行数
     */
    int insert(AssetChangeApplicationExecutionPlan executionPlan);

    /**
     * 批量插入执行计划
     *
     * @param executionPlanList 执行计划列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AssetChangeApplicationExecutionPlan> executionPlanList);

    /**
     * 根据申请单ID删除执行计划
     *
     * @param applicationId 申请单ID
     * @return 影响行数
     */
    int deleteByApplicationId(@Param("applicationId") Long applicationId);

    /**
     * 根据申请单ID查询执行计划
     *
     * @param applicationId 申请单ID
     * @return 执行计划列表
     */
    List<AssetChangeApplicationExecutionPlan> selectByApplicationId(@Param("applicationId") Long applicationId);

    /**
     * 根据ID更新执行计划
     *
     * @param executionPlan 执行计划
     * @return 影响行数
     */
    int updateById(AssetChangeApplicationExecutionPlan executionPlan);
}
