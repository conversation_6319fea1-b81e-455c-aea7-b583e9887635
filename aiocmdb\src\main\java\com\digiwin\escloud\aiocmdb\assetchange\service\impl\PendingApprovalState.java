package com.digiwin.escloud.aiocmdb.assetchange.service.impl;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.aiocmdb.assetchange.service.BaseChangeRequestState;
import org.springframework.stereotype.Component;

import static com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus.NOT_APPROVED_NAME;

@Component(NOT_APPROVED_NAME)
public class PendingApprovalState extends BaseChangeRequestState {
    @Override
    public String getStateName() {
        return ApplicationStatus.NOT_APPROVED.name();
    }

    @Override
    public void approvePass(AssetChangeApplication context) {
        context.setApplicationStatus(ApplicationStatus.NOT_EXECUTED.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.NOT_EXECUTED.name()));
    }

    @Override
    public void approveFail(AssetChangeApplication context) {
        context.setApplicationStatus(ApplicationStatus.CLOSED.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.CLOSED.name()));
    }

    @Override
    public void approveAdjust(AssetChangeApplication context) {
        // “审批退回”实际上是让流程回到“审批退回”状态，让用户可以重新编辑和提交
        context.setApplicationStatus(ApplicationStatus.APPROVAL_RETURNED.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.APPROVAL_RETURNED.name()));
    }
}
