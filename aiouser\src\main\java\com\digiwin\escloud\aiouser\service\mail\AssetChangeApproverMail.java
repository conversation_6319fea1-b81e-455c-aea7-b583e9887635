package com.digiwin.escloud.aiouser.service.mail;

import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.aiocmdb.model.assetchange.AssetChangeApplication;
import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.aiouser.annotation.MailType;
import com.digiwin.escloud.aiouser.model.common.Invitation;
import com.digiwin.escloud.aiouser.util.CommonMailService;
import com.digiwin.escloud.aiouser.util.MessageUtils;
import com.digiwin.escloud.common.feign.AioCmdbFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.util.LongUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

@MailType("AssetChangeApprover")
@Slf4j
@Service
public class AssetChangeApproverMail implements IMailService<AssetChangeApplication> {

    @Value("${digiwin.user.defaultlanguage}")
    private String defaultLanguage;
    @Autowired
    private CommonMailService commonMailService;
    @Autowired
    private MessageUtils messageUtils;
    
    @Autowired
    private AioCmdbFeignClient aioCmdbFeignClient;

    @Override
    public String[] prepareMail(AssetChangeApplication application, Supplier<Map<String, Object>> supplierOtherInfo) {
        String language = defaultLanguage;
        String userId = application.getApplicantUserId();
        if (StringUtils.isEmpty(userId)) {
            log.info(" active approver email is null");
            return null;
        }

        // 检查是否为资产变更审批邮件
        if (LongUtil.isEmpty(application.getId())) {
            log.error("准备资产变更审批邮件失败，申请单为空");
            return null;
        }


        return prepareAssetChangeApprovalMail(application, language);
    }

    /**
     * 准备资产变更审批邮件
     */
    private String[] prepareAssetChangeApprovalMail(AssetChangeApplication application, String language) {
        try {

            log.info("准备资产变更审批邮件，申请单ID: {}", application.getId());

            String invitedEmail = application.getCountingUser().getEmail();

            // 构建申请说明
            String applicationDescription = buildApplicationDescription(application, language);

            // 读取邮件模板
            String assetChangeApprovalStr = commonMailService.readMailContent("assetChangeApprovalMail.html", language);

            // 格式化邮件内容
            String mailMsg = String.format(assetChangeApprovalStr,
                    application.getApplicantName() != null ? application.getApplicantName() : "", // 收件人姓名
                "固定连接",                                    // 审批链接
                application.getApplicationNumber(),         // 申请单编号
                application.getApplicantUnit(),             // 申请单位
                application.getApplicantName(),             // 申请人
                application.getApplicationDate(),           // 申请日期
                application.getApplicationCategory(),       // 申请类别
                applicationDescription                                      // 申请说明
            );

            // 组织发送邮件
            List<String> receivers = new ArrayList<>();
            receivers.add(invitedEmail);
            String subject = messageUtils.get("ActiveApproverSubject", language);
            String subTitle = messageUtils.get("ActiveApproverSubTitle", language);
            Mail mail = new Mail();
            mail.setSubject(subject);
            mail.setSubtitle(subTitle);
            mail.setMessage(mailMsg);
            mail.setReceivers(receivers);
            mail.setMailSourceType(MailSourceType.ActiveApprover);
            mail.setUrl("");
            mail.setSourceId(Long.toString(application.getId()));
            mail.setPriority(1);
            mail.setLanguage(language);

            return new String[]{JSON.toJSONString(mail)};

        } catch (Exception e) {
            log.error("准备资产变更审批邮件失败", e);
            return null;
        }
    }

    /**
     * 构建申请说明
     */
    private String buildApplicationDescription(AssetChangeApplication application,
                                               String language) {
        StringBuilder description = new StringBuilder();
        String changeScope = messageUtils.get("ChangeScope", language);
        String changeContentDescription = messageUtils.get("ChangeContentDescription", language);
        String changeBackgroundAndReason = messageUtils.get("ChangeBackgroundAndReason", language);
        String changePriority = messageUtils.get("ChangePriority", language);
        // 变更优先级
        if (StringUtils.hasText(application.getChangePriority())) {
            description.append("<div style='margin-bottom: 10px;'>");
            description.append("<strong style='color: #d32f2f;'>").append(changePriority).append("</strong><br>");
            description.append("<span style='margin-left: 20px;'>").append(application.getChangePriority()).append("</span>");
            description.append("</div>");
        }

        // 变更背景与原因
        if (StringUtils.hasText(application.getChangeBackgroundReason())) {
            description.append("<div style='margin-bottom: 10px;'>");
            description.append("<strong>").append(changeBackgroundAndReason).append("</strong><br>");
            description.append("<span style='margin-left: 20px;'>").append(application.getChangeBackgroundReason()).append("</span>");
            description.append("</div>");
        }

        // 变更内容描述
        if (StringUtils.hasText(application.getChangeContentDescription())) {
            description.append("<div style='margin-bottom: 10px;'>");
            description.append("<strong>").append(changeContentDescription).append("</strong><br>");
            description.append("<span style='margin-left: 20px;'>").append(application.getChangeContentDescription()).append("</span>");
            description.append("</div>");
        }

        // 变更范围
        if (StringUtils.hasText(application.getChangeRange())) {
            description.append("<div style='margin-bottom: 10px;'>");
            description.append("<strong>").append(changeScope).append("</strong><br>");
            description.append("<span style='margin-left: 20px;'>").append(application.getChangeRange()).append("</span>");
            description.append("</div>");
        }

        return description.length() > 0 ? description.toString() : "";
    }
}