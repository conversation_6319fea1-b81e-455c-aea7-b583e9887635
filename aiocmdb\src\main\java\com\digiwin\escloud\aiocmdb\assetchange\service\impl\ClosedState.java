package com.digiwin.escloud.aiocmdb.assetchange.service.impl;

import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.aiocmdb.assetchange.service.BaseChangeRequestState;
import org.springframework.stereotype.Component;

@Component(ApplicationStatus.CLOSED_NAME)
public class ClosedState extends BaseChangeRequestState {
    @Override
    public String getStateName() {
        return ApplicationStatus.CLOSED.name();
    }
    // 已结案状态，所有操作都无效，继承基类即可
}
