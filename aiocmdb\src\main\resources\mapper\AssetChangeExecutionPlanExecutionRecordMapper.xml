<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeExecutionPlanExecutionRecordMapper">

    <!-- 插入执行记录 -->
    <insert id="insert" parameterType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeExecutionPlanExecutionRecord">
        INSERT INTO asset_change_execution_plan_execution_record (
            id, planId, executorUserId, executorName, planStatus, actualPlanStartDate, actualPlanEndDate,
            systemPreChangeVersion, systemPreChangeDescription, systemPostChangeVersion, 
            systemPostChangeDescription, executionExplanation, createTime, updateTime
        ) VALUES (
            #{id}, #{planId}, #{executorUserId}, #{executorName}, #{planStatus}, #{actualPlanStartDate}, #{actualPlanEndDate},
            #{systemPreChangeVersion}, #{systemPreChangeDescription}, #{systemPostChangeVersion},
            #{systemPostChangeDescription}, #{executionExplanation}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入执行记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO asset_change_execution_plan_execution_record (
            id, planId, executorUserId, executorName, planStatus, actualPlanStartDate, actualPlanEndDate,
            systemPreChangeVersion, systemPreChangeDescription, systemPostChangeVersion, 
            systemPostChangeDescription, executionExplanation, createTime, updateTime
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.planId}, #{item.executorUserId}, #{item.executorName}, #{item.planStatus}, 
             #{item.actualPlanStartDate}, #{item.actualPlanEndDate}, #{item.systemPreChangeVersion}, 
             #{item.systemPreChangeDescription}, #{item.systemPostChangeVersion}, #{item.systemPostChangeDescription}, 
             #{item.executionExplanation}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据计划ID删除执行记录 -->
    <delete id="deleteByPlanId" parameterType="long">
        DELETE FROM asset_change_execution_plan_execution_record WHERE planId = #{planId}
    </delete>

    <!-- 根据计划ID列表批量删除执行记录 -->
    <delete id="deleteByPlanIdList" parameterType="java.util.List">
        DELETE FROM asset_change_execution_plan_execution_record 
        WHERE planId IN
        <foreach collection="planIdList" item="planId" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </delete>

    <!-- 根据计划ID查询执行记录 -->
    <select id="selectByPlanId" parameterType="long" resultType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeExecutionPlanExecutionRecord">
        SELECT * FROM asset_change_execution_plan_execution_record 
        WHERE planId = #{planId}
        ORDER BY actualPlanStartDate ASC
    </select>

    <!-- 根据计划ID列表查询执行记录 -->
    <select id="selectByPlanIdList" parameterType="java.util.List" resultType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeExecutionPlanExecutionRecord">
        SELECT * FROM asset_change_execution_plan_execution_record 
        WHERE planId IN
        <foreach collection="planIdList" item="planId" open="(" separator="," close=")">
            #{planId}
        </foreach>
        ORDER BY planId, actualPlanStartDate ASC
    </select>

</mapper>
