package com.digiwin.escloud.aiocmdb.assetchange.dao;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeExecutionPlanExecutionRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 执行记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Mapper
public interface AssetChangeExecutionPlanExecutionRecordMapper {

    /**
     * 插入执行记录
     *
     * @param executionRecord 执行记录
     * @return 影响行数
     */
    int insert(AssetChangeExecutionPlanExecutionRecord executionRecord);

    /**
     * 批量插入执行记录
     *
     * @param executionRecordList 执行记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AssetChangeExecutionPlanExecutionRecord> executionRecordList);

    /**
     * 根据计划ID删除执行记录
     *
     * @param planId 计划ID
     * @return 影响行数
     */
    int deleteByPlanId(@Param("planId") Long planId);

    /**
     * 根据计划ID列表批量删除执行记录
     *
     * @param planIdList 计划ID列表
     * @return 影响行数
     */
    int deleteByPlanIdList(@Param("planIdList") List<Long> planIdList);

    /**
     * 根据计划ID查询执行记录
     *
     * @param planId 计划ID
     * @return 执行记录列表
     */
    List<AssetChangeExecutionPlanExecutionRecord> selectByPlanId(@Param("planId") Long planId);

    /**
     * 根据计划ID列表查询执行记录
     *
     * @param planIdList 计划ID列表
     * @return 执行记录列表
     */
    List<AssetChangeExecutionPlanExecutionRecord> selectByPlanIdList(@Param("planIdList") List<Long> planIdList);
}
