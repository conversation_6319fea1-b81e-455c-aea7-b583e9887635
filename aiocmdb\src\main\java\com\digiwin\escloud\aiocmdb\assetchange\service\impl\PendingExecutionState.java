package com.digiwin.escloud.aiocmdb.assetchange.service.impl;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.aiocmdb.assetchange.service.BaseChangeRequestState;
import org.springframework.stereotype.Component;

@Component(ApplicationStatus.NOT_EXECUTED_NAME)
public class PendingExecutionState extends BaseChangeRequestState {
    @Override
    public String getStateName() {
        return ApplicationStatus.NOT_EXECUTED.name();
    }

    @Override
    public void execute(AssetChangeApplication context) {
        context.setApplicationStatus(ApplicationStatus.IN_EXECUTION.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.IN_EXECUTION.name()));
    }
}
