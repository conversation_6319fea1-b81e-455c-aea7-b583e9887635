package com.digiwin.escloud.aiouser.service.iamapp.impl.user;

import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.aiouser.dao.ITenantDao;
import com.digiwin.escloud.aiouser.dao.IUserDao;
import com.digiwin.escloud.aiouser.model.common.Invitation;
import com.digiwin.escloud.aiouser.model.common.InviteType;
import com.digiwin.escloud.aiouser.model.customer.CustomerServiceInfo;
import com.digiwin.escloud.aiouser.model.tenant.Tenant;
import com.digiwin.escloud.aiouser.model.user.CountingUser;
import com.digiwin.escloud.aiouser.util.CommonUtils;
import com.digiwin.escloud.aiouser.util.MailUtils;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.integration.api.iam.req.user.IamAuthoredUser;
import com.digiwin.escloud.integration.service.IamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2025-05-19 17:43
 * @Description
 */
@Slf4j
@Service("ACTIVATE_USER")
public class ActivateUserService extends ImportUserService {

    @Resource
    private ITenantDao tenantDao;
    @Resource
    private CommonUtils commonUtils;
    @Resource
    private IamService iamService;
    @Resource
    private MailUtils mailUtils;
    @Resource
    private IUserDao userDao;
    @Value("${digiwin.aio.user.activate.linkurl}")
    private String userActivateUrl;
    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private Long defaultSid;
    @Value("#{'${digiwin.cust.level.list:DA,DB,DC}'.split(',')}")
    private List<String> custLevelList;

    protected Invitation buildInvitation(CountingUser countingUser, String inviterName, Long inviterUserSid) {
        Invitation invitation = new Invitation();
        long id = SnowFlake.getInstance().newId();
        invitation.setId(id);
        invitation.setInviterName(inviterName);
        invitation.setInviterUserSid(inviterUserSid);
        invitation.setInvitedEid(countingUser.getTenantSid());
        Tenant tenant = tenantDao.getTenant(countingUser.getTenantSid());
        if (tenant != null) {
            String tenantName = StringUtils.isEmpty(tenant.getCustomerFullNameCH()) ? tenant.getCustomerFullNameCH() : tenant.getName();
            invitation.setInvitedTenantName(tenantName);
        }
        invitation.setInvitedTenantId(countingUser.getTenantId());
        invitation.setInvitationDate(new Date());
        invitation.setInvitedSid(defaultSid);
        invitation.setInviteType(InviteType.ACTIVATE.toString());
        invitation.setInvitedServiceCode(countingUser.getServiceCode());
        invitation.setInvitedName(countingUser.getUserName());
        invitation.setInvitedEmail(countingUser.getEmail());
        invitation.setInvitedPhone(countingUser.getTelephone());
        invitation.setLinkUrl(userActivateUrl + "?invitationId=" + id);
        invitation.setInvitedUserId(countingUser.getUserId());

        HashMap<String, Object> map = new HashMap<>();
        map.put("invitedUserId", countingUser.getUserId());
        map.put("inviteType", InviteType.ACTIVATE.toString());
        Invitation lastInvitation = userDao.getLastInvitationByUserId(map);
        if (lastInvitation != null) {
            invitation.setNewUser(lastInvitation.isNewUser());
        } else {
            invitation.setNewUser(countingUser.isNewUser());
        }
        invitation.setActivated(false);
        invitation.setLastNoticeTime(LocalDateTime.now());
        return invitation;
    }

    protected void sendMail(Invitation invitation) {
        mailUtils.sendMail(MailSourceType.ActivateUser.toString(), invitation);
    }

    @Override
    protected void afterUserAuthorized(List<CountingUser> countingUsers) {
        if (CollectionUtils.isEmpty(countingUsers)) {
            return;
        }
        String headerToken = RequestUtil.getHeaderToken();
        commonUtils.asyncRun(() -> {
            String inviterName = null;
            Long inviterUserSid = null;
            if (!StringUtils.isEmpty(headerToken)) {
                IamAuthoredUser iamAuthoredUser = iamService.analyzeToken(headerToken);
                if (!ObjectUtils.isEmpty(iamAuthoredUser)) {
                    inviterName = iamAuthoredUser.getUserName();
                    inviterUserSid = iamAuthoredUser.getSid();
                }
            }
            for (CountingUser countingUser : countingUsers) {
                if (!countingUser.isAuthorized()) {
                    continue;
                }
                Invitation invitation = buildInvitation(countingUser, inviterName, inviterUserSid);
                boolean res = userDao.insertInvitation(invitation) > 0;
                if (res) {
                    sendMail(invitation);
                }
            }
        });
    }

    protected BaseResponse otherCheck(List<CountingUser> countingUsers,BaseResponse response) {
        if (!ResponseCode.SUCCESS.isSameCode(response.getCode())) {
            return response;
        }
        String serviceCode = countingUsers.get(0).getServiceCode();
        String productCode = countingUsers.get(0).getProductCode();
        CustomerServiceInfo customerServiceInfo = tenantDao.getCustomerServiceInfo(serviceCode, productCode);
        if (ObjectUtils.isEmpty(customerServiceInfo)) {
            return BaseResponse.error(ResponseCode.CUST_LEVEL_IS_EMPTY);
        }
        String custLevel = customerServiceInfo.getCustLevel();
        if (StringUtils.isEmpty(custLevel)) {
            return BaseResponse.error(ResponseCode.CUST_LEVEL_IS_EMPTY);
        }
        if (!custLevelList.contains(custLevel)) {
            return BaseResponse.error(ResponseCode.CUST_LEVEL_IS_EMPTY);
        }
        return response;
    }

    @Override
    protected BaseResponse beforeUserAuthorized(List<CountingUser> countingUsers) {
        BaseResponse response = super.beforeUserAuthorized(countingUsers);
        return otherCheck(countingUsers, response);
    }
}
