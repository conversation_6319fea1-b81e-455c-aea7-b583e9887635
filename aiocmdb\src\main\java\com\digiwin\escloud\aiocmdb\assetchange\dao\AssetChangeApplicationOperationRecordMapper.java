package com.digiwin.escloud.aiocmdb.assetchange.dao;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationOperationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 操作记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Mapper
public interface AssetChangeApplicationOperationRecordMapper {

    /**
     * 插入操作记录
     *
     * @param operationRecord 操作记录
     * @return 影响行数
     */
    int insert(AssetChangeApplicationOperationRecord operationRecord);

    /**
     * 批量插入操作记录
     *
     * @param operationRecordList 操作记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AssetChangeApplicationOperationRecord> operationRecordList);

    /**
     * 根据申请单ID删除操作记录
     *
     * @param applicationId 申请单ID
     * @return 影响行数
     */
    int deleteByApplicationId(@Param("applicationId") Long applicationId);

    /**
     * 根据申请单ID查询操作记录
     *
     * @param applicationId 申请单ID
     * @return 操作记录列表
     */
    List<AssetChangeApplicationOperationRecord> selectByApplicationId(@Param("applicationId") Long applicationId);

    /**
     * 根据申请单ID查询操作记录（按时间倒序）
     *
     * @param applicationId 申请单ID
     * @return 操作记录列表
     */
    List<AssetChangeApplicationOperationRecord> selectByApplicationIdOrderByTimeDesc(@Param("applicationId") Long applicationId);
}
