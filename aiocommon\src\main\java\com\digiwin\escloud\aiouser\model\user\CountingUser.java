package com.digiwin.escloud.aiouser.model.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023-02-06 18:30
 * @Description
 */
@Data
public class CountingUser {
    private long userSid; // 不穿
    private String userId; //email
    private String userName;
    private long tenantSid;
    private String tenantId;
    private String serviceCode; // 校验必须要有
    private String guiNumber = ""; // 台湾校验必须要有
    private String comeFrom;
    private boolean inTenant;
    private boolean newUser;
    private String productCode;// 前端 163 147
    private String email;
    private String telephone;
    private String roleId;
    private byte[] passwordHash; //加密密码 有
//    @JsonProperty("authorized")
    private boolean authorized = true;
    private Long applicationId ;

}
