package com.digiwin.escloud.common.model.operateLog;

import com.digiwin.escloud.common.util.DateUtil;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperateLogSaveParam {
    // 提醒: DUPLICATE KEY(`startTime`, `eid`)
    private String startTime;
    private String eid;
    private String deviceId;
    private String uploadDataModelCode;
    private Long userSid;
    private String userName;
    private String operateId;
    private String operateType;
    private String endTime;
    private Map<String, Object> operateContent;
    private String operateResult;
    private Long sid;
    private String sourceDbId;

    public void setStartTime(String startTime) {
        LocalDateTime localDateTime = DateUtil.tryParseLocalDateTime(startTime).orElse(null);
        this.startTime = DateUtil.getSomeDateFormatString(localDateTime, DATE_TIME_FORMATTER);
    }

    public void setEndTime(String endTime) {
        LocalDateTime localDateTime = DateUtil.tryParseLocalDateTime(endTime).orElse(null);
        this.startTime = DateUtil.getSomeDateFormatString(localDateTime, DATE_TIME_FORMATTER);
    }

}
