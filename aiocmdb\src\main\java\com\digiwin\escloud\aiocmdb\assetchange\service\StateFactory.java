package com.digiwin.escloud.aiocmdb.assetchange.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class StateFactory {

    @Autowired
    private ApplicationContext applicationContext;

    public ChangeRequestState getState(String stateType) {
        // 动态获取状态Bean，避免循环依赖
        return applicationContext.getBean(stateType, ChangeRequestState.class);
    }
}
