package com.digiwin.escloud.aiocmdb.assetchange.event.listener;

import com.digiwin.escloud.aiocmdb.assetchange.event.AssetChangeReportEvent;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationReportRecord;
import com.digiwin.escloud.aiocmdb.assetchange.service.IAssetChangeReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 资产变更报告事件监听器
 * 在同一事务中生成相关报告
 */
@Component
@Slf4j
public class AssetChangeReportEventListener {


     @Autowired
     private IAssetChangeReportService reportService;

    /**
     * 监听报告生成事件
     * 使用@EventListener确保在同一事务中执行
     * 使用REQUIRED传播级别确保与主业务在同一事务中
     */
    @EventListener
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void handleReportEvent(AssetChangeReportEvent event) {
        try {
            log.info("开始处理资产变更报告生成事件 - 申请单ID: {}, 申请单编号: {}",
                    event.getApplicationId(), event.getApplicationNumber());
            AssetChangeApplicationReportRecord record = new AssetChangeApplicationReportRecord();
            reportService.generateReport(record);
            
            log.info("资产变更报告生成事件处理完成 - 申请单ID: {}", event.getApplicationId());
            
        } catch (Exception e) {
            log.error("处理资产变更报告生成事件失败 - 申请单ID: {}, 错误信息: {}", 
                    event.getApplicationId(), e.getMessage(), e);

            throw e;
        }
    }

}
