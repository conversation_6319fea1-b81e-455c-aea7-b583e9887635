package com.digiwin.escloud.aiocmdb.assetchange.service.impl;

import org.springframework.stereotype.Component;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.aiocmdb.assetchange.service.BaseChangeRequestState;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;

@Component(ApplicationStatus.NOT_SUBMITTED_NAME)
public class UnsubmittedState extends BaseChangeRequestState {

    @Override
    public String getStateName() {
        return ApplicationStatus.NOT_SUBMITTED.name();
    }

    @Override
    public void submitApproval(AssetChangeApplication context) {
        context.setApplicationStatus(ApplicationStatus.NOT_APPROVED.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.NOT_APPROVED.name()));
    }
}
