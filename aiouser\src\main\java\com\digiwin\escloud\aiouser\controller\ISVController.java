package com.digiwin.escloud.aiouser.controller;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiouser.model.user.AuthoredUserGetResponse;
import com.digiwin.escloud.aiouser.model.user.ISVUser;
import com.digiwin.escloud.aiouser.model.user.UserTenantInfoResponse;
import com.digiwin.escloud.aiouser.service.IISVService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023-04-17
 * @Description
 */
@Api(value = "ISV接口", tags = {"ISV接口"})
@RestController
public class ISVController extends ControllerBase {

    @Resource
    private IISVService isvService;

    @ApiOperation(value = "租户第一次购买该应用时，付款完成之后会调用此接口，将相关授权信息传给产品单位进行初始化操作")
    @PostMapping("/api/app/initialize")
    public BaseResponse initialize(@ApiParam(required = true, value = "isv租户购买的商品等信息") @RequestBody JSONObject jsonObject) {
        return getBaseResponse(() -> isvService.initialize(jsonObject),false, false, null);
    }

    @ApiOperation(value = "同步租户 授权应用（合约）相关数据")
    @PostMapping("/sync/tenant/app")
    public BaseResponse syncTenantApps(@ApiParam(required = false, value = "tenantId") @RequestParam(value = "tenantId", required = false) String tenantId) {
        return getBaseResponse(() -> isvService.syncTenantApps(tenantId),false, false, null);
    }

    @ApiOperation(value = "同步运维商用户数据")
    @PostMapping("/sync/supplier/user")
    public BaseResponse syncSupplierUsers(@ApiParam(required = false, value = "tenantId") @RequestParam(value = "tenantId", required = false) String tenantId) {
        return getBaseResponse(() -> isvService.syncSupplierUsers(tenantId),false, false, null);
    }

    @ApiOperation(value = "同步用户的授权应用")
    @PostMapping("/sync/tenant/user/authorize/app")
    public BaseResponse syncUserAuthorizeApps(@ApiParam(required = false, value = "tenantId") @RequestParam(value = "tenantId", required = false) String tenantId) {
        return getBaseResponse(() -> isvService.syncUserAuthorizeApps(tenantId),false, false, null);
    }

    @ApiOperation(value = "同步ISV应用下的模组")
    @PostMapping("/sync/app/model")
    public BaseResponse syncAppModels(@ApiParam(required = false, value = "appId") @RequestParam(value = "appId", required = false) String appId) {
        return getBaseResponse(() -> isvService.syncAppModels(appId),false, false, null);
    }

    @ApiOperation(value = "获取运维商sid")
    @GetMapping("/api/isv/supplier/sid")
    public BaseResponse getSupplierSid(@ApiParam(required = true, value = "supplierCode") @RequestParam(value = "supplierCode", required = true) String supplierCode) {
        return getBaseResponse(() -> isvService.getSupplierSid(supplierCode),false, false, null);
    }
    @ApiOperation(value = "ISV用户注册")
    @PostMapping("/isv/register")
    public AuthoredUserGetResponse doISVUserRegister(@ApiParam(required = true, value = "ISV用户") @RequestBody ISVUser isvUser) {
        return isvService.doISVUserRegister(isvUser);
    }
    @ApiOperation(value = "根据租户id获取合约内的产品列表")
    @GetMapping("/isv/tenant/contracts")
    public BaseResponse getContractsByEid(@ApiParam(value = "eid", required = true) @RequestParam(value = "eid", required = true) long eid) {
        return getBaseResponse(() -> isvService.getContractsByEid(eid),false, false, null);
    }

    @ApiOperation(value = "根据isv客户用户id获取isv客户用户所属哪些运维商")
    @GetMapping("/isv/user/suppliers")
    public BaseResponse getISVUserSuppliers(@ApiParam(required = true, value = "用户id") @RequestParam(value = "id", required = true) String id) {
        return getBaseResponse(() -> isvService.getISVUserSuppliers(id),false, false, null);
    }
    @ApiOperation(value = "ISV用户登录")
    @PostMapping("/isv/login")
    public AuthoredUserGetResponse doISVUserLogin(@ApiParam(required = true, value = "第三方用户") @RequestBody ISVUser isvUser) {
        return isvService.doISVUserLogin(isvUser);
    }

    @ApiOperation(value = "根据ISV用户账号、运维商查所属租户列表")
    @GetMapping("/isv/user/tenants")
    public BaseResponse getTenantsById(@ApiParam(value = "sid", required = true) @RequestParam(value = "sid", required = true) long sid,
                                       @ApiParam(required = true, value = "用户id") @RequestParam(value = "id", required = true) String id) {
        return getBaseResponse(() -> isvService.getTenantsById(sid,id),false, false, null);
    }

    @ApiOperation(value = "根据用户ID查询用户租户详细信息")
    @GetMapping("/isv/user/tenant/info")
    public BaseResponse<UserTenantInfoResponse> getUserTenantInfo(@ApiParam(required = true, value = "用户ID")
                                                                            @RequestParam(value = "userId") String userId,
                                                                        @RequestParam(value = "tenantSid") Long tenantSid) {
        return BaseResponse.okT(isvService.getUserTenantInfo(userId,tenantSid));
    }
}
