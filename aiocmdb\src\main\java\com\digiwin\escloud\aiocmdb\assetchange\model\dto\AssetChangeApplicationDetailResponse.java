package com.digiwin.escloud.aiocmdb.assetchange.model.dto;

import com.digiwin.escloud.aiocmdb.assetchange.model.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 资产变更申请单详情响应DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Data
@ApiModel(value = "AssetChangeApplicationDetailResponse", description = "资产变更申请单详情响应")
public class AssetChangeApplicationDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("资产变更申请单主表信息")
    private AssetChangeApplication application;

    @ApiModelProperty("变更资产清单")
    private List<AssetChangeApplicationAssetList> assetList;

    @ApiModelProperty("执行计划列表")
    private List<ExecutionPlanDetail> executionPlanList;

    @ApiModelProperty("执行记录列表")
    private List<AssetChangeApplicationOperationRecord> operationRecordList;

    @ApiModelProperty("执行进度")
    private Double executionComplete;

    /**
     * 执行计划详情（包含停机状况和执行记录）
     */
    @Data
    @ApiModel(value = "ExecutionPlanDetail", description = "执行计划详情")
    public static class ExecutionPlanDetail implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty("执行计划信息")
        private AssetChangeApplicationExecutionPlan executionPlan;

        @ApiModelProperty("停机状况信息")
        private AssetChangeExecutionPlanShutdownStatus shutdownStatus;

        @ApiModelProperty("执行记录列表")
        private List<AssetChangeExecutionPlanExecutionRecord> executionRecordList;

        public ExecutionPlanDetail() {
        }

        public ExecutionPlanDetail(AssetChangeApplicationExecutionPlan executionPlan) {
            this.executionPlan = executionPlan;
        }

        @Override
        public String toString() {
            return "ExecutionPlanDetail{" +
                    "executionPlan=" + executionPlan +
                    ", shutdownStatus=" + shutdownStatus +
                    ", executionRecordList=" + executionRecordList +
                    '}';
        }
    }



}
