package com.digiwin.escloud.aiouser.service.mail;

import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.aiouser.annotation.MailType;
import com.digiwin.escloud.aiouser.dao.ITenantDao;
import com.digiwin.escloud.aiouser.model.common.Invitation;
import com.digiwin.escloud.aiouser.model.customer.CustomerServiceInfo;
import com.digiwin.escloud.aiouser.util.CommonMailService;
import com.digiwin.escloud.aiouser.util.MessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @Date: 2025-01-15 10:00
 * @Description 激活接受者邮件服务
 */
@MailType("ActiveAccepter")
@Slf4j
@Service
public class ActiveAccepterMail implements IMailService<Invitation> {

    @Value("${digiwin.user.defaultlanguage}")
    private String defaultLanguage;
    @Value("${service.area}")
    private String serviceArea;
    @Autowired
    private CommonMailService commonMailService;
    @Autowired
    private MessageUtils messageUtils;
    @Resource
    private ITenantDao tenantDao;

    @Override
    public String[] prepareMail(Invitation invitation, Supplier<Map<String, Object>> supplierOtherInfo) {
        String language = defaultLanguage;
        String invitedEmail = invitation.getInvitedEmail();
        if (StringUtils.isEmpty(invitedEmail)) {
            log.info(" active accepter email is null");
            return null;
        }
        String defaultProductCode = "TW".equals(serviceArea) ? "163" : "147";
        String invitedServiceCode = invitation.getInvitedServiceCode();
        CustomerServiceInfo customerServiceInfo = tenantDao.getCustomerServiceInfo(invitedServiceCode, defaultProductCode);
        if (ObjectUtils.isEmpty(customerServiceInfo)) {
            log.info("customerServiceInfo:{},{} is null", invitedServiceCode, defaultProductCode);
            return null;
        }
        String custLevel = customerServiceInfo.getCustLevel();
        if (StringUtils.isEmpty(custLevel)) {
            log.info("custLevel:{},{} is null", invitedServiceCode, defaultProductCode);
            return null;
        }

        log.info("in prepare Active accepter mail {}", invitedEmail);
        String subject = messageUtils.get("ActiveAccepterSubject", language);
        String subTitle = messageUtils.get("ActiveAccepterSubTitle", language);
        String serviceName = null;
        if ("DA".equals(custLevel)) {
            serviceName = messageUtils.get("envOpsService", language);
        } else if ("DB".equals(custLevel)) {
            serviceName = messageUtils.get("dataProtectionService", language);
        } else if ("DC".equals(custLevel)) {
            serviceName = messageUtils.get("continueProductionService", language);
        }
        String contractStartDate = customerServiceInfo.getContractStartDate();
        String startYear = null;
        String startMonth = null;
        String startDay = null;
        if (contractStartDate != null && contractStartDate.length() >= 8) {
            startYear = contractStartDate.substring(0, 4);
            startMonth = contractStartDate.substring(4, 6);
            startDay = contractStartDate.substring(6, 8);
        }
        String contractExprityDate = customerServiceInfo.getContractExprityDate();
        String endYear = null;
        String endMonth = null;
        String endDay = null;
        if (contractExprityDate != null && contractExprityDate.length() >= 8) {
            endYear = contractExprityDate.substring(0, 4);
            endMonth = contractExprityDate.substring(4, 6);
            endDay = contractExprityDate.substring(6, 8);
        }

        String activeAccepterStr = commonMailService.readMailContent("activeAccepterMail.html", language);
        String mailMsg = String.format(activeAccepterStr, subTitle, invitation.getInvitedTenantName(), invitation.getInvitedUserId(),
                serviceName, startYear, startMonth, startDay, endYear, endMonth, endDay, invitation.getLinkUrl());
        //组织发送邮件
        List<String> receivers01 = new ArrayList<>();
        receivers01.add(invitedEmail);
        Mail mail = new Mail();
        mail.setSubject(subject);
        mail.setSubtitle(subTitle);
        mail.setMessage(mailMsg);
        mail.setReceivers(receivers01);
        mail.setMailSourceType(MailSourceType.ActiveAccepter);
        mail.setUrl("");
        mail.setSourceId(Long.toString(invitation.getId()));
        mail.setPriority(1);
        mail.setLanguage(language);
        return new String[]{JSON.toJSONString(mail)};
    }
}