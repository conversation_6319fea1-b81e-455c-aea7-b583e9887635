package com.digiwin.escloud.aiouser.service;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiouser.model.user.AuthoredUserGetResponse;
import com.digiwin.escloud.aiouser.model.user.ISVUser;
import com.digiwin.escloud.aiouser.model.user.UserTenantInfoResponse;
import com.digiwin.escloud.common.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023-04-17
 * @Description
 */
public interface IISVService {
    BaseResponse initialize(JSONObject jsonObject);
    BaseResponse syncTenantApps(String tenantId);
    BaseResponse syncSupplierUsers(String tenantId);
    BaseResponse syncUserAuthorizeApps(String tenantId);
    BaseResponse syncAppModels(String tenantId);
    BaseResponse getSupplierSid(String tenantId);
    AuthoredUserGetResponse doISVUserRegister(ISVUser isvUser);
    BaseResponse getContractsByEid(long eid);
    BaseResponse getISVUserSuppliers(String id);
    AuthoredUserGetResponse doISVUserLogin(ISVUser isvUser);
    BaseResponse getTenantsById(long sid,String id);
    
    /**
     * 根据用户ID查询用户租户信息
     * @param userId 用户ID
     * @return 用户租户信息列表
     */
    UserTenantInfoResponse getUserTenantInfo(String userId,Long tenantSid);
}
