package com.digiwin.escloud.aiouser.model.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户租户信息响应对象
 * 用于返回用户、用户租户映射和租户的完整信息
 */
@ApiModel("用户租户信息响应对象")
@Data
public class UserTenantInfoResponse {
    
    // User表字段
    @ApiModelProperty("用户主键")
    private Long userSid;
    
    @ApiModelProperty("用户ID")
    private String userId;
    
    @ApiModelProperty("用户名")
    private String userName;
    
    @ApiModelProperty("用户密码")
    private String password;
    
    @ApiModelProperty("用户邮箱")
    private String email;
    
    @ApiModelProperty("用户手机")
    private String telephone;
    
    @ApiModelProperty("用户电话")
    private String phone;
    
    @ApiModelProperty("默认租户sid")
    private Long defaultEid;
    
    @ApiModelProperty("默认租户sid对应的运营商sid")
    private Long defaultEidSid;
    
    @ApiModelProperty("默认运营商sid")
    private Long defaultSid;
    
    @ApiModelProperty("默认运营商sid对应的租户sid")
    private Long defaultSidEid;
    
    @ApiModelProperty("用户状态")
    private Integer userStatus;
    
    @ApiModelProperty("微信openid")
    private String openId;
    
    @ApiModelProperty("微信openid")
    private String wechat;
    
    // Tenant表字段
    @ApiModelProperty("租户主键")
    private Long tenantSid;
    
    @ApiModelProperty("租户ID")
    private String tenantId;
    
    @ApiModelProperty("租户名称")
    private String tenantName;
    
    @ApiModelProperty("租户中文全称")
    private String customerFullNameCH;
    
    @ApiModelProperty("租户英文全称")
    private String customerFullNameEN;
    
    @ApiModelProperty("鼎捷客户客代")
    private String customerId;
    
    @ApiModelProperty("税号")
    private String taxCode;
    
    @ApiModelProperty("租户状态")
    private Integer tenantStatus;
    
    @ApiModelProperty("注册电话")
    private String registerPhone;
    
    @ApiModelProperty("注册地址")
    private String address;
    
    @ApiModelProperty("联系人")
    private String contacts;
    
    @ApiModelProperty("租户邮箱")
    private String tenantEmail;
    
    @ApiModelProperty("联系人电话")
    private String tenantPhone;
    
    @ApiModelProperty("国码")
    private String cellphonePrefix;
    
    @ApiModelProperty("联系人手机")
    private String tenantTelephone;
    
    @ApiModelProperty("是否isv")
    private Integer isv;
    
    @ApiModelProperty("是否完成安装")
    private Boolean installed;
}