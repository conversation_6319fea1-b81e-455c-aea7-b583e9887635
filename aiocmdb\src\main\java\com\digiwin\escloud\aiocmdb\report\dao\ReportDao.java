package com.digiwin.escloud.aiocmdb.report.dao;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import com.digiwin.escloud.aiocmdb.backupschedule.model.BackupScheduleSoftware;
import com.digiwin.escloud.aiocmdb.report.model.*;
import com.digiwin.escloud.aiocmdb.report.model.dto.ReportMaintenanceUpdateDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface ReportDao {
    List<ReportReceiver> getReceivers(HashMap<String, Object> map);

    int getReceiversCount(HashMap<String, Object> map);

    List<ReportReceiverModuleBase> getModules();

    int saveReceiver(ReportReceiver reportReceiver);

    int deleteReceiverModules(long id);

    int saveReceiverModules(HashMap<String, Object> map);

    int deleteReceiver(long id);

    List<ReportMaintenance> getReports(HashMap<String, Object> map);

    List<ReportFile> getFiles(HashMap<String, Object> map);

    int getReportsCount(HashMap<String, Object> map);

    ReportMaintenance selectReportMaintenanceById(Long rmId);

    List<ReportReceiver> selectReportReceiversByPartMap(Map<String, Object> map);

    Integer saveReportMaintenance(ReportMaintenance rm);

    int saveReportFile(ReportFile rf);

    int saveReportTemp(ReportTemp rt);

    int saveReportTempDevice(List<ReportTempDevice> rtd);

    ReportTemp getReportTemp(@Param("id") long id, @Param("modelCode") String modelCode);
    List<BackupScheduleSoftware> getBackSoftList();

    int updateReportMaintenance(@Param("id")Long id,@Param("reportStatus") int reportStatus);

    int insertReportMaintenanceLog(ReportMaintenanceUpdateDTO dto);

    List<ReportMaintenanceLog> getReportLogs(Long reportMaintenanceId);

    Integer saveAssetMaintenanceReport(ReportMaintenance reportMaintenance);

    Integer saveMaintenanceAsset(@Param("ramId") Long reportAssetMaintenanceId,
                                 @Param("reportDeviceMappingList") List<ReportDeviceMapping> reportDeviceMappingList);

    List<ReportDeviceMapping> getMaintenanceAsset(@Param("ramId") Long reportAssetMaintenanceId);

    Integer createMaintenanceReadLog(ReportMaintenanceReadLog reportMaintenanceReadLog);

    List<ReportMaintenanceReadLog> getMaintenanceReadLog(@Param("assetMaintenanceId") Long assetMaintenanceId,
                                                         @Param("pageNum") Integer pageNum,
                                                         @Param("pageSize") Integer pageSize);

    Integer getMaintenanceReadLogCount(@Param("assetMaintenanceId") Long assetMaintenanceId);
    List<Integer> getMaintenanceStatus(@Param("reportMaintenanceId") Long reportMaintenanceId);
    Integer updateMISStatus(@Param("assetMaintenanceId") Long assetMaintenanceId,
                            @Param("misStatus") Integer misStatus,
                            @Param("sender") String sender,
                            @Param("sendTime") Date sendTime);


    List<Long> getReceiversEidList();
    ReportFile getFileByReportIdAndFileId(@Param("serviceCode") String serviceCode,
                                          @Param("reportId") String reportId,
                                          @Param("fileId") String fileId);

    List<AssetReportRecord> selectAssetReportRecord(@Param("eid") String eid,
                                                    @Param("startReportDate") String startReportDate,
                                                    @Param("endReportDate") String endReportDate,
                                                    @Param("assetCategory") String assetCategory,
                                                    @Param("userName") String userName,
                                                    @Param("startReportGenerateTime") String startReportGenerateTime,
                                                    @Param("endReportGenerateTime") String endReportGenerateTime,
                                                    @Param("serviceCodeORCustomerName") String serviceCodeORCustomerName,
                                                    @Param("reportType") String reportType);

    int deleteAssetReportRecordById(@Param("id") Long id, @Param("reportType") String reportType);

    int insertAssetReportRecord(AssetReportRecord assetReportRecord);

    int updateReportStatus(@Param("id") Long id, @Param("reportStatus") String reportStatus);

    String queryMaxRecordNumberByEid(@Param("eid") String eid, @Param("likePattern") String likePattern);

    AssetReportRecord selectAssetReportRecordById(@Param("id") Long id);

    int updateAssetReportRecord(AssetReportRecord assetReportRecord);

    List<AssetChangeApplication> selectAssetChangeReport(@Param("id") Long id);

    List<AssetChangeApplication> selectAssetChangeReportList(@Param("id") Long id, @Param("eid") Long eid,
                                                             @Param("startApplicationDate") String startApplicationDate,
                                                             @Param("endApplicationDate") String endApplicationDate,
                                                             @Param("applicantName") String applicantName,
                                                             @Param("applicantUnit") String applicantUnit,
                                                             @Param("applicationCategory") String applicationCategory,
                                                             @Param("applicationStatus") String applicationStatus,
                                                             @Param("startReportTime") String startReportTime,
                                                             @Param("endReportTime") String endReportTime);
}
