package com.digiwin.escloud.aiocmdb.assetchange.service;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;

public interface ChangeRequestState {

    // 提交审批
    void submitApproval(AssetChangeApplication context);

    // 审批(通过)
    void approvePass(AssetChangeApplication context);

    // 审批(不通过)
    void approveFail(AssetChangeApplication context);

    // 审批(需调整) -> 进入审批退回状态
    void approveAdjust(AssetChangeApplication context);

    // 执行
    void execute(AssetChangeApplication context);

    // 提交验收
    void submitAcceptance(AssetChangeApplication context);

    // 验收(通过)
    void acceptPass(AssetChangeApplication context);

    // 验收(不通过)
    void acceptFail(AssetChangeApplication context);

    // 验收(需调整) -> 进入验收退回状态
    void acceptAdjust(AssetChangeApplication context);

    // 获取当前状态的名称（用于显示）
    String getStateName();
}
