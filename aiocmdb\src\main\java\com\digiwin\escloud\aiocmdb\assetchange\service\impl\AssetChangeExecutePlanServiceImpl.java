package com.digiwin.escloud.aiocmdb.assetchange.service.impl;

import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationExecutionPlanMapper;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationExecutionPlan;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.PlanStatus;
import com.digiwin.escloud.aiocmdb.assetchange.service.AssetChangeExecutePlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AssetChangeExecutePlanServiceImpl implements AssetChangeExecutePlanService {
    @Autowired
    private AssetChangeApplicationExecutionPlanMapper executionTaskMapper; 
    @Override
    public Double getExecutionComplete(Long applicationId) {
        // 查询该申请单下的所有执行计划
        List<AssetChangeApplicationExecutionPlan> executionPlans = 
                executionTaskMapper.selectByApplicationId(applicationId);
        
        if (executionPlans == null || executionPlans.isEmpty()) {
            return 0.0; // 没有执行计划，完成度为0
        }
        
        // 统计总数
        int totalCount = executionPlans.size();
        
        // 统计已完成的数量（状态为PARTIALLY_COMPLETED、COMPLETED、TERMINATED）
        long completedCount = executionPlans.stream()
                .filter(plan -> {
                    String planStatus = plan.getPlanStatus();
                    return PlanStatus.COMPLETED.name().equals(planStatus) ||
                            PlanStatus.TERMINATED.name().equals(planStatus) ||
                            PlanStatus.PARTIALLY_COMPLETED.name().equals(planStatus);
                })
                .count();
        
        // 计算完成度（保留两位小数）
        double completionRate = (double) completedCount / totalCount;
        return Math.round(completionRate * 100.0) / 100.0;
    }
}
