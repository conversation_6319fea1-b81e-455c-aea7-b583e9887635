package com.digiwin.escloud.aiocmdb.assetchange.service;

import com.digiwin.escloud.aiocmdb.assetchange.event.AssetChangeEmailEvent;

/**
 * 资产变更邮件服务接口
 * 定义邮件发送相关的业务方法
 */
public interface IAssetChangeEmailService {

    /**
     * 发送审批拒绝邮件
     * @param event 邮件事件对象
     */
    void sendApproveFailEmail(AssetChangeEmailEvent event);

    /**
     * 发送验收通过邮件
     * @param event 邮件事件对象
     */
    void sendAcceptPassEmail(AssetChangeEmailEvent event);

    /**
     * 发送验收拒绝邮件
     * @param event 邮件事件对象
     */
    void sendAcceptFailEmail(AssetChangeEmailEvent event);

    /**
     * 获取申请人邮箱地址
     * @param applicationId 申请单ID
     * @return 申请人邮箱地址
     */
    String getApplicantEmail(Long applicationId);


}
