package com.digiwin.escloud.aiouser.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiouser.cache.UserCache;
import com.digiwin.escloud.aiouser.constant.AioUserConst;
import com.digiwin.escloud.aiouser.dao.IISVAppDao;
import com.digiwin.escloud.aiouser.dao.ITenantDao;
import com.digiwin.escloud.aiouser.dao.IUserDao;
import com.digiwin.escloud.aiouser.model.product.ProductClassification;
import com.digiwin.escloud.aiouser.model.supplier.*;
import com.digiwin.escloud.aiouser.model.tenant.Tenant;
import com.digiwin.escloud.aiouser.model.tenant.TenantContract;
import com.digiwin.escloud.aiouser.model.user.AuthoredUser;
import com.digiwin.escloud.aiouser.model.user.*;
import com.digiwin.escloud.aiouser.service.IISVService;
import com.digiwin.escloud.aiouser.service.IUserBaseService;
import com.digiwin.escloud.aiouser.service.IUserService;
import com.digiwin.escloud.aiouser.util.KeyUtils;
import com.digiwin.escloud.common.constant.AioPlatformEnum;
import com.digiwin.escloud.common.constant.DateTimePatternConstant;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.digiwin.escloud.integration.api.iam.req.org.OrgVO;
import com.digiwin.escloud.integration.api.iam.req.org.QueryOrgTreeList;
import com.digiwin.escloud.integration.api.iam.req.org.QueryUserInOrgResultVO;
import com.digiwin.escloud.integration.api.iam.req.org.UserWithOrgsVO;
import com.digiwin.escloud.integration.api.iam.req.user.*;
import com.digiwin.escloud.integration.service.CacService;
import com.digiwin.escloud.integration.service.IamService;
import com.github.pagehelper.util.StringUtil;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023-04-17
 * @Description
 */
@Service
@Slf4j
public class ISVService implements IISVService {
    @Value("${esc.integration.appToken}")
    private String appToken;
    @Value("${service.area}")
    private String serviceArea;
    @Value("${digiwin.user.defaultlanguage}")
    private String defaultLanguage;
    @Value("${digiwin.user.defaultserviceregion}")
    private String defaultServiceRegion;
    @Value("${digiwin.user.defaulttimezone}")
    private String defaultTimeZone;

    @Autowired
    private UserCache userCache;
    @Autowired
    private IISVAppDao isvAppDao;
    @Autowired
    private IamService iamService;
    @Autowired
    private CacService cacService;
    @Autowired
    private IUserService userService;
    @Autowired
    private ITenantDao tenantDao;
    @Autowired
    private IUserBaseService userBaseService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private KeyUtils keyUtils;
    @Override
    public BaseResponse initialize(JSONObject jsonObject) {
        //校验
        if(jsonObject.get("tenantId") == null || jsonObject.get("tenantSid") == null)
        {
            return BaseResponse.error(ResponseCode.PARAM_VERIFY);
        }
        String tenantId = jsonObject.get("tenantId").toString();

        long supplierSid = SnowFlake.getInstance().newId();
        Supplier existSupplier = isvAppDao.getSuppler(jsonObject.get("tenantId").toString());
        if(!ObjectUtils.isEmpty(existSupplier)){
            supplierSid = existSupplier.getSid();//如果第一次初始化失败，这时候sid 已经产生，就用已有的sid
        }
        //1 初始化数据，包含运维商、运维商合约、租户、mapping、租户合约、授权等
        initData(jsonObject, supplierSid);
        //2 同步租户的授权应用（合约）
        syncTenantApp(tenantId, supplierSid);
        //3 同步运维商所有租户的用户数据
        syncSupplierUsers(tenantId);
        //4 同步用户授权
        syncUserAuthorizeApp(tenantId);

        return BaseResponse.ok();
    }

    /**
     * 初始化数据，包含运维商、运维商合约、租户、mapping等
     * @param jsonObject
     */
    private void initData(JSONObject jsonObject, long supplierSid){

        //1 初始化运维商
        initSupplier(jsonObject, supplierSid);
        //2 初始化运维商合约
        initSupplierContract(jsonObject, supplierSid);
        //3 初始化租户
        initTenant(jsonObject);
        //4 初始化运维商、租户mapping
        initSupplierTenantMap(jsonObject, supplierSid);

    }

    /**
     * 同步租户的授权应用（合约）
     * @param tenantId
     */
    @Override
    public BaseResponse syncTenantApps(String tenantId){
        List<Supplier> suppliers =  isvAppDao.getSupplierList(tenantId); //获取运维商列表
        if(CollectionUtils.isEmpty(suppliers)) return BaseResponse.error(ResponseCode.DATA_IS_NOT_FIND);
        suppliers.stream().forEach(k->{
            log.info("start syncTenantApps");
            syncTenantApp(k.getSupplierCode(),k.getSid());
            log.info("end syncTenantApps");
        });

        return BaseResponse.ok();

    }

    /**
     * 同步租户的授权应用（合约）
     * @param tenantId
     */
    public BaseResponse syncTenantApp(String tenantId, long supplierSid){
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    String iamToken = userCache.getIntegrationIamToken(tenantId);
                    if (StringUtils.isEmpty(iamToken)) {
                        log.error("同步租户的授权应用失败：iamToken为空");
                        return ;
                    }

                    Map<String, Object> map = cacService.getTenantApp(iamToken, appToken, tenantId);
                    if (HttpStatus.OK.value() != Integer.parseInt(map.get("code").toString())) {
                        log.error("获取租户的授权应用失败");
                    }
                    if(map != null && map.get("data") != null){
                        List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("data");
                        if (list != null && list.size() > 0) {
                            list.forEach(teneantJO -> {
                                List<Map<String,Object>> list1 = (List<Map<String,Object>>) teneantJO.get("auths");//授权应用
                                if(list1.stream().filter(k->"V01".equals(k.get("appId"))).count() > 0){
                                    syncUpdateTenant(teneantJO);//同步更新租户
                                    JSONObject tenant = new JSONObject();
                                    tenant.put("tenantSid",teneantJO.get("sid") != null ? teneantJO.get("sid").toString() : "");
                                    tenant.put("customerServiceCode",teneantJO.get("customerId") != null ? teneantJO.get("customerId").toString() : "");
                                    initSupplierTenantMap(tenant, supplierSid);//同步更新运维商租户mapping
                                    syncTenantContact(teneantJO, supplierSid);//同步更新租户合约、产品线、初始化分类等
                                }

                            });
                        }
                    }
                }
            });
        }catch (Exception e){
            log.error("同步租户的授权应用失败：{}", e.toString());
        }finally {
            executorService.shutdown();
        }

        return BaseResponse.ok();
    }

    /**
     * 同步更新租户
     * @param tenantJO
     */
    private void syncUpdateTenant(Map<String,Object> tenantJO){
        try{
            if(tenantJO.get("sid") == null || tenantJO.get("id") == null ) return;;
            Tenant tenant = new Tenant();
            tenant.setSid(Long.parseLong(tenantJO.get("sid").toString()));
            tenant.setId(tenantJO.get("id").toString());
            tenant.setName(tenantJO.get("name") != null ? tenantJO.get("name").toString() : "");
            tenant.setCustomer_id(tenantJO.get("customerId") != null ? tenantJO.get("customerId").toString() : "");
            tenant.setTaxCode(tenantJO.get("taxCode") != null ? tenantJO.get("taxCode").toString() : "");
            tenant.setRegisterPhone(tenantJO.get("phone") != null ? tenantJO.get("phone").toString() : "");
            tenant.setAddress(tenantJO.get("address") != null ? tenantJO.get("address").toString() : "");
            tenant.setContacts(tenantJO.get("contacts") != null ? tenantJO.get("contacts").toString() : "");
            tenant.setEmail(tenantJO.get("email") != null ? tenantJO.get("email").toString() : "");
            tenant.setPhone(tenantJO.get("phone") != null ? tenantJO.get("phone").toString() : "");
            tenant.setCellphone_prefix(tenantJO.get("cellphonePrefix") != null ? tenantJO.get("cellphonePrefix").toString() : "");
            tenant.setTelephone(tenantJO.get("telephone") != null ? tenantJO.get("telephone").toString() : "");
            //更新租户
            isvAppDao.updateTenant(tenant);
        }catch (Exception e) {
            log.error("同步更新租户失败：{}", e.toString());
        }


    }

    /**
     * 同步更新租户合约、产品线、初始化分类等
     * @param tenantJO
     */
    private void syncTenantContact(Map<String,Object> tenantJO, long supplierSid){
        try {
//            long sid = isvAppDao.getSupplierSid(supplierSid,tenantJO.get("customerId").toString());//根据租户反推运维商
            Supplier supplier = isvAppDao.getSupplerBySid(supplierSid);
            if(tenantJO.get("auths") != null){
                List<Map<String,Object>> list = (List<Map<String,Object>>) tenantJO.get("auths");//授权应用
                if (list != null && list.size() > 0) {
                    list.forEach(tenantContractJO -> {
                        //2023-07-06 新增运维商与授权应用的合约
                        syncUpdateTenantContract(supplier.getSid(), supplier.getEid(), tenantContractJO);
                        syncUpdateTenantContract(supplier.getSid(), Long.parseLong(tenantJO.get("sid").toString()), tenantContractJO);

                        int count = isvAppDao.getAppCount(supplier.getSid(), tenantContractJO.get("appId").toString());//如果是新应用，需要初始化新应用的分类
                        if(count == 0){//表示新应用
                            syncSaveSupplierProduct(supplier.getSid(), tenantContractJO);//新增产品线
                            initSupplierProductClassification(supplier.getSid(), tenantContractJO.get("appId").toString());//初始化分类
                            syncAppModel(supplier.getSid(), tenantContractJO.get("appId").toString());//同步应用的模组
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("同步更新租户合约、产品线、初始化分类失败：{}", e.toString());
        }

    }

    private void syncUpdateTenantContract(long sid, long eid, Map<String,Object> tenantContractJO){
        try {
            SimpleDateFormat df = new SimpleDateFormat(DateTimePatternConstant.ONLY_DATE);
            TenantContract tenantContract = new TenantContract();
            tenantContract.setId(SnowFlake.getInstance().newId());
            tenantContract.setSid(sid);
            tenantContract.setEid(eid);
            tenantContract.setProductCode(tenantContractJO.get("appId") != null ? tenantContractJO.get("appId").toString() : "");
            tenantContract.setProductShortName(tenantContractJO.get("appName") != null ? tenantContractJO.get("appName").toString() : "");
            tenantContract.setContractStartDate(tenantContractJO.get("effectiveDateTime") != null ? df.parse(tenantContractJO.get("effectiveDateTime").toString()) : null);
            tenantContract.setContractExpiryDate(tenantContractJO.get("expiredDateTime") != null ? df.parse(tenantContractJO.get("expiredDateTime").toString()) : null);
            tenantContract.setAuthorizedNum(Integer.parseInt(tenantContractJO.get("totalUserCount").toString()));
            //更新租户合约
            int count = isvAppDao.selectTenantContract(tenantContract);
            if(count == 0){
                isvAppDao.insertTenantContract(tenantContract);
            }else {
                isvAppDao.updateTenantContract(tenantContract);
            }

        } catch (ParseException e) {
            log.error("同步更新租户合约：{}", e.toString());
        }

    }
    private void syncSaveSupplierProduct(long sid, Map<String,Object> tenantContractJO){
        try{
            //新增产品线
            SupplierProduct product = new SupplierProduct();
            product.setSid(sid);
            product.setProductCode(tenantContractJO.get("appId").toString());
            product.setProductCategory(tenantContractJO.get("appName").toString());
            product.setProductShortName(tenantContractJO.get("appName").toString());
            product.setProductName(tenantContractJO.get("appName").toString());
            if ("CN".equals(serviceArea)) {
                product.setProductShortNameCN(tenantContractJO.get("appName").toString());
            } else {
                product.setProductShortNameTW(tenantContractJO.get("appName").toString());
            }
            isvAppDao.saveSupplierProduct(product);
        } catch (Exception e) {
            log.error("同步更新运维商应用失败：{}", e.toString());
        }

    }

    private void initSupplierProductClassification(long sid, String productCode){
        try{
            List<ProductClassification> defaultClassifications =  isvAppDao.getDefaultClassification();
            if(CollectionUtils.isEmpty(defaultClassifications)) return;
            Map<String,Object> map = new HashMap<>();
            map.put("sid",sid);
            map.put("productCode",productCode);
            map.put("defaultClassifications",defaultClassifications);
            isvAppDao.initSupplierProductClassification(map);
        } catch (Exception e) {
            log.error("初始化新应用{}的分类失败：{}", productCode, e.toString());
        }

    }
    /**
     * 初始化运维商
     * @param jsonObject
     */
    private void initSupplier(JSONObject jsonObject, long supplierSid) {
        try{
            if(jsonObject.get("tenantId") == null || jsonObject.get("tenantSid") == null) return;
            //初始化运维商
            Supplier supplier = new Supplier();
            supplier.setSid(supplierSid);
            supplier.setSupplierCode(jsonObject.get("tenantId").toString());
            supplier.setName(jsonObject.get("tenantName") != null ? jsonObject.get("tenantName").toString() : "");
            supplier.setEid(Long.parseLong(jsonObject.get("tenantSid").toString()));
            supplier.setFlag(AioUserConst.ISV_SERVICE_PREFIX);
            Supplier existSupplier = isvAppDao.getSuppler(jsonObject.get("tenantId").toString());
            if(ObjectUtils.isEmpty(existSupplier)){ //不存在就新增运维商
                isvAppDao.saveSupplier(supplier);
            }
        }catch (Exception e){
            log.error("初始化运维商失败：{}", e.toString());
        }

    }


    /**
     * 初始化运维商合约
     * @param jsonObject
     */
    private void initSupplierContract(JSONObject jsonObject, long supplierSid){
        SimpleDateFormat df = new SimpleDateFormat(DateTimePatternConstant.DATE_TIME);
        //初始化运维商合约
        if(jsonObject.get("authorizations") == null ) return;
        JSONArray jsonArray = jsonObject.getJSONArray("authorizations");
        if (jsonArray != null && jsonArray.size() > 0) {
            jsonArray.forEach(k -> {
                try {
                    JSONObject jo = JSONObject.parseObject(JSON.toJSONString(k),JSONObject.class);
                    SupplierContract supplierContract = new SupplierContract();
                    supplierContract.setId(isvAppDao.getMaxSupplierContractID() + 1);
                    supplierContract.setSid(supplierSid);
                    supplierContract.setProductCode(jo.get("code") != null ? jo.get("code").toString() : "");  //todo：code? itemId?
                    supplierContract.setAuthorizedNum(jo.get("totalCount") != null ? Integer.parseInt(jo.get("totalCount").toString()) : 0);
                    //需要从云市场 单独获取 当前租户指定商品的授权信息
                    if(jsonObject.get("tenantId") != null && jo.get("code") != null){
                        Map<String, Object> authorizeMap = getAuthorizeDetail(jsonObject.get("tenantId").toString(), jo.get("code").toString());
                        supplierContract.setContractStartDate(authorizeMap.get("effectiveTime") != null ? df.parse(authorizeMap.get("effectiveTime").toString()) : null);
                        supplierContract.setContractExpiryDate(authorizeMap.get("expiredTime") != null ? df.parse(authorizeMap.get("expiredTime").toString()) : null);
                    }

                    int count = isvAppDao.selectSupplierContract(supplierContract);
                    if(count == 0) {
                        isvAppDao.insertSupplierContract(supplierContract);
                    }else {
                        isvAppDao.updateSupplierContract(supplierContract);
                    }

                } catch (ParseException e) {
                    log.error("保存运维商合约失败：{}", e.toString());
                }
            });
        }
    }

    private Map<String, Object> getAuthorizeDetail(String tenantId, String goodsCode){
        Map<String, Object> result = new HashMap<>();
        if(StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(goodsCode)) return result;
        try {
            String iamToken = userCache.getIntegrationIamToken(tenantId);
            if (StringUtils.isEmpty(iamToken)) {
                return result;
            }

            return cacService.getGoodsAuthorizeDetail(iamToken, appToken, goodsCode);

        } catch (Exception e) {
            log.error("获取当前租户{}指定商品{}的授权信息失败", tenantId, goodsCode, e.toString());
        }
        return result;
    }

    /**
     * 初始化租户
     * @param jsonObject
     */
    private void initTenant(JSONObject jsonObject) {
        try{
            Tenant tenant = new Tenant();
            tenant.setSid(Long.parseLong(jsonObject.get("tenantSid").toString()));
            tenant.setId(jsonObject.get("tenantId").toString());
            tenant.setName(jsonObject.get("tenantName") !=null ? jsonObject.get("tenantName").toString() : "");
            tenant.setCustomer_id(jsonObject.get("customerServiceCode") != null ? jsonObject.get("customerServiceCode").toString() : ""); //兼容
            tenant.setStatus(1);//保持给跟A1一样都是1
            isvAppDao.updateTenant(tenant);
        }catch (Exception e){
            log.error("初始化租户失败：{}", e.toString());
        }

    }

    /**
     * 初始化运维商、租户mapping
     * @param jsonObject
     */
    private void initSupplierTenantMap(JSONObject jsonObject, long supplierSid){
        try{
            SupplierTenantMap supplierTenantMap = new SupplierTenantMap();
            supplierTenantMap.setId(SnowFlake.getInstance().newId());
            supplierTenantMap.setSid(supplierSid);
            supplierTenantMap.setEid(Long.parseLong(jsonObject.get("tenantSid").toString()));
            supplierTenantMap.setServiceCode(jsonObject.get("customerServiceCode") != null ? AioUserConst.ISV_SERVICE_PREFIX + jsonObject.get("customerServiceCode").toString() : "");//兼容
            if(isvAppDao.getSupplierTenantMapCount(supplierTenantMap) == 0){
                isvAppDao.saveSupplierTenantMap(supplierTenantMap);
            }

        }catch (Exception e){
            log.error("初始化运维商、租户mapping失败：{}", e.toString());
        }
    }

    /**
     * 同步运维商下所有租户的用户数据
     * @param tenantId
     * @return
     */
    @Override
    public BaseResponse syncSupplierUsers(String tenantId){
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    List<Supplier> suppliers =  isvAppDao.getSupplierList(tenantId); //获取运维商列表
                    if(CollectionUtils.isEmpty(suppliers)) {
                        log.error("supplier is null");
                        return ;
                    }
                    suppliers.stream().forEach(k->{
                        List<Tenant> tenants = isvAppDao.getTenantListNew(k.getSid());//获取指定运维商下的所有租户
                        if(!CollectionUtils.isEmpty(tenants)) {
                            log.info("start syncSupplierUsers");
                            tenants.stream().forEach(h->{
                                syncSupplierUser(h.getId());//同步指定运维商 指定租户的用户数据
                            });
                            log.info("end syncSupplierUsers");
                        }
                    });
                }
            });
        }catch (Exception e){
            log.error("同步租户的授权应用失败：{}", e.toString());
        }finally {
            executorService.shutdown();
        }

        return BaseResponse.ok();
    }

    /**
     * 同步租户的用户
     */
    private BaseResponse syncSupplierUser(String tenantId){
        try {
            String iamToken = userCache.getIntegrationIamToken(tenantId);
            if (StringUtils.isEmpty(iamToken)) {
                return BaseResponse.error(ResponseCode.USER_IAM_NULL);
            }
            //获取iam的用户列表
            List<Map<String,Object>> list = syncTenantUserByPage(iamToken, tenantId, 1, new ArrayList());

            if(list != null && list.size() > 0){
                Supplier supplier = isvAppDao.getSuppler(tenantId);
                boolean isSupplier = supplier != null ? true : false;//判断该租户是否是运维商

                Tenant t = isvAppDao.getTenant(tenantId);
                boolean createOrg = false;//是否创建部门
                String tenantId_create = t.getId();//用于记录创建的部门
                String tenantName_create = t.getName();//用于记录创建的部门
                OrgVO orgVO = new OrgVO();//用于记录创建的部门
                for(int i = 0 ; i< list.size() ; i++){
                    Map<String,Object> userJO = list.get(i); //用户信息
                    User user = new User();
                    user.setSid(Long.parseLong(userJO.get("sid").toString()));
                    user.setId(userJO.get("id") != null ? userJO.get("id").toString() : "");
                    user.setName(userJO.get("name") != null ? userJO.get("name").toString() : "");
                    user.setEmail(userJO.get("email") != null ? userJO.get("email").toString() : "");
                    user.setTelephone(userJO.get("telephone") != null ? userJO.get("telephone").toString() : "");
                    user.setPhone(userJO.get("phone") !=null ?userJO.get("phone").toString() : "");
                    if(isSupplier){
                        user.setDefaultSid(supplier.getSid());
                        user.setDefaultSidEid(supplier.getEid());
                        isvAppDao.updateUserTenantMap(new UserTenantMap(SnowFlake.getInstance().newId(), Long.parseLong(userJO.get("sid").toString()), supplier.getEid(), supplier.getSid(),true));//同步用户租户mapping
                    }else {
                        //如果是租户
                        SupplierTenantMap supplierTenantMap = isvAppDao.getSupplierTenantMap(tenantId);
                        user.setDefaultEidSid(supplierTenantMap.getSid());
                        user.setDefaultEid(supplierTenantMap.getEid());
                        isvAppDao.updateUserTenantMap(new UserTenantMap(SnowFlake.getInstance().newId(), Long.parseLong(userJO.get("sid").toString()), supplierTenantMap.getEid(), supplierTenantMap.getSid(),false));//同步用户租户mapping
                    }

                    user.setStatus(UserStatus.ACTIVATED.getIndex());
                    user.setWechat(userJO.get("wechat") != null ? userJO.get("wechat").toString() : "");

                    User existUser = isvAppDao.getUser(user);
                    if(existUser == null){
                        isvAppDao.insertUser(user);//新增用户
                    }else {
                        if(isSupplier){
                            if((existUser.getDefaultSid() == null || existUser.getDefaultSid().equals(0L) || "".equals(existUser.getDefaultSid())) &&
                                    existUser.getDefaultSidEid() == null || existUser.getDefaultSidEid().equals(0L) || "".equals(existUser.getDefaultSidEid())){
                                user.setDefaultEidSid(existUser.getDefaultEidSid());
                                user.setDefaultEid(existUser.getDefaultEid());
                                isvAppDao.updateUser(user);//更新用户
                            }
                        }else {
                            if((existUser.getDefaultEidSid() == null || existUser.getDefaultEidSid().equals(0L) || "".equals(existUser.getDefaultEidSid())) &&
                                    existUser.getDefaultEid() == null || existUser.getDefaultEid().equals(0L) || "".equals(existUser.getDefaultEid())){
                                user.setDefaultSid(existUser.getDefaultSid());
                                user.setDefaultSidEid(existUser.getDefaultSidEid());
                                isvAppDao.updateUser(user);//更新用户
                            }
                        }
                    }

                    if(isvAppDao.existUserPersonalinfoCount(Long.parseLong(userJO.get("sid").toString())) == 0){ //有也不用更新，新用户插入
                        isvAppDao.insertUserPersonalinfo(new UserPersonalInfo(Long.parseLong(userJO.get("sid").toString()), defaultLanguage, defaultServiceRegion, defaultTimeZone));//同步用户信息
                    }
                    if(isSupplier){//需要同步到员工表
                        if(userJO.get("userInOrgs") != null){
                            List<Map<String,Object>> userOgrs = (List<Map<String,Object>>)userJO.get("userInOrgs");//用户所属部门，可能有多个
                            if(userOgrs != null && userOgrs.size() > 0){
                                Map<String,Object> userOrg = userOgrs.get(0);
                                syncSupplierEmployee(supplier, tenantId, userOrg, userJO);
                            }else {
                                if(!createOrg){
                                    //创建部门
                                    orgVO = createOrg(iamToken, tenantId_create, tenantName_create);
                                    createOrg = true;
                                }
                                updateUserOrg(iamToken, orgVO, userJO);

                                Map<String,Object> userOrg = new HashMap<>();
                                userOrg.put("orgSid", orgVO.getSid());
                                userOrg.put("orgLabel",tenantId_create);
                                userOrg.put("orgName",tenantName_create);
                                userOrg.put("orgUri", orgVO.getUri());
                                syncSupplierEmployee(supplier, tenantId, userOrg, userJO);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("同步租户的用户：", e.toString());
        }
        return BaseResponse.ok();
    }

    private OrgVO createOrg(String iamToken, String tenantId_create, String tenantName_create){
        OrgVO orgVO = new OrgVO();
        try{
            orgVO.setLabel(tenantId_create);
            orgVO.setName(tenantName_create);
            orgVO.setOrgAspectId("defaultOrgAspect");
            orgVO.setOrgCatalogId("defaultOrgCatalog");
            orgVO.setParentSid(0L);
            orgVO.setTypeId("department");

            //1 创建部门
            iamService.saveOrg(iamToken,orgVO);
        }catch (Exception e){
            log.error("创建iam部门失败");
        }

        //2 查询部门列表
        QueryOrgTreeList queryOrgTreeList = new QueryOrgTreeList();
        queryOrgTreeList.setCatalogId(AioUserConst.ORG_CATALOGID);
        queryOrgTreeList.setId(AioUserConst.ORG_ID);
        List<OrgVO> orgVOS = iamService.getOrgAspect(iamToken, queryOrgTreeList);
        if(!CollectionUtils.isEmpty(orgVOS)){
            //3 根据部门编码获取部门的orgSid
            orgVO = orgVOS.stream().filter(k->k.getLabel().equals(tenantId_create)).findFirst().get();
        }
        return orgVO;
    }

    private void updateUserOrg(String iamToken, OrgVO orgVO, Map<String,Object> userJO){
        try{
            if(orgVO != null){
                UserWithOrgsVO userWithOrgsVO = new UserWithOrgsVO();
                userWithOrgsVO.setSid(Long.parseLong(userJO.get("sid").toString()));
                userWithOrgsVO.setUpdateMode(AioUserConst.ORG_ALLUPDATEMODE);
                List<QueryUserInOrgResultVO> queryUserInOrgResultVOS = new ArrayList<>();
                QueryUserInOrgResultVO queryUserInOrgResultVO = new QueryUserInOrgResultVO();
                queryUserInOrgResultVO.setOrgSid(orgVO.getSid());
                queryUserInOrgResultVOS.add(queryUserInOrgResultVO);
                userWithOrgsVO.setUserInOrgs(queryUserInOrgResultVOS);
                // 更新iam：员工到这个部门
                iamService.updateUserOrg(iamToken, userWithOrgsVO);
            }
        }catch (Exception e){
            log.error("更新iam员工的部门失败");
        }

    }


    /**
     * 递归
     * 默认查第一页，根据返回的用户总量，如果超过200，在重新翻页获取数据
     * @param iamToken
     * @param tenantId
     * @return
     */
    private List<Map<String, Object>> syncTenantUserByPage(String iamToken, String tenantId, int pageNum, List<Map<String,Object>> userlist){
        try{
            if (StringUtils.isEmpty(iamToken)) {
                log.error("iamToken is null");
            }

            Map<String, Object> map = iamService.queryTenantUsers(iamToken,tenantId,pageNum,true);
            if (HttpStatus.OK.value() != Integer.parseInt(map.get("code").toString())) {
                log.error("分页查询租户下的所有用户数据失败");
            }
            if(map != null && map.get("data") != null){
                Map<String, Object> dataJO = (Map<String, Object>)map.get("data");
                int total = dataJO.get("total") != null ? Integer.parseInt(dataJO.get("total").toString()) : 0 ;//所有的用户数
                if (dataJO != null && dataJO.get("list") != null) {
                    List<Map<String, Object>> list = (List<Map<String, Object>>)dataJO.get("list");
                    userlist.addAll(list); //合并
                }
                if(total >= 200){//每页iam默认返回200笔，超过200需要你们翻页处理
                    syncTenantUserByPage(iamToken, tenantId, pageNum+1, userlist);
                }
            }

        }catch (Exception e){
            log.error("同步获取租户用户失败：{}", e.toString());
        }
        return userlist;
    }

    private void syncSupplierEmployee(Supplier supplier, String tenantId, Map<String,Object> userOrg, Map<String,Object> userJO){
        try{
            SupplierEmployee employee = new SupplierEmployee();
            employee.setId(SnowFlake.getInstance().newId());
            employee.setName(userJO.get("name") != null ? userJO.get("name").toString() : "");
            String workNo = "E0001";//默认值
            String existWorkNO = isvAppDao.getMaxWorkNo(supplier.getSid(), tenantId);//默認以E0001流水號最大+1 (同一租戶)
            if(StringUtil.isNotEmpty(existWorkNO)){
                workNo = "E"+ String.format("%04d", Integer.parseInt(existWorkNO.substring(1)) + 1);
            }
            employee.setWorkNo(workNo);
            employee.setOrgSid(userOrg != null && userOrg.get("orgSid") != null ? Long.parseLong(userOrg.get("orgSid").toString()) : 0);
            employee.setOrgUri(userOrg != null && userOrg.get("orgUri") != null ? userOrg.get("orgUri").toString() : "");
            employee.setEmail(userJO.get("email") != null ? userJO.get("email").toString() : "");
            employee.setTelephone(userJO.get("telephone") != null ? userJO.get("telephone").toString() : "");
            employee.setStatus(EmployeeStatus.REGISTERED.getIndex());
            employee.setUserSid(userJO.get("sid") != null ? Long.parseLong(userJO.get("sid").toString()) : 0L);
            employee.setLanguage(defaultLanguage);
            employee.setTimeZone(defaultTimeZone);
            employee.setEid(supplier.getEid());
            employee.setSid(supplier.getSid());
            int count = isvAppDao.selectSupplierEmployee(employee);
            if(count == 0){
                isvAppDao.insertSupplierEmployee(employee);
            }else{
                isvAppDao.updateSupplierEmployee(employee);
            }
        }catch (Exception e){
            log.error("同步更新运维商员工信息失败：{}", e.toString());
        }

    }


    /**
     * 同步用户授权应用
     * @param tenantId
     * @return
     */
    @Override
    public BaseResponse syncUserAuthorizeApps(String tenantId){

        List<Supplier> suppliers = isvAppDao.getSupplierList(tenantId); //获取运维商列表
        if(CollectionUtils.isEmpty(suppliers)) return BaseResponse.error(ResponseCode.DATA_IS_NOT_FIND);

        suppliers.stream().forEach(k->{
            log.info("start syncUserAtuhorizeApps");
            syncUserAuthorizeApp(k.getSupplierCode());
            log.info("end syncUserAtuhorizeApps");
        });

        return BaseResponse.ok();

    }

    /**
     * 获取指定租户的指定应用授权下的用户清单
     * @param tenantId
     * @return
     */
    public BaseResponse syncUserAuthorizeApp(String tenantId){
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    List<SupplierProduct> supplierProducts = isvAppDao.getSupplierProductList(new HashMap<>()); //获取应用列表
                    if(CollectionUtils.isEmpty(supplierProducts)) {
                        log.error("supplierProducts is null");
                        return ;
                    }

                    List<Supplier> suppliers =  isvAppDao.getSupplierList(tenantId); //获取运维商列表
                    if(CollectionUtils.isEmpty(suppliers)) {
                        log.error("suppliers is null");
                        return ;
                    }
                    suppliers.stream().forEach(k->{
                        List<Tenant> tenants = isvAppDao.getTenantListNew(k.getSid());//获取指定运维商下的所有租户
                        if(!CollectionUtils.isEmpty(tenants)) {
                            log.info("start syncUserAuthorizeApp");
                            tenants.stream().forEach(h->{
                                syncUserAuthorizeApp(h.getId(), supplierProducts.stream().map(j->j.getProductCode()).collect(Collectors.toList()));
                            });
                            log.info("end syncUserAuthorizeApp");
                        }
                    });
                }
            });
        }catch (Exception e){
            log.error("获取用户清单失败：{}", e.toString());
        }finally {
            executorService.shutdown();
        }
        return BaseResponse.ok();
    }

    /**
     * 获取指定租户的指定应用授权下的用户清单
     * @param tenantId
     * @return
     */
    public BaseResponse syncUserAuthorizeApp(String tenantId, List<String> productCodes){
        try{
            String iamToken = userCache.getIntegrationIamToken(tenantId);
            if (StringUtils.isEmpty(iamToken)) {
                return BaseResponse.error(ResponseCode.USER_IAM_NULL);
            }
            //获取iam的授权应用的用户列表
            syncAuthorizeAppUsers(iamToken, tenantId, productCodes);
        }catch (Exception e)
        {
            log.error("同步租户{}，的用户授权应用失败：{}", tenantId, e.toString());
        }
        return BaseResponse.ok();
    }

    /**
     * 获取iam的授权应用的用户列表
     * @param iamToken
     * @param tenantId
     * @return
     */
    private void syncAuthorizeAppUsers(String iamToken, String tenantId, List<String> productCodes){
        try{
            if (StringUtils.isEmpty(iamToken)) {
                log.error("iamToken is null");
            }


            Map<String, Object> map = cacService.getAuthorizeAppUsers(iamToken,appToken,tenantId,productCodes);

            if (HttpStatus.OK.value() != Integer.parseInt(map.get("code").toString())) {
                log.error("获取iam的授权应用的用户列表失败");
            }

            Gson gson = new Gson();
            if(map != null && map.get("data") != null){
                List<Map<String, Object>> dataJA = (List<Map<String, Object>>)map.get("data");
                if (dataJA != null && dataJA.size() > 0) {
                    dataJA.forEach(object-> {
                        if(object.get("appId") == null) return;
                        String appId = object.get("appId").toString();
                        if(object.get("userIds") != null){
                            List<String> userIds = (List<String>)object.get("userIds");
                            if (userIds != null && userIds.size() > 0) {
                                for (String f : userIds) {//先查历史的授权
                                    UserTenantMap userTenantMap = isvAppDao.getUserTenantMap(f, tenantId);
                                    String oldAuthorizedProductCodes = userTenantMap.getAuthorizedProductCodes();
                                    String oldDefaultProductCode = userTenantMap.getDefaultProductCode();
                                    boolean existAuthorizedProductCode = false; //用户授权应用是否存在
                                    boolean existDefaultProductCode = false; //用户默认授权应用是否存在
                                    if (!StringUtils.isEmpty(oldAuthorizedProductCodes)) {
                                        String[] oldAuthorizedProductCodesStr = oldAuthorizedProductCodes.split(",");
                                        if (oldAuthorizedProductCodesStr != null && oldAuthorizedProductCodesStr.length > 0) {
                                            for (String oldAuthorizedProductCode : oldAuthorizedProductCodesStr) {
                                                if (oldAuthorizedProductCode.equals(appId))
                                                    existAuthorizedProductCode = true;
                                            }
                                        }
                                    }
                                    if (StringUtil.isNotEmpty(oldDefaultProductCode)) existDefaultProductCode = true;
                                    if (existAuthorizedProductCode && existDefaultProductCode) {
                                        continue; //不需要更新
                                    }

                                    Map<String, Object> map2 = new HashMap<>();
                                    map2.put("userId",f.toString());
                                    map2.put("tenantId",tenantId);
                                    map2.put("authorizedProductCodes",StringUtils.isEmpty(oldAuthorizedProductCodes) ? appId : oldAuthorizedProductCodes + "," + appId);
                                    map2.put("defaultProductCode",appId);
                                    isvAppDao.updateUserAuthorizeApp(map2);
                                }
                            }
                        }
                    });
                }
            }
        }catch (Exception e){
            log.error("同步获取租户用户失败：{}", e.toString());
        }
    }


    /**
     * 同步ISV应用模组列表
     * @param appId
     * @return
     */
    @Override
    public BaseResponse syncAppModels(String appId){
        Map<String, Object> map = new HashMap<>();
        map.put("appId",appId);
        List<SupplierProduct> supplierProducts = isvAppDao.getSupplierProductList(map); //获取应用列表
        if(CollectionUtils.isEmpty(supplierProducts)) return BaseResponse.error(ResponseCode.DATA_IS_NOT_FIND);
        log.info("start syncAppModels");
        supplierProducts.stream().forEach(k->{
            syncAppModel(k.getSid(), k.getProductCode());
        });
        log.info("end syncAppModels");
        return BaseResponse.ok();

    }

    /**
     * 同步ISV指定应用的模组列表
     * @param appId
     * @return
     */
    public BaseResponse syncAppModel(long sid, String appId){
        try{
            String iamToken = userCache.getIntegrationIamToken("");
            if (StringUtils.isEmpty(iamToken)) {
                return BaseResponse.error(ResponseCode.USER_IAM_NULL);
            }

            List<Map<String,Object>> list = iamService.queryAppModel(iamToken,appId);

            if(list != null && list.size() > 0){
                list.forEach(k -> {
                    SupplierProductModule module = new SupplierProductModule();
                    module.setId(SnowFlake.getInstance().newId());
                    module.setSid(sid);
                    module.setProductCode(appId);
                    module.setModuleCode(k.get("id").toString());
                    module.setModuleName(k.get("name").toString());

                    SupplierProductModule existModule = isvAppDao.getSupplierProductModule(module);
                    if(existModule != null){ //更新
                        module.setId(existModule.getId());
                        isvAppDao.updateSupplierProductModule(module);
                    }else{
                        isvAppDao.insertSupplierProductModule(module);
                    }
                });
            }

        }catch (Exception e){
            log.error("同步ISV指定应用{}的模组失败：{}",appId, e.toString());
        }

        return BaseResponse.ok();
    }

    /**
     * 获取运维商明细
     * @param tenantId
     * @return
     */
    @Override
    public BaseResponse getSupplierSid(String tenantId){
        return BaseResponse.ok(isvAppDao.getSupplierSidByTenantId(tenantId));
    }

    @Override
    public AuthoredUserGetResponse doISVUserRegister(ISVUser isvUser) {
        AuthoredUserGetResponse res = new AuthoredUserGetResponse();
        try {
            if(isvUser.getEid() == null){
                res.setErrMsg(ResponseCode.TENANT_EMPTY.toString());
                res.setCode(ResponseCode.TENANT_EMPTY.getMsg());
                return res;
            }
            /*String flag = tenantDao.getSupplierFlag(isvUser.getSid());
            String serviceCode = "";
            if(StringUtils.isEmpty(flag)){
                serviceCode = isvUser.getServiceCode();
            }else {
                serviceCode = flag + isvUser.getServiceCode();
            }*/
            Tenant tenant = tenantDao.getTenantByServiceCode(isvUser.getServiceCode());

            if (tenant == null) {
                res.setErrMsg(ResponseCode.TENANT_EMPTY.toString());
                res.setCode(ResponseCode.TENANT_EMPTY.getMsg());
                return res;
            }
            //先根据邮箱查用户，再根据手机查用户
            String email = isvUser.getEmail();
            String telephone = isvUser.getTelephone();
            String id = isvUser.getId();
            if (!StringUtils.isEmpty(email)) {
                User userByEmail = userDao.getUserByEmail(email);
                if (userByEmail != null) {
                    res.setErrMsg(ResponseCode.MAIL_DUPLICATED.toString());
                    res.setCode(ResponseCode.MAIL_DUPLICATED.getMsg());
                    return res;
                }
            }
            if (!StringUtils.isEmpty(telephone)) {
                User userByTelephone = userDao.getUserByTelephone(telephone);
                if (userByTelephone != null) {
                    res.setErrMsg(ResponseCode.CELLPHONE_DUPLICATED.toString());
                    res.setCode(ResponseCode.CELLPHONE_DUPLICATED.getMsg());
                    return res;
                }
            }
            if (!StringUtils.isEmpty(id)) {
                User userById = userDao.getUserById(id);
                if (userById != null) {
                    res.setErrMsg(ResponseCode.ID_DUPLICATED.toString());
                    res.setCode(ResponseCode.ID_DUPLICATED.getMsg());
                    return res;
                }
            }
            //找不到用户，就默认是个新用户，需要判断手机邮箱是否被注册过
            if (!StringUtils.isEmpty(email)) {
                CheckRes checkRes = userService.checkEmailExist(email);
                if (checkRes.getIsRegister()) {
                    res.setErrMsg(ResponseCode.MAIL_DUPLICATED.toString());
                    res.setCode(ResponseCode.MAIL_DUPLICATED.getMsg());
                    return res;
                }
            }
            if (!StringUtils.isEmpty(telephone)) {
                CheckRes checkRes = userService.checkTelephoneExist(telephone);
                if (checkRes.getIsRegister()) {
                    res.setErrMsg(ResponseCode.CELLPHONE_DUPLICATED.toString());
                    res.setCode(ResponseCode.CELLPHONE_DUPLICATED.getMsg());
                    return res;
                }
            }

            if (!StringUtils.isEmpty(id)) {
                UserIdCheckRes checkRes = userService.checkUserIdExist(id);
                if (checkRes.getIsExistId()) {
                    res.setErrMsg(ResponseCode.ID_DUPLICATED.toString());
                    res.setCode(ResponseCode.ID_DUPLICATED.getMsg());
                    return res;
                }
            }

            //解密密码
            try {
                byte[] password_decrypt = EncryptionUtil.decrypt(Base64.getDecoder().decode(isvUser.getPassword().getBytes()), EncryptionUtil.key.getBytes());
                isvUser.setPassword(new String(password_decrypt));
            } catch (Exception e) {
                log.info("isv register passWord decrypt error :" + e.toString());
                res.setErrMsg(e.getMessage());
                res.setCode(ResponseCode.INTERNAL_ERROR.toString());
                return res;
            }

            List<MappingInfo> mappingInfos = new ArrayList<>();
            MappingInfo mappingInfo = new MappingInfo();
            mappingInfo.setTenantId(tenant.getId());
//            mappingInfo.setTenantId("99990000");
            mappingInfo.setProviderId(AioUserConst.PROVIDERID);
            mappingInfo.setVerifyUserId(id);
            mappingInfos.add(mappingInfo);

            List<RoleInfo> roleInfos = new ArrayList<>();
            RoleInfo roleInfo = new RoleInfo();
            roleInfo.setId(AioUserConst.ROLE_ENDUSERID);
            roleInfo.setName(AioUserConst.ROLE_ENDUSERNAME);
            roleInfos.add(roleInfo);

            UserBasicInfo userBasicInfo = new UserBasicInfo();
            UserImportInfo user = new UserImportInfo();
            user.setId(id);
            //iam必须要传name
            user.setName(isvUser.getName());
            user.setEmail(email);
            user.setTelephone(telephone);
            user.setPassword(isvUser.getPassword());
            user.setTenantId(tenant.getId());
            user.setTenantName(tenant.getName());
//            user.setTenantId("99990000");
//            user.setTenantName("鼎捷软件");

            userBasicInfo.setUser(user);
            userBasicInfo.setMappingInfo(mappingInfos);
            userBasicInfo.setRoleInfo(roleInfos);
            List<UserBasicInfo> userBasicInfos = new ArrayList<>();
            userBasicInfos.add(userBasicInfo);
            log.info("doISVUserRegister : " + SerializeUtil.JsonSerialize(userBasicInfos));
            iamService.importUserInTenant(userBasicInfos);

            IamAuthoredUser iamAuthoredUser = userBaseService.doBaseLoginWithTenant(userBasicInfo.getUser().getTenantId(), userBasicInfo.getUser().getId(), userBasicInfo.getUser().getPassword(), IdentityType.token);
            log.info("iam login : " + SerializeUtil.JsonSerialize(iamAuthoredUser));
            UserPersonalInfo userPersonalInfo = new UserPersonalInfo(iamAuthoredUser.getSid(), defaultLanguage, defaultServiceRegion, defaultTimeZone);
            AuthoredUser authoredUser = userBaseService.isvUserloginAfterRegister(iamAuthoredUser, null, isvUser.getServiceCode(), userPersonalInfo, isvUser,tenant);
            log.info("isvUserloginAfterRegister : " + SerializeUtil.JsonSerialize(authoredUser));
            stringRedisTemplate.opsForValue().set(authoredUser.getToken(), SerializeUtil.JsonSerialize(authoredUser), 1, TimeUnit.DAYS);

            res.setCode(ResponseCode.SUCCESS.toString());
            res.setAuthoredUser(authoredUser);
        } catch (Exception ex) {
            log.error("doISVUserRegister", ex);
            res.setErrMsg(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
        }
        return res;
    }

    @Override
    public BaseResponse getContractsByEid(long eid){
        return BaseResponse.ok(isvAppDao.getContractsByEid(eid));
    }

    @Override
    public BaseResponse getISVUserSuppliers(String id){
        return BaseResponse.ok(isvAppDao.getISVUserSuppliers(id));
    }

    @Override
    public AuthoredUserGetResponse doISVUserLogin(ISVUser isvUser) {
        try {
            //1 密码解密
            byte[] password_decrypt = EncryptionUtil.decrypt(Base64.getDecoder().decode(isvUser.getPassword().getBytes()), EncryptionUtil.key.getBytes());
            isvUser.setPassword(new String(password_decrypt));

            //2.客户端生成公私钥
            HashMap<String, String> keyMap = keyUtils.getKeyPairMap();
            if (keyMap != null) {
                String clientPublicKey = keyMap.get("publicKey");
                String privateKey = keyMap.get("privateKey");
                //2.获取服务端公钥
                String serverPublicKey = keyUtils.getServerPublicky();
                //3.根据服务端公钥加密客户端公钥
                String encryptPublicKey = RSAUtils.encryptByPublicKey(clientPublicKey, serverPublicKey);
                //4.获取加密后的AES的key值
                String encryptAesKey = keyUtils.getAesPublicky(encryptPublicKey);
                //5.根据客户端私有解密加密的aes的key值
                String aesKey = new String(RSAUtils.decryptByPrivateKey(org.apache.commons.codec.binary.Base64.decodeBase64(encryptAesKey), privateKey));
                String passwordHash = AESUtils.aesEncryptByBase64(isvUser.getPassword(), aesKey);

                //根据账号和运维商，可能存在多个租户，默认取第一个租户，因为目前还没涉及多租户
                if(isvUser.getEid() == null){
                    isvUser.setEid(isvAppDao.getEidByAccount(isvUser.getId(), isvUser.getSid()));
                }
                EsLoginUser esLoginUser = new EsLoginUser(isvUser.getEid(), "", isvUser.getSid(), isvUser.getId(),
                        passwordHash, IdentityType.token, AioPlatformEnum.ISV);
                esLoginUser.setClientEncryptPublicKey(encryptPublicKey);
                return userService.doLogin(esLoginUser);
            }
        } catch (Exception e) {
            System.out.println("doISVUserLogin passWord decrypt error :" + e.toString());
        }
        AuthoredUserGetResponse res = new AuthoredUserGetResponse();
        res.setErrMsg(ResponseCode.USERNAMEPASSWORD_ERROR.getMsg());
        res.setCode(ResponseCode.USERNAMEPASSWORD_ERROR.getCode());
        return res;
    }

    @Override
    public BaseResponse getTenantsById(long sid,String id){
        return BaseResponse.ok(isvAppDao.getTenantsById(sid,id));
    }

    @Override
    public UserTenantInfoResponse getUserTenantInfo(String userId,Long tenantSid) {
        UserTenantInfoResponse userInfo = isvAppDao.selectUserInfo(userId);
        UserTenantInfoResponse tenantInfo = isvAppDao.selectTenantInfo(tenantSid);
        userInfo.setTenantId(tenantInfo.getTenantId());
        userInfo.setTenantSid(tenantInfo.getTenantSid());
        return userInfo;
    }
}
