<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiouser.dao.IISVAppDao">
    <insert id="saveSupplier" parameterType="com.digiwin.escloud.aiouser.model.supplier.Supplier">
        insert into Supplier (sid, supplierCode, `name`, eid, flag)
        values(#{sid}, #{supplierCode}, #{name}, #{eid}, #{flag})
    </insert>
    <select id="getMaxSupplierContractID" resultType="java.lang.Integer">
        select max(id) from supplier_contract
    </select>
    <select id="selectSupplierContract" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierContract" resultType="java.lang.Integer">
        select count(*) from supplier_contract WHERE sid = #{sid} and productCode = #{productCode}
    </select>
    <insert id="insertSupplierContract" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierContract">
        insert into supplier_contract (id, sid, productCode, contractStartDate, contractExprityDate,authorizedNum)
        values(#{id}, #{sid}, #{productCode}, #{contractStartDate}, #{contractExpiryDate}, #{authorizedNum})
    </insert>
    <insert id="updateSupplierContract" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierContract">
        UPDATE supplier_contract
        set contractStartDate = #{contractStartDate}, contractExprityDate = #{contractExpiryDate}, authorizedNum =#{authorizedNum}
        where sid = #{sid} and productCode = #{productCode}
    </insert>
    <insert id="saveTenant" parameterType="com.digiwin.escloud.aiouser.model.tenant.Tenant">
        insert into tenant (sid, id, `name`, customer_id, status)
        values(#{sid}, #{id}, #{name}, #{customer_id}, #{status})
    </insert>
    <insert id="updateTenant" parameterType="com.digiwin.escloud.aiouser.model.tenant.Tenant">
        insert into tenant(sid, id, `name`, customer_id, taxCode, registerPhone, address, contacts, email, phone, cellphone_prefix, telephone)
        VALUES(#{sid},#{id},#{name},#{customer_id},#{taxCode},#{registerPhone},#{address},#{contacts},#{email},#{phone},#{cellphone_prefix},#{telephone})
        ON DUPLICATE KEY UPDATE sid = #{sid}, name = #{name}, customer_id =#{customer_id}, taxCode = #{taxCode}, registerPhone = #{registerPhone}, address = #{address},
                          contacts = #{contacts}, email = #{email}, phone = #{phone}, cellphone_prefix = #{cellphone_prefix}, telephone = #{telephone}
    </insert>
    <select id="getSupplierTenantMapCount" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierTenantMap" resultType="java.lang.Integer">
        select count(*) from supplier_tenant_map a where a.sid=#{sid} and a.eid=#{eid}
    </select>
    <insert id="saveSupplierTenantMap" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierTenantMap">
        insert into supplier_tenant_map (id, sid, eid, serviceCode)
        values(#{id}, #{sid}, #{eid}, #{serviceCode})
    </insert>

    <select id="selectTenantContract" resultType="java.lang.Integer">
        select count(*) from tenant_contract a where sid = #{sid} and eid=#{eid} and productCode=#{productCode}
    </select>
    <insert id="insertTenantContract" parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantContract">
        insert into tenant_contract(id,sid,eid,productCode,productShortName,contractStartDate,contractExpiryDate,authorizedNum)
        VALUES(#{id},#{sid},#{eid},#{productCode},#{productShortName},#{contractStartDate},#{contractExpiryDate},#{authorizedNum})
    </insert>
    <update id="updateTenantContract" parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantContract">
        update tenant_contract
        set productShortName=#{productShortName},contractStartDate=#{contractStartDate},contractExpiryDate=#{contractExpiryDate},authorizedNum=#{authorizedNum}
        where sid = #{sid} and eid=#{eid} and productCode=#{productCode}
    </update>
    <select id="getSupplierSid" resultType="java.lang.Long" >
        select a.sid from supplier_tenant_map a where a.sid = #{sid} and a.serviceCode = #{customerServiceCode}
    </select>
    <select id="getAppCount" resultType="java.lang.Integer">
        select count(*) from supplier_product a where a.sid = #{sid} and a.productCode = #{productCode}
    </select>

    <insert id="saveSupplierProduct" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierProduct">
        insert into supplier_product(sid,productCode,productCategory,productShortName,productName,productShortNameCN,productShortNameTW)
        VALUES(#{sid},#{productCode},#{productCategory},#{productShortName},#{productName},#{productShortNameCN},#{productShortNameTW})
        ON DUPLICATE KEY UPDATE productCategory=#{productCategory},productShortNameCN=#{productShortNameCN},productShortNameTW=#{productShortNameTW}
    </insert>
    <select id="getDefaultClassification" resultType="com.digiwin.escloud.aiouser.model.product.ProductClassification">
        select * from supplier_product_classification a where a.productCode='default' order by a.classificationCode asc
    </select>
    <insert id="initSupplierProductClassification">
        insert into supplier_product_classification (sid, productCode, classificationCode, classificationDesc, classificationNameCN, classificationNameTW,
        classificationNameUS, classificationNameVN,classificationNameTH)
        <foreach collection="defaultClassifications" item="item" open="VALUES (" separator="), (" close=")">
            #{sid}, #{productCode}, #{item.classificationCode}, #{item.classificationDesc}, #{item.classificationNameCN}, #{item.classificationNameTW},
            #{item.classificationNameUS}, #{item.classificationNameVN}, #{item.classificationNameTH}
        </foreach>
    </insert>
    <select id="getSuppler" resultType="com.digiwin.escloud.aiouser.model.supplier.Supplier">
        select * from Supplier a where a.supplierCode =#{tenantId} limit 1
    </select>
    <select id="getSupplerBySid" resultType="com.digiwin.escloud.aiouser.model.supplier.Supplier">
        select * from Supplier a where a.sid =#{sid} limit 1
    </select>

    <select id="getSupplierTenantMap" resultType="com.digiwin.escloud.aiouser.model.supplier.SupplierTenantMap">
        SELECT a.* FROM supplier_tenant_map a LEFT JOIN tenant b ON b.sid = a.eid WHERE b.id = #{tenantId} limit 1
    </select>
    <select id="getMaxWorkNo" resultType="java.lang.String">
        SELECT max(workNo) FROM supplier_employee a LEFT JOIN tenant b ON a.eid = b.sid  WHERE a.sid =#{sid} AND b.id= #{tenantId}
    </select>
    <select id="getUser" resultType="com.digiwin.escloud.aiouser.model.user.User">
        select * from user where sid = #{sid}
    </select>
    <insert id="insertUser" parameterType="com.digiwin.escloud.aiouser.model.user.User">
        insert into user(sid, id, name, email, telephone, phone, defaultEid,defaultEidSid,defaultSid,defaultSidEid,status,wechat)
        values(#{sid}, #{id}, #{name}, #{email}, #{telephone}, #{phone}, #{defaultEid}, #{defaultEidSid},
        #{defaultSid},#{defaultSidEid},#{status},#{wechat})
    </insert>
    <update id="updateUser" parameterType="com.digiwin.escloud.aiouser.model.user.User">
        UPDATE user
        set id = #{id},name=#{name},email=#{email},telephone=#{telephone},phone=#{phone},
        defaultEid=#{defaultEid},defaultEidSid=#{defaultEidSid},defaultSid=#{defaultSid},defaultSidEid=#{defaultSidEid},
        wechat=#{wechat}
        where sid = #{sid}
    </update>
    <insert id="updateUserTenantMap" parameterType="com.digiwin.escloud.aiouser.model.user.UserTenantMap">
        insert into user_tenant_map(id, userSid, eid, sid, enterprise)
        values(#{id}, #{userSid}, #{eid}, #{sid}, #{enterprise})
        ON DUPLICATE KEY UPDATE enterprise=#{enterprise}
    </insert>
    <select id="existUserPersonalinfoCount" resultType="java.lang.Integer">
        select count(*) from user_personalinfo where sid = #{sid}
    </select>

    <insert id="insertUserPersonalinfo" parameterType="com.digiwin.escloud.aiouser.model.user.UserPersonalInfo">
        insert into user_personalinfo(sid, language, serviceRegion, timeZone)
        values(#{sid}, #{language}, #{serviceRegion}, #{timeZone})
    </insert>
    <select id="selectSupplierEmployee" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee" resultType="java.lang.Integer">
        select count(*) from supplier_employee where userSid =#{userSid} and sid =#{sid}
    </select>
    <insert id="insertSupplierEmployee" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee">
        insert into supplier_employee(id, name, workNo, orgSid, orgUri, email, telephone, status, userSid, language, timeZone, eid, sid)
        values(#{id}, #{name}, #{workNo}, #{orgSid}, #{orgUri}, #{email}, #{telephone}, #{status}, #{userSid}, #{language}, #{timeZone}, #{eid}, #{sid})
    </insert>
    <update id="updateSupplierEmployee" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee">
        update supplier_employee
        set name=#{name},orgSid=#{orgSid},orgUri=#{orgUri},email=#{email},telephone=#{telephone}
        where userSid =#{userSid} and sid =#{sid}
    </update>
    <select id="getTenantList" resultType="com.digiwin.escloud.aiouser.model.supplier.Supplier">
        SELECT c.* FROM tenant a
        LEFT JOIN supplier_tenant_map b ON a.sid = b.eid
        LEFT JOIN supplier c ON c.eid = b.eid and c.sid = b.sid
        WHERE b.sid NOT IN ('241199971893824','275823690678848')
        <if test="tenantId != null and tenantId !='' ">
            AND a.id = #{tenantId}
        </if>
    </select>

    <select id="getTenantListNew" resultType="com.digiwin.escloud.aiouser.model.tenant.Tenant">
        SELECT a.* FROM tenant a
        LEFT JOIN supplier_tenant_map b ON a.sid = b.eid
        LEFT JOIN supplier c ON c.sid = b.sid
        WHERE c.sid NOT IN ('241199971893824','275823690678848')
        <if test="sid > 0">
            and c.sid = #{sid}
        </if>
    </select>
    <select id="getSupplierList" resultType="com.digiwin.escloud.aiouser.model.supplier.Supplier">
        select * from Supplier a WHERE a.sid NOT IN ('241199971893824','275823690678848')
        <if test="tenantId != null and tenantId !='' ">
            AND a.supplierCode = #{tenantId}
        </if>
    </select>
    <select id="getSupplierProductList" resultType="com.digiwin.escloud.aiouser.model.supplier.SupplierProduct">
        SELECT a.* FROM supplier_product a
        WHERE a.sid NOT IN ('241199971893824','275823690678848')
        <if test="appId != null and appId !='' ">
            AND a.productCode = #{appId}
        </if>
    </select>
    <select id="getUserTenantMap" resultType="com.digiwin.escloud.aiouser.model.user.UserTenantMap">
        SELECT a.* from user_tenant_map a
        INNER JOIN user b ON b.sid = a.userSid
        INNER JOIN tenant c ON c.sid = a.eid
        where b.id = #{userId} AND c.id=#{tenantId}
    </select>
    <update id="updateUserAuthorizeApp">
        update user_tenant_map a
        inner join user b on b.sid = a.userSid
        inner join tenant c on c.sid = a.eid
        set a.authorizedProductCodes = #{authorizedProductCodes},a.defaultProductCode = #{defaultProductCode}
        where b.id = #{userId} and c.id=#{tenantId}
    </update>
    <select id="getSupplierProductModule" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierProductModule" resultType="com.digiwin.escloud.aiouser.model.supplier.SupplierProductModule">
        select * from supplier_product_module a where a.sid = #{sid} and a.productCode = #{productCode} and a.moduleCode = #{moduleCode}
    </select>
    <insert id="insertSupplierProductModule" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierProductModule">
        insert into supplier_product_module(id, sid, productCode, moduleCode, moduleName)
        values(#{id}, #{sid}, #{productCode}, #{moduleCode}, #{moduleName})
    </insert>
    <update id="updateSupplierProductModule" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierProductModule">
        update supplier_product_module
        set moduleName = #{moduleName}
        where id = #{id}
    </update>
    <select id="getSupplierSidByTenantId" resultType="java.lang.Long">
        select a.sid from supplier a where a.supplierCode =#{tenantId}
    </select>
    <select id="getTenant" resultType="com.digiwin.escloud.aiouser.model.tenant.Tenant">
        select * from tenant a where a.id  = #{tenantId}
    </select>

    <select id="getContractsByEid" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantContract">
        SELECT tc.id,tc.sid, tc.eid, tc.productCode, tc.productShortName,
                 tc.contractState, tc.contractStartDate, tc.contractExpiryDate
        FROM tenant_contract tc
        WHERE tc.eid = #{eid}
    </select>
    <select id="getISVUserSuppliers" resultType="com.digiwin.escloud.aiouser.model.supplier.Supplier">
        SELECT a.sid,a.supplierCode,a.name,a.eid,a.taxCode,a.flag,a.supplierType
        FROM supplier a
        LEFT JOIN user_tenant_map b ON a.sid = b.sid
        LEFT JOIN user c ON c.sid = b.userSid
        WHERE c.id = #{id}
    </select>
    <select id="getEidByAccount" resultType="java.lang.Long">
        SELECT utm.eid FROM user_tenant_map utm
        LEFT JOIN user u ON utm.userSid= u.sid
        WHERE u.id = #{id} AND utm.sid = #{sid}
        limit 1
    </select>

    <select id="getTenantsById" resultType="java.util.Map">
        SELECT t.sid,ifnull(t.id,'') id,ifnull(t.name,'') name,t.customer_id,t.taxCode,t.status,t.registerPhone,t.address,
        t.contacts,t.email,t.phone,t.cellphone_prefix,t.telephone,t.isv,ifnull(stm.serviceCode,'') serviceCode
        FROM tenant t
        LEFT JOIN user_tenant_map utm ON utm.eid= t.sid
        LEFT JOIN user u ON utm.userSid= u.sid
        LEFT JOIN supplier_tenant_map stm ON stm.sid = #{sid} AND stm.eid = utm.eid
        WHERE u.id = #{id} AND utm.sid = #{sid}
    </select>

    <!-- 用户租户信息结果映射 -->
    <resultMap id="UserTenantInfoMap" type="com.digiwin.escloud.aiouser.model.user.UserTenantInfoResponse">
        <!-- User表字段 -->
        <result column="user_sid" property="userSid"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="password" property="password"/>
        <result column="email" property="email"/>
        <result column="telephone" property="telephone"/>
        <result column="phone" property="phone"/>
        <result column="defaultEid" property="defaultEid"/>
        <result column="defaultEidSid" property="defaultEidSid"/>
        <result column="defaultSid" property="defaultSid"/>
        <result column="defaultSidEid" property="defaultSidEid"/>
        <result column="user_status" property="userStatus"/>
        <result column="openId" property="openId"/>
        <result column="wechat" property="wechat"/>
        
        <!-- Tenant表字段 -->
        <result column="tenant_sid" property="tenantSid"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="customerFullNameCH" property="customerFullNameCH"/>
        <result column="customerFullNameEN" property="customerFullNameEN"/>
        <result column="customer_id" property="customerId"/>
        <result column="taxCode" property="taxCode"/>
        <result column="tenant_status" property="tenantStatus"/>
        <result column="registerPhone" property="registerPhone"/>
        <result column="address" property="address"/>
        <result column="contacts" property="contacts"/>
        <result column="tenant_email" property="tenantEmail"/>
        <result column="tenant_phone" property="tenantPhone"/>
        <result column="cellphone_prefix" property="cellphonePrefix"/>
        <result column="tenant_telephone" property="tenantTelephone"/>
        <result column="isv" property="isv"/>
        <result column="installed" property="installed"/>
    </resultMap>

    <!-- 根据用户ID查询用户租户信息 -->
    <select id="selectUserInfo" resultMap="UserTenantInfoMap">
        SELECT 
            b.sid as user_sid,
            b.id as user_id,
            b.name as user_name,
            b.password,
            b.email,
            b.telephone,
            b.phone,
            b.defaultEid,
            b.defaultEidSid,
            b.defaultSid,
            b.defaultSidEid,
            b.status as user_status,
            b.openId,
            b.wechat
        FROM user b
        WHERE b.id = #{userId}
        LIMIT 1
    </select>

    <select id="selectTenantInfo" resultMap="UserTenantInfoMap">
        SELECT
            c.sid as tenant_sid,
            c.id as tenant_id,
            c.name as tenant_name,
            c.customerFullNameCH,
            c.customerFullNameEN,
            c.customer_id,
            c.taxCode,
            c.status as tenant_status,
            c.registerPhone,
            c.address,
            c.contacts,
            c.email as tenant_email,
            c.phone as tenant_phone,
            c.cellphone_prefix,
            c.telephone as tenant_telephone,
            c.isv,
            c.installed
        FROM tenant c
        WHERE c.sid = #{tenantSid}
        LIMIT 1
    </select>
</mapper>