package com.digiwin.escloud.aiocmdb.assetchange.model.dto;

import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.common.model.PageRequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 资产变更申请单分页查询请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@ApiModel(value = "AssetChangeApplicationPageRequest", description = "资产变更申请单分页查询请求")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssetChangeApplicationPageRequest extends PageRequestBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("企业ID")
    private Long eid;

    @ApiModelProperty("申请人名称（模糊搜索）")
    private String applicantName;

    @ApiModelProperty("申请单编号（模糊搜索）")
    private String applicationNumber;

    @ApiModelProperty("申请日期开始")
    private LocalDate applicationDateStart;

    @ApiModelProperty("申请日期结束")
    private LocalDate applicationDateEnd;

    @ApiModelProperty("申请类别（精准搜索）")
    private String applicationCategory;

    @ApiModelProperty("申请单状态（精准搜索）")
    private List<String> applicationStatus;

    @ApiModelProperty("申请进度（精准搜索，会转换为对应的申请单状态列表）")
    private String applicationProgress;
}
