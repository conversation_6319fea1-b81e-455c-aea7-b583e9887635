package com.digiwin.escloud.common.util.auto;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.common.constant.BigDataConstant;
import com.digiwin.escloud.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.digiwin.escloud.common.constant.BigDataConstant.DEFAULT_SR_DB;

/**
 * @Date 2025/01/03 13:45
 * @Created yanggld
 * @Description
 */
@Slf4j
@RefreshScope
public class BigDataUtils {

    @Value("${esc.integration.datauri:}")
    private String bigDataUrl;

    @Autowired
    private RestTemplate restTemplate;

    public BaseResponse startHbase2ImpalaProcess(Map<String,Object> runParams) {
        try {
            JSONObject jsonObject = restTemplate.postForObject(bigDataUrl + BigDataConstant.HBASE2IMPALA_PROCESS_START_URL, runParams, JSONObject.class);
            String code = jsonObject.getString("code");
            String msg = jsonObject.getString("msg");
            Object data = jsonObject.get("data");
            BaseResponse resp = new BaseResponse();
            resp.setCode(code);
            resp.setErrMsg(msg);
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            log.error("startProcess", e);
            return BaseResponse.error(e);
        }
    }

    public List<Map<String, Object>> query(String sql) {
        String url = bigDataUrl + "/impala/sql/query";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(sql, headers);
            List<Map<String, Object>> result = restTemplate.postForObject(url, request, List.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }

    public List<Map<String, Object>> starrocksQuery(String sql) {
        String url = bigDataUrl + "/sr/sql/query";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(sql, headers);
            List<Map<String, Object>> result = restTemplate.postForObject(url, request, List.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }

    public Map<String,Object> srStreamLoad(StarRocksEntity starRocksEntity) {
        String url = bigDataUrl + "/sr/stream/load";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<StarRocksEntity> request = new HttpEntity<>(starRocksEntity, headers);
            return restTemplate.postForObject(url, request, Map.class);
        } catch (RestClientException e) {
            e.printStackTrace();
            return new HashMap<>(0);
        }
    }

    public String getSrDbName() {
        return DEFAULT_SR_DB;
    }

}
