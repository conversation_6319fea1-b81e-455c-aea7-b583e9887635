package com.digiwin.escloud.aiocmdb.assetchange.event;

import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationCategory;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.aiouser.model.user.UserTenantInfoResponse;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 资产变更邮件发送事件
 * 用于在事务提交成功后异步发送邮件通知
 */
@Getter
public class AssetChangeEmailEvent extends ApplicationEvent {
    
    /**
     * 申请单ID
     */
    private final Long applicationId;
    
    /**
     * 申请单编号
     */
    private final String applicationNumber;
    
    /**
     * 操作类型
     */
    private final String operationType;
    
    /**
     * 操作意见/备注
     */
    private final String operationOpinion;
    
    /**
     * 操作人信息
     */
    private final UserTenantInfoResponse operatorInfo;
    
    /**
     * 企业ID
     */
    private final Long eid;

    public AssetChangeEmailEvent(Object source, Long applicationId, String applicationNumber,
                                 String operationType, String operationOpinion,
                                 UserTenantInfoResponse operatorInfo, Long eid) {
        super(source);
        this.applicationId = applicationId;
        this.applicationNumber = applicationNumber;
        this.operationType = operationType;
        this.operationOpinion = operationOpinion;
        this.operatorInfo = operatorInfo;
        this.eid = eid;
    }


}
