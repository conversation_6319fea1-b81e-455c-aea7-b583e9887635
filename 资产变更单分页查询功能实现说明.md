# 资产变更单分页查询功能实现说明

## 功能概述

实现了资产变更单列表的分页查询功能，支持多种搜索条件，并提供申请进度的智能转换。

## 实现的文件

### 1. 申请进度枚举 (ApplicationProgress.java)
- **位置**: `aiocmdb/src/main/java/com/digiwin/escloud/aiocmdb/assetchange/model/enums/ApplicationProgress.java`
- **功能**: 定义申请进度枚举，包含申请、审批、执行、验收、结案五个进度
- **核心方法**:
  - `fromApplicationStatus()`: 根据申请单状态获取对应的申请进度
  - `getApplicationStatusesByString()`: 根据进度字符串获取对应的申请单状态列表

### 2. 分页查询请求DTO (AssetChangeApplicationPageRequest.java)
- **位置**: `aiocmdb/src/main/java/com/digiwin/escloud/aiocmdb/assetchange/model/dto/AssetChangeApplicationPageRequest.java`
- **功能**: 定义分页查询的请求参数
- **支持的搜索条件**:
  - `applicantName`: 申请人名称（模糊搜索）
  - `applicationNumber`: 申请单编号（模糊搜索）
  - `applicationDateStart/End`: 申请日期范围
  - `applicationCategory`: 申请类别（精准搜索）
  - `applicationStatus`: 申请单状态（精准搜索）
  - `applicationProgress`: 申请进度（精准搜索，会转换为对应的申请单状态）

### 3. 分页查询响应DTO (AssetChangeApplicationPageResponse.java)
- **位置**: `aiocmdb/src/main/java/com/digiwin/escloud/aiocmdb/assetchange/model/dto/AssetChangeApplicationPageResponse.java`
- **功能**: 定义分页查询的响应数据
- **特色功能**: 
  - `setApplicationStatusAndProgress()`: 根据申请单状态自动计算申请进度
  - 包含 `applicationProgress` 和 `applicationProgressLabel` 字段供前端使用

### 4. Mapper接口和XML (AssetChangeApplicationMapper)
- **接口位置**: `aiocmdb/src/main/java/com/digiwin/escloud/aiocmdb/assetchange/dao/AssetChangeApplicationMapper.java`
- **XML位置**: `aiocmdb/src/main/resources/mapper/AssetChangeApplicationMapper.xml`
- **新增方法**: `selectPageList()` - 分页查询资产变更申请单列表
- **SQL特点**: 支持动态条件查询，按创建时间倒序排列

### 5. Service接口和实现类
- **接口位置**: `aiocmdb/src/main/java/com/digiwin/escloud/aiocmdb/assetchange/service/IAssetChangeApplicationService.java`
- **实现位置**: `aiocmdb/src/main/java/com/digiwin/escloud/aiocmdb/assetchange/service/impl/AssetChangeApplicationServiceImpl.java`
- **新增方法**: `getAssetChangeApplicationPageList()`
- **核心功能**:
  - 使用PageHelper进行分页
  - 申请进度条件的智能转换
  - 自动设置响应数据中的申请进度信息

### 6. Controller接口
- **位置**: `aiocmdb/src/main/java/com/digiwin/escloud/aiocmdb/assetchange/controller/AssetChangeApplicationController.java`
- **新增接口**: `POST /asset/assetChangeApplication/pageList`
- **功能**: 提供分页查询的REST API接口

## 申请进度映射关系

| 申请进度 | 对应的申请单状态 |
|---------|----------------|
| 申请进度 | NOT_SUBMITTED |
| 审批进度 | NOT_APPROVED, APPROVAL_RETURNED |
| 执行进度 | NOT_EXECUTED, IN_EXECUTION |
| 验收进度 | NOT_ACCEPTED, ACCEPTANCE_RETURNED |
| 结案进度 | CLOSED |

## API使用示例

### 请求示例
```json
POST /asset/assetChangeApplication/pageList
{
    "pageNum": 1,
    "pageSize": 10,
    "eid": 123,
    "applicantName": "张三",
    "applicationNumber": "CR-20250818",
    "applicationDateStart": "2025-08-01",
    "applicationDateEnd": "2025-08-31",
    "applicationCategory": "SYSTEM_UPDATE",
    "applicationProgress": "APPROVAL"
}
```

### 响应示例
```json
{
    "code": "200",
    "message": "success",
    "data": {
        "total": 50,
        "pages": 5,
        "pageNum": 1,
        "pageSize": 10,
        "list": [
            {
                "id": 1,
                "applicationNumber": "CR-20250818-001",
                "applicantName": "张三",
                "applicationStatus": "NOT_APPROVED",
                "applicationProgress": "APPROVAL",
                "applicationProgressLabel": "审批进度",
                "createTime": "2025-08-18T10:00:00"
            }
        ]
    }
}
```

## 特色功能

1. **智能进度转换**: 前端可以使用申请进度进行搜索，后端自动转换为对应的申请单状态
2. **双向兼容**: 既支持按申请单状态搜索，也支持按申请进度搜索
3. **自动计算**: 响应数据中自动包含计算后的申请进度信息
4. **灵活搜索**: 支持模糊搜索和精准搜索的组合
5. **分页优化**: 使用PageHelper实现高效分页查询

## 注意事项

1. 当申请进度对应多个申请单状态时，目前只使用第一个状态进行查询
2. 后续可以扩展SQL支持IN查询来处理多状态情况
3. 所有搜索条件都是可选的，支持灵活组合
4. 分页参数会自动使用默认值（pageNum=1, pageSize=10）
