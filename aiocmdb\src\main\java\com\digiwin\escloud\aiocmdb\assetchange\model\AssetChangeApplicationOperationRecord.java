package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationDetailResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 操作记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@ApiModel(value = "AssetChangeApplicationOperationRecord对象", description = "操作记录")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssetChangeApplicationOperationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("申请单ID")
    private Long applicationId;

    @ApiModelProperty("执行记录id")
    private Long objId;

    @ApiModelProperty("操作人用户ID")
    private String operatorUserId;

    @ApiModelProperty("操作人名称")
    private String operatorName;

    @ApiModelProperty("操作时间")
    private LocalDateTime operationTime;

    @ApiModelProperty("下一处理人用户ID")
    private String nextUserId;

    @ApiModelProperty("下一处理人名称")
    private String nextUserName;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("操作意见")
    private String operationOpinion;

    @ApiModelProperty("操作意见说明")
    private String operationExplanation;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    private AssetChangeExecutionPlanExecutionRecordMapping executionPlanDetail;

}
