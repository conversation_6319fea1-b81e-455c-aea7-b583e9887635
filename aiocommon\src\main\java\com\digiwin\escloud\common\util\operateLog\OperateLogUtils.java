package com.digiwin.escloud.common.util.operateLog;

import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.common.model.operateLog.OperateLogSaveParam;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.GsonUtil;
import com.digiwin.escloud.common.util.auto.BigDataUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/08/14 17:52
 * @Description starRocks AiopsOperateLog 表操作
 */
@Slf4j
public class OperateLogUtils {
    @Autowired
    private BigDataUtils bigDataUtil;

    public BaseResponse saveRecord(OperateLogSaveParam param) {
        StarRocksEntity starRocksEntity = new StarRocksEntity();
        starRocksEntity.setDatabase(bigDataUtil.getSrDbName());
        starRocksEntity.setTable("AiopsOperateLog");
        starRocksEntity.setRows(buildRowData(param));

        bigDataUtil.srStreamLoad(starRocksEntity);
        return BaseResponse.ok();
    }

    private List<LinkedHashMap<String, Object>> buildRowData(OperateLogSaveParam param) {
        LinkedHashMap<String, Object> row = new LinkedHashMap<>();
        // 轉成 jsonString
        String operateContent = GsonUtil.getInstance().toJson(param.getOperateContent());

        row.put("startTime", param.getStartTime());
        row.put("eid", param.getEid());
        row.put("deviceId", param.getDeviceId());
        row.put("uploadDataModelCode", param.getUploadDataModelCode());
        row.put("userSid", param.getUserSid());
        row.put("userName", param.getUserName());
        row.put("operateId", param.getOperateId());
        row.put("operateType", param.getOperateType());
        row.put("endTime", param.getEndTime());
        row.put("operateContent", operateContent);
        row.put("operateResult", param.getOperateResult());
        row.put("sid", param.getSid());
        row.put("source_db_id", param.getSourceDbId());

        return Collections.singletonList(row);
    }

}
