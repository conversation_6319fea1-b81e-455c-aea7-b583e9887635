package com.digiwin.escloud.aioitms.report.aspect;

import com.digiwin.escloud.aioitms.report.annotation.ProductServiceReportLog;
import com.digiwin.escloud.aioitms.report.dao.ProductServiceReportMapper;
import com.digiwin.escloud.aioitms.report.model.serviceReport.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Date 2023/6/30 18:23
 * @Created yanggld
 * @Description
 */
@Aspect
@Component
@Slf4j
public class ProductServiceReportAspect {

    @Autowired
    private ProductServiceReportMapper productServiceReportMapper;

    @Pointcut("@annotation(com.digiwin.escloud.aioitms.report.annotation.ProductServiceReportLog)")
    public void ProductServiceReportLogPointCut() {
    }

    @AfterReturning(returning = "result", value = "ProductServiceReportLogPointCut()")
    public void saveOperLog(JoinPoint joinPoint, Object result) throws Throwable {
        ProductServiceReportOperationLog opLog;
        try {
            Object arg = joinPoint.getArgs()[0];
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            ProductServiceReportLog annotation = method.getAnnotation(ProductServiceReportLog.class);
            if (annotation != null) {
                ProductServiceReportOperation op = annotation.op();
                Long dataId;
                String operatorId;
                String operatorName;
                String remark = "";
                if (arg instanceof ProductServiceReportSendDTO) {
                    ProductServiceReportSendDTO dto = (ProductServiceReportSendDTO) arg;
                    dataId = dto.getId();
                    operatorId = dto.getOperatorId();
                    operatorName = dto.getOperatorName();
                    List<ProductServiceReportSendDTO.ProductServiceReportSendMail> sendMailList = dto.getSendMailList();
                    if (!CollectionUtils.isEmpty(sendMailList)) {
                        remark = sendMailList.stream().map(ProductServiceReportSendDTO.ProductServiceReportSendMail::getEmail).distinct().collect(Collectors.joining(";"));
                    }
                } else if (arg instanceof ProductServiceReportReqDTO) {
                    ProductServiceReportReqDTO dto = (ProductServiceReportReqDTO) arg;
                    dataId = dto.getId();
                    operatorId = dto.getOperatorId();
                    operatorName = dto.getOperatorName();
                } else {
                    ProductServiceReportModel model = (ProductServiceReportModel) arg;
                    dataId = model.getId();
                    operatorId = model.getUserId();
                    operatorName = model.getUserName();
                    if (StringUtils.isBlank(operatorId)) {
                        operatorId = model.getAuditUserId();
                        operatorName = model.getAuditUserName();
                    }
                    if (StringUtils.isBlank(operatorId)) {
                        operatorId = model.getRejectUserId();
                        operatorName = model.getRejectUserName();
                        remark = model.getRejectReason();
                    }
                }
                LocalDateTime time = null;
                if (ProductServiceReportOperation.Audit.equals(op)) {
                    time = ((ProductServiceReportModel)arg).getAuditTime();
                } else if(ProductServiceReportOperation.Reject.equals(op)){
                    time = ((ProductServiceReportModel)arg).getRejectTime();
                } else if(ProductServiceReportOperation.SendPlatform.equals(op)){
                    time = ((ProductServiceReportReqDTO)arg).getSendTime();
                } else if(ProductServiceReportOperation.SendMail.equals(op) || ProductServiceReportOperation.SendMailAndPlatform.equals(op)){
                    time = ((ProductServiceReportSendDTO)arg).getSendTime();
                }
                if (time == null) {
                    opLog = new ProductServiceReportOperationLog(dataId, op, operatorId, operatorName, remark);
                } else {
                    opLog = new ProductServiceReportOperationLog(dataId, op, operatorId, operatorName,time, remark);
                }
                productServiceReportMapper.insertLog(opLog);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
