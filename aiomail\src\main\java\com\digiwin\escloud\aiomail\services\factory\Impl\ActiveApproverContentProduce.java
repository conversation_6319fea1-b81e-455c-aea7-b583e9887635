package com.digiwin.escloud.aiomail.services.factory.Impl;

import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.aiomail.services.factory.annotation.BaseMailContent;
import org.springframework.stereotype.Service;

import static com.digiwin.escloud.aiomail.util.MailUtils.ReadMailContent;

@BaseMailContent({MailSourceType.ActiveApprover})
@Service
public class ActiveApproverContentProduce extends ContentProduceBase {

    @Override
    protected String getFileName() {
        return "activeApproverMail.html";
    }

    @Override
    public String getContent(Mail mail) {
        // 邮件内容暂时留空，由用户后续补充
        return String.format(ReadMailContent(getFileName(), mail.getLanguage()), mail.getMessage());
    }
}