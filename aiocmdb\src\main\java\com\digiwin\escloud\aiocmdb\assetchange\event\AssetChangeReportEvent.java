package com.digiwin.escloud.aiocmdb.assetchange.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 资产变更报告生成事件
 * 用于在同一事务中生成相关报告
 */
@Getter
public class AssetChangeReportEvent extends ApplicationEvent {
    
    /**
     * 申请单ID
     */
    private final Long applicationId;
    
    /**
     * 申请单编号
     */
    private final String applicationNumber;
    

    /**
     * 操作意见/备注
     */
    private final String operationOpinion;
    

    /**
     * 企业ID
     */
    private final Long eid;

    public AssetChangeReportEvent(Object source, Long applicationId, String applicationNumber, String operationOpinion,
                                  Long eid) {
        super(source);
        this.applicationId = applicationId;
        this.applicationNumber = applicationNumber;
        this.operationOpinion = operationOpinion;
        this.eid = eid;
    }


}
