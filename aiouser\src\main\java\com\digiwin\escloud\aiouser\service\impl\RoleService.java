package com.digiwin.escloud.aiouser.service.impl;

import cn.hutool.core.date.StopWatch;
import com.digiwin.escloud.aiouser.cache.TenantCache;
import com.digiwin.escloud.aiouser.dao.ITpUserDao;
import com.digiwin.escloud.aiouser.dao.IUserDao;
import com.digiwin.escloud.aiouser.model.role.RoleParam;
import com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee;
import com.digiwin.escloud.aiouser.model.user.PermissionRole;
import com.digiwin.escloud.aiouser.model.user.TpUserType;
import com.digiwin.escloud.aiouser.model.user.User;
import com.digiwin.escloud.aiouser.model.user.UserThirdParty;
import com.digiwin.escloud.aiouser.service.IOrgService;
import com.digiwin.escloud.aiouser.service.IRoleService;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.integration.api.iam.req.org.OrgVO;
import com.digiwin.escloud.integration.api.iam.req.user.UserInfo;
import com.digiwin.escloud.integration.api.iam.req.userpairrole.RoleAddUserActionReq;
import com.digiwin.escloud.integration.api.iam.res.userpairrole.UserRelationRoleRes;
import com.digiwin.escloud.integration.api.iam.res.userpairrole.UserRelationRoleV2Res;
import com.digiwin.escloud.integration.service.IamService;
import com.digiwin.escloud.integration.service.iam.AuthorizationService;
import com.digiwin.escloud.integration.service.iam.PermissionService;
import com.digiwin.escloud.integration.service.iam.common.RoleType;
import com.digiwin.escloud.integration.service.iam.common.RoleTypeCustom;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.digiwin.escloud.common.model.ResponseCode.LOW_ROLE_PRIORITY;
import static com.digiwin.escloud.common.model.ResponseCode.USER_IAM_NULL;

@Service
@Slf4j
public class RoleService implements IRoleService {
    @Value("${digiwin.token.user.verifyuserid}")
    private String verifyUserId;
    @Autowired
    private ITpUserDao tpUserDao;
    @Autowired
    protected AuthorizationService authorizationService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private com.digiwin.escloud.integration.service.iam.RoleService iamRoleService;
    @Autowired
    private IUserDao userDao;

    @Autowired
    private IOrgService orgService;
    @Autowired
    private TenantCache tenantCache;

    @Autowired
    private IamService iamService;

    @Override
    public void syncUserRole(String tpEid, String tpUserId, String productCode) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("tpProductCode", productCode);
        map.put("tpAccountSet", "");
        map.put("tpEid", tpEid);
        map.put("tpUserId", tpUserId);
        List<UserThirdParty> tpUserDetails = tpUserDao.getTpUserDetail(map);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(() -> {
                tpUserDetails.stream().forEach(o -> {
                    try {
                        authorizationService.getUserAccessToken(o.getUserId(), o.getTpEid()).subscribe(token -> {
                            RoleType roleType = TpUserType.M.equals(o.getTpUserType()) ? RoleType.MIS : RoleType.User;
                            permissionService.appendRoleToUser(token.getToken(), token.getUserId(), roleType);
                            stringRedisTemplate.delete("userauthinfo" + o.getUserSid());
                        });
                    } catch (Exception e) {
                        stringRedisTemplate.opsForSet().add("errorUserRole", tpEid + "||" + tpUserId);
                    }
                    try {
                        Thread.sleep(200);
                    } catch (Exception e) { //huly: 修复漏洞/bug InterruptedException 改成 Exception
                        e.printStackTrace();
                    }
                });
            });

        } catch (Exception ex) {
            log.error("syncUserRole", ex);
        } finally {
            executorService.shutdown();
        }

    }

    @Override
    public ResponseBase<List<PermissionRole>> getDataAuthRoleDetailList(Long eid, List<String> roleCodeList, Long sid) {
        List<PermissionRole> permissionRoleList = userDao.selectDataAuthRoleDetail(eid, roleCodeList, sid);
        return ResponseBase.okT(permissionRoleList);
    }

    @Override
    public ResponseBase<List<SupplierEmployee>> getAllUserFromRoleIdV2(RoleParam param) {
        StopWatch watch  = new StopWatch();

        String tenantServiceCode = tenantCache.getTenantServiceCode(RequestUtil.getHeaderSid(), RequestUtil.getHeaderEid());

        watch.start();
        Map<String, Object> queryMap = createQueryMap(param);
        // TODO :需要根据当前登录用户得token和tenantServiceCode获取 allUserFromRoleIdV2这个对象
        UserRelationRoleV2Res allUserFromRoleIdV2 = iamRoleService.getAllUserFromRoleIdV2(verifyUserId, tenantServiceCode, queryMap);
        watch.stop();
        log.info("[getAllUserFromRoleIdV2] cost ,{}", watch.prettyPrint(TimeUnit.SECONDS));
        if (allUserFromRoleIdV2.getList().size() < allUserFromRoleIdV2.getTotal()) {
            log.warn("[getAllUserFromRoleIdV2] Incomplete data {}", param);
        }
        List<OrgVO> orgAspect = (List<OrgVO>) orgService.getOrgAspect(false);
        Set<String> roleSet = userDao.getRoleByEid(RequestUtil.getHeaderEid(), RequestUtil.getHeaderSid())
                .stream()
                .map(PermissionRole::getRoleCode)
                .collect(Collectors.toSet());

        List<SupplierEmployee> supplierEmployeeList = userDao.queryEmployeeList(0, 0, null, 0, 0, "");

        Map<String, UserRelationRoleRes> iamUserMap = allUserFromRoleIdV2.getList()
                .stream()
                .collect(Collectors.toMap(
                        user -> user.getId() + "_" + user.getSid(),
                        Function.identity()));

        List<SupplierEmployee> filteredSupplierEmployeeList = supplierEmployeeList
                .stream()
                .filter(se -> iamUserMap.containsKey(se.getEmail() + "_" + se.getUserSid()))
                .map(se -> enrichWithRoleData(se, iamUserMap, roleSet,orgAspect))
                .collect(Collectors.toList());

        return ResponseBase.okT(filteredSupplierEmployeeList);
    }

    @Override
    public ResponseBase empAddRole(RoleParam param) {

        String token = RequestUtil.getHeaderToken();
        PriorityQueue<PermissionRole> permissionRolesQueue = checkRolePriority(token, param);

        if (!permissionRolesQueue.isEmpty()) {
           return ResponseBase.error(LOW_ROLE_PRIORITY,permissionRolesQueue.peek());
        }

        HashMap<String, String> map = new HashMap<>();
        map.put("email", param.getEmail());
        UserInfo userInfo = iamService.getUserByEmail(token, map);
        if (Objects.isNull(userInfo)) {
            log.info("{} iamService.getUserByEmail token:{} email:{} user not exist", new Date(), token, param.getEmail());
            return ResponseBase.error(USER_IAM_NULL);
        }
        List<RoleTypeCustom> rtcList = param.getRoleIdList().stream().map(RoleTypeCustom::new).collect(Collectors.toList());
        permissionService.addUserToRoleRelation(token, userInfo.getId(),rtcList
                , () -> {
                });
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase roleAddEmp(RoleParam param) {
        String token = RequestUtil.getHeaderToken();
        PriorityQueue<PermissionRole> permissionRolesQueue = checkRolePriority(token, param);

        if (!permissionRolesQueue.isEmpty()) {
            return ResponseBase.error(LOW_ROLE_PRIORITY, permissionRolesQueue.peek());
        }

        RoleAddUserActionReq roleAddUserActionReq = new RoleAddUserActionReq();
        roleAddUserActionReq.setRoleId(param.getRoleId());
        roleAddUserActionReq.setUserIds(param.getUserIds());
        permissionService.roleAddUser(token,roleAddUserActionReq);
        return ResponseBase.ok();
    }

    private Map<String, Object> createQueryMap(RoleParam param) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("roleId", param.getRoleId());
        queryMap.put("pageNum", param.getPageNum());
        queryMap.put("pageSize", param.getPageSize());
        return queryMap;
    }

    private SupplierEmployee enrichWithRoleData(SupplierEmployee se, Map<String, UserRelationRoleRes> iamUserMap
            ,Set<String> roleSet, List<OrgVO> orgAspect) {
        UserRelationRoleRes userRelationRoleRes = iamUserMap.get(se.getEmail() + "_" + se.getUserSid());
        if (userRelationRoleRes == null) {
            return se;
        }

        orgAspect.stream()
                .filter(org -> se.getOrgSid() == org.getSid())
                .findFirst()
                .ifPresent(org -> se.setOrgName(org.getName()));
        List<Map<String, String>> roleList = userRelationRoleRes.getRoles()
                .stream()
                .filter(role -> roleSet.contains(role.getId()))
                .map(role -> {
                    Map<String, String> roleMap = new HashMap<>();
                    roleMap.put("roleId", role.getId());
                    roleMap.put("roleName", role.getName());
                    return roleMap;
                })
                .collect(Collectors.toList());

        se.setRoles(roleList);
        return se;
    }

    public PriorityQueue<PermissionRole> checkRolePriority(String token, RoleParam roleParam) {
        Long eid = RequestUtil.getHeaderEid();
        Long sid = RequestUtil.getHeaderSid();
        List<Map<String, String>> currentUser = iamService.getUserRoles(token, null, RequestUtil.getHeaderUserSid());
        // 服务云超级管理员可以修改所有
        if (currentUser.stream().anyMatch(i -> RoleType.SuperManager.getId().equals(i.get("roleId")))) {
            return new PriorityQueue<>(1);
        }
        List<PermissionRole> roleList = userDao.getRoleByEid(eid, sid);

        Map<String, PermissionRole> codeMap = roleList.stream()
                .peek(role -> {
                    if (role.getPriority() == null) {
                        role.setPriority(Integer.MAX_VALUE);
                    }
                })
                .collect(Collectors.toMap(
                        PermissionRole::getRoleCode,
                        Function.identity()
                ));

        PriorityQueue<PermissionRole> userRolePriorityQueue = new PriorityQueue<>(Comparator.comparingInt(PermissionRole::getPriority));
        PriorityQueue<PermissionRole> addRolePriorityQueue = new PriorityQueue<>(Comparator.comparingInt(PermissionRole::getPriority));
        PriorityQueue<PermissionRole> deleteRolePriorityQueue = new PriorityQueue<>(Comparator.comparingInt(PermissionRole::getPriority));

        currentUser.forEach(roleInfo -> {
            PermissionRole role = codeMap.get(roleInfo.get("roleId"));
            if (role != null) {
                userRolePriorityQueue.add(role);
            }
        });

        if (roleParam.getRoleIdList() != null) {
            roleParam.getRoleIdList().forEach(roleId -> {
                PermissionRole role = codeMap.get(roleId);
                if (role != null && !userRolePriorityQueue.contains(role)) {
                    addRolePriorityQueue.add(role);
                }
            });
        }
        if (StringUtils.isNotBlank(roleParam.getEmail())) {
            User user = userDao.getUserByEmail(roleParam.getEmail());
            List<Map<String, String>> userRoles = iamService.getUserRoles(token, null, user.getSid());
            List<String> deleteRoleList = userRoles.stream().map(i -> i.get("roleId")).collect(Collectors.toList());
            deleteRoleList.removeAll(roleParam.getRoleIdList());
            deleteRoleList.forEach(roleId -> {
                PermissionRole role = codeMap.get(roleId);
                if (role != null) {
                    deleteRolePriorityQueue.add(role);
                }
            });
        }

        PermissionRole role = codeMap.get(roleParam.getRoleId());
        if (role != null) {
            addRolePriorityQueue.add(role);
        }
        addRolePriorityQueue.addAll(deleteRolePriorityQueue);
        // 确保两个队列都有元素
        if (!userRolePriorityQueue.isEmpty() && !addRolePriorityQueue.isEmpty()) {
            if (userRolePriorityQueue.peek().getPriority() >= addRolePriorityQueue.peek().getPriority()){
                return addRolePriorityQueue;
            }
        }

        return new PriorityQueue<>(1);
    }
}
