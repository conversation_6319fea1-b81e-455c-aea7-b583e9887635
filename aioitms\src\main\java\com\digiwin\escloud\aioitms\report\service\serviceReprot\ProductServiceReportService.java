package com.digiwin.escloud.aioitms.report.service.serviceReprot;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.common.Constants;
import com.digiwin.escloud.aioitms.constants.MqConstant;
import com.digiwin.escloud.aioitms.escloud.dao.EscloudMapper;
import com.digiwin.escloud.aioitms.escloud.model.*;
import com.digiwin.escloud.aioitms.model.mail.ProductServiceReportMail;
import com.digiwin.escloud.aioitms.properties.AioItmsProperties;
import com.digiwin.escloud.aioitms.report.constant.ReportConst;
import com.digiwin.escloud.aioitms.report.dao.DbReportMapper;
import com.digiwin.escloud.aioitms.report.dao.ProductServiceReportMapper;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import com.digiwin.escloud.aioitms.report.model.db.SqlServerDbReport;
import com.digiwin.escloud.aioitms.report.model.enums.ReportConclusionTypeEnum;
import com.digiwin.escloud.aioitms.report.model.serviceReport.*;
import com.digiwin.escloud.aioitms.report.service.AiopsUserService;
import com.digiwin.escloud.aioitms.report.service.ChatGptEvalType;
import com.digiwin.escloud.aioitms.report.service.ChatGptService;
import com.digiwin.escloud.aioitms.report.service.db.AsyncService;
import com.digiwin.escloud.aioitms.report.service.impl.ProductServiceReportStateHandler;
import com.digiwin.escloud.aioitms.report.service.impl.ReportTemplateService;
import com.digiwin.escloud.aioitms.report.service.utils.TrendAnalysisUtils;
import com.digiwin.escloud.common.feign.UserV2FeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.util.DoubleUtil;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.integration.api.gpt.RetryCallback;
import com.digiwin.escloud.integration.api.gpt.resp.ChatGPTResponse;
import com.digiwin.escloud.integration.service.gpt.IChatGPTService;
import com.digiwin.escloud.userv2.model.Customer;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Date 2023/6/27 14:23
 * @Created yanggld
 * @Description
 */
@Slf4j
@Service
public class ProductServiceReportService implements InitializingBean {

    @Autowired
    private ProductServiceReportMapper productServiceReportMapper;
    private ProductServiceReportStateHandler productServiceReportStateHandler;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private ProductServiceReportEsService productServiceReportEsService;
    @Autowired
    private TProductServiceReportEsService tProductServiceReportEsService;
    @Autowired
    private TProductServiceReportService tProductServiceReportService;
    @Resource
    private UserV2FeignClient userV2FeignClient;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private AioItmsProperties aioItmsProperties;
    @Autowired
    private AsyncService asyncService;
    @Resource
    private AiopsUserService aiopsUserService;
    @Autowired
    private ReportTemplateService reportTemplateService;
    @Value("#{${service.report.product.map}}")
    private Map<String, String> productMap;
    @Autowired
    private BigDataUtil bigDataUtil;
    @Resource
    private DbReportMapper dbReportMapper;
    @Value("${aio.service.area:CN}")
    private String serviceArea;

    public final static String E_PRODUCT_CODE = "37";
//    public final static List<String> E_PRODUCT_CODE_LIST = Arrays.asList("37", "08", "165"); //20241119:代码稽核 移走到别处Constants中定义
    public final static String T_PRODUCT_CODE = "100";
//    public final static List<String> T_PRODUCT_CODE_LIST = Arrays.asList("100","06","164"); //20241119:代码稽核 移走到别处Constants中定义
    public final static String EXAMINATION_REPORT = "ProductServiceReport";
    //目前产品服务报告E 结论模板相同使用默认产品线 E10、易飞、E10半导体
    public static final String DEFAULT_PRODUCT_CODE = "9999";

    public ProductServiceReportModel getById(Long id) {
        return productServiceReportMapper.getById(id);
    }

    public PageInfo pageList(ProductServiceReportQueryDTO pageQueryDto) {
        //顾问角色需要查询eidList
        pageQueryDto.setEidList(aiopsUserService.getEidList());
        List<Long> ids = productServiceReportMapper.pageIdList(pageQueryDto);
        Long count = productServiceReportMapper.countPageList(pageQueryDto);
        List<ProductServiceReportModel> list;
        if (CollectionUtils.isEmpty(ids)) {
            list = Collections.emptyList();
        } else {
            list = productServiceReportMapper.pageListByIds(ids);
        }
        PageInfo<ProductServiceReportModel> pageInfo = new PageInfo<>(list);
        pageInfo.setTotal(count);
        return pageInfo;
    }

    public Long generate(ProductServiceReportGenerateReqDTO model) throws Exception {
        try {
            ResponseBase res = userV2FeignClient.getTenantBasicDetail(model.getServiceCode());
            if (ResponseCode.SUCCESS.toString().equals(res.getCode())) {
                Optional.ofNullable(res.getData()).ifPresent(o -> {
                    Customer customer = JSON.parseObject(JSON.toJSONString(o), Customer.class);
                    model.setCustomerFullName(customer.getCustomerFullNameCH());
                });
            } else {
                log.warn("getCustomerFullName error:{}", JSONObject.toJSONString(res));
                model.setCustomerFullName(model.getCustomerName());
            }
        } catch (Exception ex) {
            log.warn("getCustomerFullName error:{}", ex.getMessage());
            model.setCustomerFullName(model.getCustomerName());
        }
        long id = SnowFlake.getInstance().newId();
        model.setId(id);
        model.setSid(RequestUtil.getHeaderSid());
        model.setReportStatus(ProductServiceReportStatus.GENERATING);
        model.setGenerateTime(LocalDateTime.now());
        model.setAppCode(RequestUtil.getHeaderAppCode());
        String[] analysisProjectCodes = model.getAnalysisProjectCodes();
        if (ArrayUtils.isNotEmpty(analysisProjectCodes)) {
            model.setAnalysisProjectCode(StringUtils.join(analysisProjectCodes, ","));
        }
        productServiceReportMapper.insert(model);

        String productCode = model.getProductCode();
        model.setProductLine(productMap.getOrDefault(productCode, "T100"));

        //在没有选择账套只选择了企业编号，需要获取所有账套信息
        settingAccountList(model);
        List<ProductServiceReportAccountModel> accountList = model.getAccountList();
        if (!CollectionUtils.isEmpty(accountList)) {
            List<ProductServiceReportAccountModel> newAccountList = accountList.stream().map(m -> {
                m.setProductServiceReportId(id);
                return m;
            }).collect(Collectors.toList());
            productServiceReportMapper.insertProductServiceReportAccountModel(newAccountList);
        }

        if (ReportConst.E_PRODUCT_CODE_LIST.contains(productCode)) {
            ProductServiceReport report = new ProductServiceReport();
            report.setId(Long.toString(id));
            report.setReportName(model.getCustomerFullName() + model.getProductName() + "产品服务报告");
            productServiceReportEsService.save(report, true);
            new Thread(() -> {
                try {
                    saveProductServiceReport(report, model);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }).start();
        } else if (ReportConst.T_PRODUCT_CODE_LIST.contains(productCode)) {
            tProductServiceReportService.saveTProductServiceReport(model);
        }
        return id;
    }


    public void save(Map<String,Object> map) {
        ProductServiceReport productServiceReport = JSONObject.parseObject(JSONObject.toJSONString(map), ProductServiceReport.class);
        productServiceReportEsService.bulkUpdateByScript(productServiceReport.getId(),map,ProductServiceReport.class);
    }


    public ProductServiceReport findById(String id) {
        return productServiceReportEsService.findById(id, ProductServiceReport.class);
    }

    public boolean deleteByState(Long id) throws Exception {
        ProductServiceReportModel model = productServiceReportMapper.getById(id);
        ProductServiceReportState productServiceReportState = get(model);
        return productServiceReportState.delete(model);
    }

    @Transactional
    public boolean delete(ProductServiceReportModel po) {
        Long id = po.getId();
        ProductServiceReportModel updPo = new ProductServiceReportModel();
        updPo.setId(id);
        updPo.setReportStatus(ProductServiceReportStatus.DELETED);
        boolean flag = productServiceReportMapper.update(updPo) > 0;
        if (ReportConst.E_PRODUCT_CODE_LIST.contains(po.getProductCode())) {
            productServiceReportEsService.deleteById(id.toString(), ProductServiceReport.class);
        } else if (ReportConst.T_PRODUCT_CODE_LIST.contains(po.getProductCode())) { //20241119:代码稽核 equals 改为
            tProductServiceReportEsService.deleteById(id.toString(), TProductServiceReport.class);
        }
        return flag;
    }

    public boolean submitAuditByState(ProductServiceReportModel model) throws Exception {
        ProductServiceReportModel po = productServiceReportMapper.getById(model.getId());
        ProductServiceReportState productServiceReportState = get(po);
        return productServiceReportState.submitAudit(model);
    }


    public boolean submitAudit(ProductServiceReportModel model) throws Exception {
        ProductServiceReportModel updPo = new ProductServiceReportModel();
        updPo.setId(model.getId());
        updPo.setReportStatus(ProductServiceReportStatus.AUDITING);
        return productServiceReportMapper.update(updPo) > 0;
    }

    public boolean auditByState(ProductServiceReportModel model) throws Exception {
        ProductServiceReportModel po = productServiceReportMapper.getById(model.getId());
        ProductServiceReportState productServiceReportState = get(po);
        return productServiceReportState.audit(model);
    }

    public boolean audit(ProductServiceReportModel model) throws Exception {
        ProductServiceReportModel updPo = new ProductServiceReportModel();
        updPo.setId(model.getId());
        updPo.setReportStatus(ProductServiceReportStatus.AUDITED);
        updPo.setAuditUserId(model.getAuditUserId());
        updPo.setAuditUserName(model.getAuditUserName());
        updPo.setAuditTime(model.getAuditTime());
        return productServiceReportMapper.update(updPo) > 0;
    }

    public boolean rejectByState(ProductServiceReportModel model) throws Exception {
        ProductServiceReportModel po = productServiceReportMapper.getById(model.getId());
        ProductServiceReportState productServiceReportState = get(po);
        return productServiceReportState.reject(model);
    }

    public boolean reject(ProductServiceReportModel model) throws Exception {
        ProductServiceReportModel updPo = new ProductServiceReportModel();
        updPo.setId(model.getId());
        updPo.setReportStatus(ProductServiceReportStatus.EVALUATING);
        updPo.setRejectTime(model.getRejectTime());
        updPo.setRejectUserId(model.getRejectUserId());
        updPo.setRejectUserName(model.getRejectUserName());
        updPo.setRejectReason(model.getRejectReason());
        return productServiceReportMapper.update(updPo) > 0;
    }

    public boolean sendPlatformByState(ProductServiceReportModel model) throws Exception {
        ProductServiceReportModel po = productServiceReportMapper.getById(model.getId());
        ProductServiceReportState productServiceReportState = get(po);
        po.setSendTime(model.getSendTime());
        return productServiceReportState.sendPlatform(po);
    }

    public boolean sendPlatform(ProductServiceReportModel po) throws Exception {
        ProductServiceReportModel updPo = new ProductServiceReportModel();
        updPo.setId(po.getId());
        updPo.setReportStatus(ProductServiceReportStatus.SENDED);
        updPo.setSendPlatform(true);
        updPo.setSendTime(po.getSendTime());
        return productServiceReportMapper.update(updPo) > 0;
    }

    public boolean sendMailByState(ProductServiceReportSendDTO dto) throws Exception {
        ProductServiceReportModel po = productServiceReportMapper.getById(dto.getId());
        ProductServiceReportState productServiceReportState = get(po);
        return productServiceReportState.sendMail(dto);
    }

    public boolean sendMail(ProductServiceReportSendDTO dto) {
        ProductServiceReportModel updPo = new ProductServiceReportModel();
        updPo.setId(dto.getId());
        updPo.setReportStatus(ProductServiceReportStatus.SENDED);
        updPo.setSendTime(dto.getSendTime());
        List<ProductServiceReportSendDTO.ProductServiceReportSendMail> sendMailList = dto.getSendMailList();
        if (!CollectionUtils.isEmpty(sendMailList)) {
            String mails = sendMailList.stream().map(ProductServiceReportSendDTO.ProductServiceReportSendMail::getEmail).distinct().collect(Collectors.joining(";"));
            updPo.setRecipientEmail(mails);
        }
        if (dto.getSendPlatform() != null) {
            updPo.setSendPlatform(dto.getSendPlatform());
        }
        boolean flag = productServiceReportMapper.update(updPo) > 0;
        ProductServiceReportModel po = productServiceReportMapper.getById(dto.getId());
        sendMail2MQ(po, dto);
        return flag;
    }

    public void sendMail2MQ(ProductServiceReportModel model, ProductServiceReportSendDTO dto) {
        ProductServiceReportMail mail = new ProductServiceReportMail();
        mail.setId(dto.getId());
        mail.setReportName(model.getCustomerFullName() + model.getProductName() + "产品服务报告(系统发送请勿回信)");
        mail.setRecipientEmail(model.getRecipientEmail());
        mail.setAttachmentUrl(dto.getAttachmentUrl());
        mail.setContent(dto.getContent());
        mail.setProductName(model.getProductName());
        mail.setFileName(dto.getFileName());
        log.info("send serviceReport:{}", JSONObject.toJSONString(mail));
        rabbitTemplate.convertAndSend(MqConstant.REPORT_EXCHANGE, MqConstant.SERVICE_REPORT_ROUTING_KEY, JSONObject.toJSONString(mail));
    }


    public boolean sendMailAndPlatformByState(ProductServiceReportSendDTO dto) throws Exception {
        ProductServiceReportModel po = productServiceReportMapper.getById(dto.getId());
        ProductServiceReportState productServiceReportState = get(po);
        return productServiceReportState.sendMailAndPlatform(dto);
    }

    public boolean sendMailAndPlatform(ProductServiceReportSendDTO dto) {
        dto.setSendPlatform(true);
        return sendMail(dto);
    }

    public List<ProductServiceReportOperationLog> getOperationLog(Long id) {
        return productServiceReportMapper.getByDataId(id);
    }

    @Autowired
    private EscloudMapper escloudMapper;

    @Autowired
    private ProductServiceReportBigDataService reportBigDataService;

    @Autowired
    @Qualifier("IChatGPTService")
    private IChatGPTService chatGPTService;

    @Autowired
    private ChatGptService chatGptService;

    private Integer retryCount = 3;
    private Integer retryIntervalSecond = 10;
    private String token = "7bb472a3-b51c-42ea-81c5-c08ca2d0248b";

    private int resultSize() {
        int resultSize = 0;
        Method[] methods = ProductServiceReport.class.getMethods();
        for (Method method : methods) {
            String name = method.getName();
            if (name.endsWith("Result")) {
                resultSize++;
            }
        }
        return resultSize;
    }

    public final String productServiceReportOutputMethod = "企业服务云";
    public final String productServiceReportServiceOverview = "鼎捷软件立足基础服务，进化赋能服务，迈向数智服务，为客户搭建起专业、完善、细致的服务体系，深入了解客户需求和行为，制定更贴近客户的服务。%s期间，为客户提供应用指导，流程讲解，补丁更新，账务核对，线上培训，安全巡检等服务，保障企业信息化系统稳定运行。";
    public final String basicApplicationEvaluationOverview = "基础应用评量从业务执行完整度、流程执行顺畅度、三大业务循环执行状况、月结周期四个方面分析企业基础应用的优点和不足之处，列举各方面的优缺点和问题，并提出针对性的建议和改进方案。";
    public final String processCompletionExplain = "业务流程执行完整度是指一个业务流程在执行过程中，每个步骤都被按照规定的顺序和方式完成的程度。这个指标通常与业务流程的规范程度和执行效率相关。如果业务流程执行完整度高，说明业务流程的规范性和执行效率较好，业务流程的目标可以更好地实现。相反，如果业务流程执行完整度低，则可能导致业务流程的延误、质量问题等，影响业务流程的效果和价值。";
    public final String processSmoothnessExplain = "ERP系统应该具备库存账实一致的能力，即在系统中及时录入审核更新库存信息，确保库存账面数量与实际库存数量相符，及时反映库存数量的变化，以便企业管理人员及时掌握库存状况，做好库存管理工作。";
    public final String circularBusinessExecutionStatusExplain = "三大业务循环执行顺畅度是衡量企业内部管理效率的重要指标，制单及时率和审核及时率的提高可以有效地提高企业内部管理的效率和准确性，有助于提高企业的运营效率和经济效益。";
    public final String billingCycleAnalysisExplain = "结账周期这个指标反映了企业在当月内对财务数据进行处理的效率和能力，也是企业管理和运营水平的一个重要表现。如果企业能够实现5日月结，说明企业的ERP系统操作流程比较规范，并且企业内部各个部门之间的沟通和协作比较有效，财务数据处理效率比较高。";
    public final String preface1 = "感谢您一直以来对我们公司的信任和支持。我们始终致力于为您提供高品质的产品和服务，不断进步和完善。为更好地了解贵司的需求和反馈，我们对%s至%s时间内的服务情况进行了总结。";
    public final String preface2 = "感谢您一直以来对我们公司的信任和支持。我们始终致力于为您提供高品质的产品和服务，不断进步和完善。为更好地了解贵司的需求和反馈，我们对%s至%s时间内的服务情况进行了总结，并进行服务器数据库安全效能健康体检。该体检主要针对服务器配置、服务器性能、数据库配置和数据库性能进行分析，旨在帮助客户降低因服务器安全效能问题导致的应用宕机、慢卡等状况的出现。";

    public final String total = "0000-01";

    public String resetProductServiceReportServiceOverview(ProductServiceReportGenerateReqDTO model) {
        LocalDate dataStartDate = model.getDataStartDate();
        LocalDate dataEndDate = model.getDataEndDate();
        String dataStartMonth = dataStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String dataEndMonth = dataEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String time = dataStartMonth + "-" + dataEndMonth;
        return String.format(productServiceReportServiceOverview, time);
    }

    private void saveProductServiceReport(ProductServiceReport report, ProductServiceReportGenerateReqDTO model) throws ExecutionException, InterruptedException {
        String token = RequestUtil.getHeaderToken();
        report.setCustomerName(model.getCustomerName());
        LocalDate dataEndDate = model.getDataEndDate();
        report.setYear(LocalDateTimeUtil.format(dataEndDate, DateTimeFormatter.ofPattern("yyyy")));
        report.setDataStartDate(model.getDataStartDate());
        report.setDataEndDate(model.getDataEndDate());
        report.setGenerateTime(model.getGenerateTime());
        report.setOutputMethod(productServiceReportOutputMethod);
        report.setServiceOverview(resetProductServiceReportServiceOverview(model));
        String[] analysisProjectCodes = model.getAnalysisProjectCodes();
        String productCode = model.getProductCode();
        if (ArrayUtils.isEmpty(analysisProjectCodes)) {
            return;
        }
        int countDownLatchSize = 0;
        boolean aiops = ArrayUtils.contains(analysisProjectCodes, "aiops");
        boolean basicServiceAnalysis = ArrayUtils.contains(analysisProjectCodes, "basicServiceAnalysis");
        if (basicServiceAnalysis) {
            countDownLatchSize += 7;
        }
        boolean basicApplicationEvaluation = ArrayUtils.contains(analysisProjectCodes, "basicApplicationEvaluation");
        if (basicApplicationEvaluation) {
            countDownLatchSize += 4;
        }
        CountDownLatch countDownLatch = new CountDownLatch(countDownLatchSize);
        if (aiops) {
            report.setPreface(String.format(preface2, model.getDataStartYMStr(), model.getDataEndYMStr()));
            //E10 Sqlserver体验报告内容 同时需要兼容E10半导体、易飞  需要兼容其他产品线
            SqlServerDbReport dbReport = asyncService.getDbReportData(buildDbReportRecord(model));
            buildReportByE10SqlServerDbReport(report, dbReport);
            Optional<String> questionOpt = ChatGptEvalType.SqlServerReportConclusion.getQuestion(reportTemplateService, dbReport);
            if (questionOpt.isPresent()) {
                try {
                    StringBuilder sb = new StringBuilder();
//                    sb.append("<div style=\"margin-left: 3em;\">");
//                    String gptConclusion = chatGptService.EvaluationConclusion(questionOpt.get());
//                    String gptConclusion = chatGptService.EvaluationConclusionV2(token, questionOpt.get());
//                    gptConclusion = gptConclusion.replaceAll("\n\n", "<p/>");
//                    gptConclusion = gptConclusion.replaceAll("\n", "<br/>");
                    sb.append(questionOpt.get());
//                    sb.append("</div>");
                    report.setEvaluateConclusionResult(sb.toString());
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("EvaluationConclusion error:{}", e.getMessage());
                }
            }
        } else {
            report.setPreface(String.format(preface1, model.getDataStartYMStr(), model.getDataEndYMStr()));
        }
        if (basicServiceAnalysis) {
            taskExecutor.submit(() -> {
                List<QuestionClosePO> questionCloseData = escloudMapper.getQuestionClose(model);
                QuestionClosePO questionCloseCount = escloudMapper.getQuestionCloseCount(model);
                if (!CollectionUtils.isEmpty(questionCloseData)) {
                    report.setQuestionTrendAnalysisData(questionCloseData);
                    if (aioItmsProperties.isProductServiceReportSend()) {
                        String content = buildProblemTrendsTemplates(productCode, questionCloseData);
                        questionCloseCount.setTime(total);
                        questionCloseData.add(questionCloseCount);
                        report.setQuestionTrendAnalysisResult(content);
                        countDownLatch.countDown();
//                        StringBuilder sb = new StringBuilder();
//                        String question = QuestionClosePO.getChatGptQuestion(questionCloseData);
//                        sb.append(question);
//                        Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
//                        responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
//                            @Override
//                            public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
//                                log.info("==========1.questionCloseData===========");
//                                if (log.isDebugEnabled()) {
//                                    log.debug(sb.toString());
//                                }
//                                log.info("-->{}", response.body().getData());
//                                report.setQuestionTrendAnalysisResult(response.body().getData());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
//                                log.error("questionCloseDataFail:{}", t.getMessage());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleRetry(Call<ChatGPTResponse> call) {
//                                log.warn("questionCloseDataRetry:{}", LocalDateTime.now());
//                            }
//                        });
                    } else {
                        countDownLatch.countDown();
                    }
                } else {
                    countDownLatch.countDown();
                }
            });

            /**
             * 2问题模块分析结果
             */
            taskExecutor.submit(() -> {
                List<QuestionClassificationPO> questionModuleData = escloudMapper.getQuestionModule(model);
                QuestionClassificationPO questionModuleCount = escloudMapper.getQuestionModuleCount(model);
                if (!CollectionUtils.isEmpty(questionModuleData)) {
                    report.setQuestionModuleAnalysisData(questionModuleData);
                    long sum = questionModuleData.stream().mapToLong(QuestionClassificationPO::getCount).sum();
                    for (QuestionClassificationPO questionModuleDatum : questionModuleData) {
                        if (sum == 0) {
                            questionModuleDatum.setRate(0.0);
                        } else {
                            questionModuleDatum.setRate(BigDecimal.valueOf(questionModuleDatum.getCount()).divide(BigDecimal.valueOf(sum), 4, RoundingMode.HALF_UP).doubleValue());
                        }
                    }
                    if (aioItmsProperties.isProductServiceReportSend()) {
                        String content = buildProblemClassificationTemplates(productCode, questionModuleData);
                        questionModuleCount.setName(total);
                        questionModuleCount.setRate(BigDecimal.valueOf(questionModuleCount.getCount()).divide(BigDecimal.valueOf(sum), 4, RoundingMode.HALF_UP).doubleValue());
                        questionModuleData.add(questionModuleCount);
                        report.setQuestionModuleAnalysisResult(content);
                        countDownLatch.countDown();
//                        StringBuilder sb = new StringBuilder();
//                        String question = QuestionClassificationPO.getChatGptQuestion4Module(questionModuleData);
//                        sb.append(question);
//                        Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
//                        responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
//                            @Override
//                            public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
//                                log.info("==========2.questionModuleData===========");
//                                if (log.isDebugEnabled()) {
//                                    log.debug(sb.toString());
//                                }
//                                log.info("-->{}", response.body().getData());
//                                report.setQuestionModuleAnalysisResult(response.body().getData());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
//                                log.error("questionModuleDataFail:{}", t.getMessage());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleRetry(Call<ChatGPTResponse> call) {
//                                log.warn("questionModuleDataRetry:{}", LocalDateTime.now());
//                            }
//                        });
                    } else {
                        countDownLatch.countDown();
                    }

                } else {
                    countDownLatch.countDown();
                }
            });
            /**
             * 3问题分类分析结果
             */
            taskExecutor.submit(() -> {
                List<QuestionClassificationPO> questionClassificationData = escloudMapper.getQuestionClassification(model);
                QuestionClassificationPO questionClassificationCount = escloudMapper.getQuestionClassificationCount(model);
                if (!CollectionUtils.isEmpty(questionClassificationData)) {
                    report.setQuestionClassificationAnalysisData(questionClassificationData);
                    long sum = questionClassificationData.stream().mapToLong(QuestionClassificationPO::getCount).sum();
                    for (QuestionClassificationPO questionClassificationDatum : questionClassificationData) {
                        if (sum == 0) {
                            questionClassificationDatum.setRate(0.0);
                        } else {
                            questionClassificationDatum.setRate(BigDecimal.valueOf(questionClassificationDatum.getCount()).divide(BigDecimal.valueOf(sum), 4, RoundingMode.HALF_UP).doubleValue());
                        }
                    }
                    if (aioItmsProperties.isProductServiceReportSend()) {
                        String content = buildProblemTypeTemplates(productCode, questionClassificationData);
                        questionClassificationCount.setName(total);
                        questionClassificationCount.setRate(BigDecimal.valueOf(questionClassificationCount.getCount()).divide(BigDecimal.valueOf(sum), 4, RoundingMode.HALF_UP).doubleValue());
                        questionClassificationData.add(questionClassificationCount);
                        report.setQuestionClassificationAnalysisResult(content);
                        countDownLatch.countDown();
//                        StringBuilder sb = new StringBuilder();
//                        String question = QuestionClassificationPO.getChatGptQuestion4Classification(questionClassificationData);
//                        sb.append(question);
//                        Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
//                        responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
//                            @Override
//                            public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
//                                log.info("==========3.questionClassificationData===========");
//                                if (log.isDebugEnabled()) {
//                                    log.debug(sb.toString());
//                                }
//                                log.info("-->{}", response.body().getData());
//                                report.setQuestionClassificationAnalysisResult(response.body().getData());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
//                                log.error("questionClassificationDataFail:{}", t.getMessage());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleRetry(Call<ChatGPTResponse> call) {
//                                log.warn("questionClassificationDataRetry:{}", LocalDateTime.now());
//                            }
//                        });
                    } else {
                        countDownLatch.countDown();
                    }
                } else {
                    countDownLatch.countDown();
                }
            });

            /**
             * 知识库点阅结果
             */
            taskExecutor.submit(() -> {
                //需求变更调整 选择E10或是E10半导体时，云管家知识库统计部分都传E10产品进行统计
                model.setTempProductCode(model.getProductCode());
                if ("165".equals(model.getProductCode())) {
                    model.setTempProductCode("37");
                }
                List<KnowledgeBaseReviewPO> knowledgeBaseReviewData = escloudMapper.getKnowledgeBaseReview(model);
                String content = buildKnowledgeBaseReviewDataTemplates(productCode, knowledgeBaseReviewData);
                report.setKnowledgeBaseReviewResult(content);
                if (!CollectionUtils.isEmpty(knowledgeBaseReviewData)) {
                    report.setKnowledgeBaseReviewData(knowledgeBaseReviewData);
                    countDownLatch.countDown();
//                    if (aioItmsProperties.isProductServiceReportSend()) {
//                        countDownLatch.countDown();
//                        StringBuilder sb = new StringBuilder();
//                        String question = KnowledgeBaseReviewPO.getChatGptQuestion(knowledgeBaseReviewData);
//                        sb.append(question);
//                        Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
//                        responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
//                            @Override
//                            public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
//                                log.info("==========4.knowledgeBaseReviewData===========");
//                                if (log.isDebugEnabled()) {
//                                    log.debug(sb.toString());
//                                }
//                                log.info("-->{}", response.body().getData());
//                                report.setKnowledgeBaseReviewResult(response.body().getData());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
//                                log.error("knowledgeBaseReviewDataFail:{}", t.getMessage());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleRetry(Call<ChatGPTResponse> call) {
//                                log.warn("knowledgeBaseReviewDataRetry:{}", LocalDateTime.now());
//                            }
//                        });
//                    } else {
//                        countDownLatch.countDown();
//                    }
                } else {
                    countDownLatch.countDown();
                }
            });
            /**
             * 知识库远程数据
             */
            taskExecutor.submit(() -> {
                List<KnowledgeBaseRemotePO> knowledgeBaseRemoteData = escloudMapper.getKnowledgeBaseRemote(model);
                String content = buildKnowledgeBaseRemoteTemplates(productCode, knowledgeBaseRemoteData);
                report.setKnowledgeBaseRemoteResult(content);
                if (!CollectionUtils.isEmpty(knowledgeBaseRemoteData)) {
                    report.setKnowledgeBaseRemoteData(knowledgeBaseRemoteData);
                    countDownLatch.countDown();
//                    if (aioItmsProperties.isProductServiceReportSend()) {
//                        countDownLatch.countDown();
//                        StringBuilder sb = new StringBuilder();
//                        String question = KnowledgeBaseRemotePO.getChatGptQuestion(knowledgeBaseRemoteData);
//                        sb.append(question);
//                        Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
//                        responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
//                            @Override
//                            public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
//                                log.info("==========5.knowledgeBaseRemoteData===========");
//                                if (log.isDebugEnabled()) {
//                                    log.debug(sb.toString());
//                                }
//                                log.info("-->{}", response.body().getData());
//                                report.setKnowledgeBaseRemoteResult(response.body().getData());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
//                                log.error("knowledgeBaseRemoteDataFail:{}", t.getMessage());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleRetry(Call<ChatGPTResponse> call) {
//                                log.warn("knowledgeBaseRemoteDataRetry:{}", LocalDateTime.now());
//                            }
//                        });
//                    } else {
//                        countDownLatch.countDown();
//                    }
                } else {
                    countDownLatch.countDown();
                }
            });
            /**
             * 满意度结果
             */
            taskExecutor.submit(() -> {
                List<SatisfactionPO> satisfactionData = escloudMapper.getSatisfaction(model);
                String content = buildSatisfactionTemplates(productCode, satisfactionData);
                report.setSatisfactionResult(content);
                if (!CollectionUtils.isEmpty(satisfactionData)) {
                    report.setSatisfactionData(satisfactionData);
                    countDownLatch.countDown();
//                    if (aioItmsProperties.isProductServiceReportSend()) {
//                        countDownLatch.countDown();
//                        StringBuilder sb = new StringBuilder();
//                        String question = SatisfactionPO.getChatGptQuestion(satisfactionData);
//                        sb.append(question);
//                        Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
//                        responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
//                            @Override
//                            public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
//                                log.info("==========6.satisfactionData===========");
//                                if (log.isDebugEnabled()) {
//                                    log.debug(sb.toString());
//                                }
//                                log.info("-->{}", response.body().getData());
//                                report.setSatisfactionResult(response.body().getData());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
//                                log.error("satisfactionDataFail:{}", t.getMessage());
//                                countDownLatch.countDown();
//                            }
//
//                            @Override
//                            public void handleRetry(Call<ChatGPTResponse> call) {
//                                log.warn("satisfactionDataRetry:{}", LocalDateTime.now());
//                            }
//                        });
//                    } else {
//                        countDownLatch.countDown();
//                    }
                } else {
                    countDownLatch.countDown();
                }
            });

            /**
             * 组织能力提升分析
             */
            taskExecutor.submit(() -> {
                List<Map<String, Object>> trainCourseList = getTrainCourse(model);
                if (!CollectionUtils.isEmpty(trainCourseList)) {
                    report.setYearTrainCourseArrangementData(trainCourseList);
                }
                countDownLatch.countDown();

            });
        }

        if (basicApplicationEvaluation) {
            report.setBasicApplicationEvaluationOverview(basicApplicationEvaluationOverview);
            /**
             * 完成度
             */
            report.setProcessCompletionExplain(processCompletionExplain);
            taskExecutor.submit(() -> {
                List<ProcessCompletionPO> processCompletionData = reportBigDataService.getProcessCompletionData(model);
                if (!CollectionUtils.isEmpty(processCompletionData)) {
                    report.setProcessCompletionData(processCompletionData);
                    if (aioItmsProperties.isProductServiceReportSend()) {
                        StringBuilder sb = new StringBuilder();
                        String question = ChatGPTQuestion.getQuestion(processCompletionData);
                        sb.append(question).append(ProcessCompletionPO.getDefaultRemark());
                        Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
                        responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
                            @Override
                            public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                                log.info("==========processCompletionData===========");
                                if (log.isDebugEnabled()) {
                                    log.debug(sb.toString());
                                }
                                log.info("-->{}", response.body().getData());
                                report.setProcessCompletionResult(response.body().getData());
                                countDownLatch.countDown();
                            }

                            @Override
                            public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                                log.error("processCompletionDataFail:{}", t.getMessage());
                                countDownLatch.countDown();
                            }

                            @Override
                            public void handleRetry(Call<ChatGPTResponse> call) {
                                log.warn("processCompletionDataRetry:{}", LocalDateTime.now());
                            }
                        });
                    } else {
                        countDownLatch.countDown();
                    }
                } else {
                    countDownLatch.countDown();
                }
            });

            /**
             * 流程执行顺畅度数据
             */
            report.setProcessSmoothnessExplain(processSmoothnessExplain);
            taskExecutor.submit(() -> {
                List<WarehouseInventoryControlAbilityPO> warehouseInventoryControlAbilityData = reportBigDataService.getWarehouseInventoryControlAbility(model);
                if (!CollectionUtils.isEmpty(warehouseInventoryControlAbilityData)) {
                    List<DocTimelyRatePO> docTimelyRateData = reportBigDataService.getDocTimelyRate(model);
                    report.setWarehouseInventoryControlAbilityData(warehouseInventoryControlAbilityData);
                    if (aioItmsProperties.isProductServiceReportSend()) {
                        StringBuilder sb = new StringBuilder();
                        String question = WarehouseInventoryControlAbilityPO.getChatGptQuestion(warehouseInventoryControlAbilityData, docTimelyRateData);
                        sb.append(question).append(WarehouseInventoryControlAbilityPO.getDefaultRemark());
                        Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
                        responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
                            @Override
                            public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                                log.info("==========warehouseInventoryControlAbilityData===========");
                                if (log.isDebugEnabled()) {
                                    log.debug(sb.toString());
                                }
                                log.info("-->{}", response.body().getData());
                                report.setProcessSmoothnessResult(response.body().getData());
                                countDownLatch.countDown();
                            }

                            @Override
                            public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                                log.error("warehouseInventoryControlAbilityDataFail:{}", t.getMessage());
                                countDownLatch.countDown();
                            }

                            @Override
                            public void handleRetry(Call<ChatGPTResponse> call) {
                                log.warn("warehouseInventoryControlAbilityDataRetry:{}", LocalDateTime.now());
                            }
                        });
                    } else {
                        countDownLatch.countDown();
                    }
                } else {
                    countDownLatch.countDown();
                }
            });

            /**
             * 循环业务执行状况结果
             */
            report.setCircularBusinessExecutionStatusExplain(circularBusinessExecutionStatusExplain);
            Future<String> processSmoothnessPurchasePayableRes = taskExecutor.submit(() -> {
                List<ProcessSmoothnessPO> processSmoothnessPurchasePayableData = reportBigDataService.getProcessSmoothnessPurchasePayable(model);
                report.setProcessSmoothnessPurchasePayableData(processSmoothnessPurchasePayableData);
                StringBuilder sb = new StringBuilder();
                if (!CollectionUtils.isEmpty(processSmoothnessPurchasePayableData)) {
                    String question = ChatGPTQuestion.getQuestion(processSmoothnessPurchasePayableData);
                    sb.append(question);
                }
                return sb.toString();
            });
            Future<String> processSmoothnessSalesReceivableRes = taskExecutor.submit(() -> {
                List<ProcessSmoothnessPO> processSmoothnessSalesReceivableData = reportBigDataService.getProcessSmoothnessSalesReceivable(model);
                report.setProcessSmoothnessSalesReceivableData(processSmoothnessSalesReceivableData);
                StringBuilder sb = new StringBuilder();
                if (!CollectionUtils.isEmpty(processSmoothnessSalesReceivableData)) {
                    String question = ChatGPTQuestion.getQuestion(processSmoothnessSalesReceivableData);
                    sb.append(question);
                }
                return sb.toString();
            });
            Future<String> processSmoothnessProductionManagementRes = taskExecutor.submit(() -> {
                List<ProcessSmoothnessPO> processSmoothnessProductionManagementData = reportBigDataService.getProcessSmoothnessProductionManagement(model);
                report.setProcessSmoothnessProductionManagementData(processSmoothnessProductionManagementData);
                StringBuilder sb = new StringBuilder();
                if (!CollectionUtils.isEmpty(processSmoothnessProductionManagementData)) {
                    String question = ChatGPTQuestion.getQuestion(processSmoothnessProductionManagementData);
                    sb.append(question);
                }
                return sb.toString();
            });

            /**
             * 结账周期
             */
            report.setBillingCycleAnalysisExplain(billingCycleAnalysisExplain);
            Future<String> costSetCycleInventoryRes = taskExecutor.submit(() -> {
                List<CostSetCyclePO> costSetCycleInventoryData = reportBigDataService.getCostSetCycleInventory(model);
                StringBuilder sb = new StringBuilder();
                if (!CollectionUtils.isEmpty(costSetCycleInventoryData)) {
                    report.setCostSetCycleInventoryData(costSetCycleInventoryData);
                    String question = CostSetCyclePO.getChatGptQuestion(costSetCycleInventoryData);
                    sb.append(question);
                }
                return sb.toString();
            });
            Future<String> costSetCycleLedgerRes = taskExecutor.submit(() -> {
                List<CostSetCyclePO> costSetCycleLedgerData = reportBigDataService.getCostSetCycleLedger(model);
                StringBuilder sb = new StringBuilder();
                if (!CollectionUtils.isEmpty(costSetCycleLedgerData)) {
                    report.setCostSetCycleLedgerData(costSetCycleLedgerData);
                    String question = CostSetCyclePO.getChatGptQuestion(costSetCycleLedgerData);
                    sb.append(question);
                }
                return sb.toString();
            });
            Future<String> costSetCycleReceivableRes = taskExecutor.submit(() -> {
                List<CostSetCyclePO> costSetCycleReceivableData = reportBigDataService.getCostSetCycleReceivable(model);
                StringBuilder sb = new StringBuilder();
                if (!CollectionUtils.isEmpty(costSetCycleReceivableData)) {
                    report.setCostSetCycleReceivableData(costSetCycleReceivableData);
                    String question = CostSetCyclePO.getChatGptQuestion(costSetCycleReceivableData);
                    sb.append(question);
                }
                return sb.toString();
            });
            Future<String> costSetCyclePayableRes = taskExecutor.submit(() -> {
                List<CostSetCyclePO> costSetCyclePayableData = reportBigDataService.getCostSetCyclePayableData(model);
                StringBuilder sb = new StringBuilder();
                if (!CollectionUtils.isEmpty(costSetCyclePayableData)) {
                    report.setCostSetCyclePayableData(costSetCyclePayableData);
                    String question = CostSetCyclePO.getChatGptQuestion(costSetCyclePayableData);
                    sb.append(question);
                }
                return sb.toString();
            });
            Future<String> costSetCycleDaysRes = taskExecutor.submit(() -> {
                List<CostSetCyclePO> costSetCycleDaysData = reportBigDataService.getCostSetCycleDaysData(model);
                StringBuilder sb = new StringBuilder();
                if (!CollectionUtils.isEmpty(costSetCycleDaysData)) {
                    report.setCostSetCycleDaysData(costSetCycleDaysData);
                    String question = CostSetCyclePO.getChatGptQuestion(costSetCycleDaysData);
                    sb.append(question);
                }
                return sb.toString();
            });
            buildCostSetCycleResult(report, countDownLatch, costSetCycleInventoryRes, costSetCycleLedgerRes, costSetCycleReceivableRes, costSetCyclePayableRes, costSetCycleDaysRes);
            buildProcessSmoothnessResult(report, countDownLatch, processSmoothnessPurchasePayableRes, processSmoothnessSalesReceivableRes, processSmoothnessProductionManagementRes);
        }
        System.out.println(countDownLatch.getCount());
//        countDownLatch.await(5, TimeUnit.MINUTES);
        boolean awaitResult = countDownLatch.await(20, TimeUnit.MINUTES); //修复bug 需要判断计数是否到零结束
        if (!awaitResult) {
            // 如果等待被中断或超时，抛出异常或执行其他操作
            throw new RuntimeException("Failed to get GPT response within the specified timeout.");
        }
        log.info("report-->{}", JSONUtil.toJsonStr(report));
        productServiceReportEsService.save(report, true);
        ProductServiceReportModel poModel = new ProductServiceReportModel();
        poModel.setReportStatus(ProductServiceReportStatus.EVALUATING);
        poModel.setId(model.getId());
        productServiceReportMapper.update(poModel);
    }

    private String buildKnowledgeBaseRemoteTemplates(String productCode,
                                                     List<KnowledgeBaseRemotePO> knowledgeBaseRemoteData) {

        Map<String, Object> params = new HashMap<>();
        params.put("modelType", ReportConclusionTypeEnum.KNOWLEDGE_BASE_REMOTE.getConclusionType());
        if (!CollectionUtils.isEmpty(knowledgeBaseRemoteData)) {
            params.put("knowledgeBaseRemoteDataNotEmpty", true);
            params.put("avgNum", Long.valueOf(String.format("%.0f",
                    knowledgeBaseRemoteData.stream()
                            .mapToDouble(KnowledgeBaseRemotePO::getCount).sum() / knowledgeBaseRemoteData.size()))
            );

            params.put("avgDuration", knowledgeBaseRemoteData.stream()
                    .map(KnowledgeBaseRemotePO::getDuration)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(new BigDecimal(knowledgeBaseRemoteData.size()), 0, RoundingMode.HALF_UP)
            );

            //判断是否连续增长

            boolean countAsc = TrendAnalysisUtils.isContinuousAscendingLongType(
                    knowledgeBaseRemoteData.stream()
                            .map(KnowledgeBaseRemotePO::getCount)
                            .collect(Collectors.toList())
            );
            boolean durationAsc = TrendAnalysisUtils.isContinuousAscending(
                    knowledgeBaseRemoteData.stream()
                            .map(KnowledgeBaseRemotePO::getDuration)
                            .collect(Collectors.toList())
            );
            params.put("upwardTrend", durationAsc || countAsc);

            if (durationAsc && countAsc) {
                params.put("knowledgeBaseRemoteTypeAsc", 3);
            } else if (countAsc) {
                params.put("knowledgeBaseRemoteTypeAsc", 1);
            } else if (durationAsc) {
                params.put("knowledgeBaseRemoteTypeAsc", 2);
            }

            //判断是否连续下降
            boolean durationDesc = TrendAnalysisUtils.isContinuousDescending(
                    knowledgeBaseRemoteData.stream()
                            .map(KnowledgeBaseRemotePO::getDuration)
                            .collect(Collectors.toList())
            );
            boolean countDesc = TrendAnalysisUtils.isContinuousDescendingLongType(
                    knowledgeBaseRemoteData.stream()
                            .map(KnowledgeBaseRemotePO::getCount)
                            .collect(Collectors.toList())
            );
            params.put("downwardTrend", durationDesc || countDesc);
            if (durationDesc && countDesc) {
                params.put("knowledgeBaseRemoteTypeDesc", 3);
            } else if (countDesc) {
                params.put("knowledgeBaseRemoteTypeDesc", 1);
            } else if (durationDesc) {
                params.put("knowledgeBaseRemoteTypeDesc", 2);
            }
        } else {
            params.put("knowledgeBaseRemoteDataNotEmpty", false);
        }
        String reportTemplate = reportTemplateService.getReportTemplate(EXAMINATION_REPORT, DEFAULT_PRODUCT_CODE,
                ReportConclusionTypeEnum.KNOWLEDGE_BASE_REMOTE.getConclusionType(), params);
        return reportTemplate;
    }


    private String buildSatisfactionTemplates(String productCode, List<SatisfactionPO> satisfactionData) {
        Map<String, Object> params = new HashMap<>();
        params.put("modelType", ReportConclusionTypeEnum.SATISFACTION.getConclusionType());
        if (!CollectionUtils.isEmpty(satisfactionData)) {
            params.put("satisfactionDataNotEmpty", true);
            params.put("reportStartTime", satisfactionData.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getTime()))
                    .map(SatisfactionPO::getTime)
                    .findFirst()
                    .orElse("")
            );
            params.put("reportEndTime", satisfactionData.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getTime()))
                    .map(SatisfactionPO::getTime)
                    .reduce((first, second) -> second)
                    .orElse("")
            );

            boolean satisfied = satisfactionData.stream()
                    .allMatch(item -> item.getSatisfaction().compareTo(new BigDecimal("10")) == 0);

            if (satisfied) {
                params.put("allTen", true);
            } else {
                params.put("allTen", false);
                boolean verySatisfied = satisfactionData.stream()
                        .anyMatch(item -> item.getSatisfaction().compareTo(BigDecimal.valueOf(10.0)) == 0);
                params.put("verySatisfied", verySatisfied);

                params.put("verySatisfiedNum", satisfactionData.stream()
                        .filter(item -> item.getSatisfaction().compareTo(BigDecimal.valueOf(10.0)) == 0)
                        .collect(Collectors.toList()).size());

                boolean moreSatisfied = satisfactionData.stream()
                        .anyMatch(num -> num.getSatisfaction().compareTo(new BigDecimal("6")) >= 0 &&
                                num.getSatisfaction().compareTo(new BigDecimal("10")) < 0);
                params.put("moreSatisfied", moreSatisfied);

                params.put("moreSatisfiedNum", satisfactionData.stream()
                        .filter(num -> num.getSatisfaction().compareTo(new BigDecimal("6")) >= 0 &&
                                num.getSatisfaction().compareTo(new BigDecimal("10")) < 0)
                        .collect(Collectors.toList()).size()
                );
                boolean normalSatisfied = satisfactionData.stream()
                        .anyMatch(num -> num.getSatisfaction().compareTo(new BigDecimal("6")) < 0);
                params.put("normalSatisfied", normalSatisfied);
                params.put("normalSatisfiedNum", satisfactionData.stream()
                        .filter(num -> num.getSatisfaction().compareTo(new BigDecimal("6")) < 0)
                        .collect(Collectors.toList()).size()
                );
            }
        } else {
            params.put("satisfactionDataNotEmpty", false);
        }
        String reportTemplate = reportTemplateService.getReportTemplate(EXAMINATION_REPORT, DEFAULT_PRODUCT_CODE, ReportConclusionTypeEnum.SATISFACTION.getConclusionType(), params);
        return reportTemplate;

    }

    private String buildKnowledgeBaseReviewDataTemplates(String productCode,
                                                         List<KnowledgeBaseReviewPO> knowledgeBaseReviewData) {

        Map<String, Object> params = new HashMap<>();
        params.put("modelType", ReportConclusionTypeEnum.KNOWLEDGE_BASE_REVIEW.getConclusionType());
        if (!CollectionUtils.isEmpty(knowledgeBaseReviewData)) {
            params.put("knowledgeDataNotEmpty", true);
            params.put("reportStartTime", knowledgeBaseReviewData.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getTime()))
                    .map(KnowledgeBaseReviewPO::getTime)
                    .findFirst()
                    .orElse("")
            );

            List<KnowledgeBaseReviewPO> searchList = knowledgeBaseReviewData.stream()
                    .filter(item -> LongUtil.isNotEmpty(item.getSearchCount()))
                    .collect(Collectors.toList());

            double avgSearch = searchList.stream()
                    .mapToDouble(KnowledgeBaseReviewPO::getSearchCount).sum() / searchList.size();
            avgSearch = Double.isNaN(avgSearch) ? 0 : avgSearch;
            params.put("avgSearches", avgSearch > 0 ? String.format("%.1f", avgSearch) : 0);

            List<KnowledgeBaseReviewPO> readList = knowledgeBaseReviewData.stream()
                    .filter(item -> LongUtil.isNotEmpty(item.getReadCount()))
                    .collect(Collectors.toList());

            double avgClicks = readList.stream()
                    .mapToDouble(KnowledgeBaseReviewPO::getReadCount).sum() / readList.size();
            avgClicks = Double.isNaN(avgClicks) ? 0 : avgClicks;

            params.put("avgClicks", avgClicks > 0 ? String.format("%.1f", avgClicks) : 0);

            params.put("preference", avgSearch > avgClicks);

            params.put("reportEndTime", knowledgeBaseReviewData.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getTime()))
                    .map(KnowledgeBaseReviewPO::getTime).reduce((first, second) -> second)
                    .orElse("")
            );

            boolean searchAsc = TrendAnalysisUtils.isContinuousAscendingLongType(
                    knowledgeBaseReviewData.stream()
                            .map(KnowledgeBaseReviewPO::getSearchCount)
                            .collect(Collectors.toList())
            );

            boolean readAsc = TrendAnalysisUtils.isContinuousAscendingLongType(
                    knowledgeBaseReviewData.stream()
                            .map(KnowledgeBaseReviewPO::getReadCount)
                            .collect(Collectors.toList())
            );

            params.put("upwardTrend", searchAsc || readAsc);
            if (searchAsc && readAsc) {
                params.put("knowledgeBaseTypeAsc", 3);
            } else if (searchAsc) {
                params.put("knowledgeBaseTypeAsc", 1);
            } else if (readAsc) {
                params.put("knowledgeBaseTypeAsc", 2);
            }
            boolean searchDesc = TrendAnalysisUtils.isContinuousDescendingLongType(
                    knowledgeBaseReviewData.stream().map(KnowledgeBaseReviewPO::getSearchCount)
                            .collect(Collectors.toList())
            );
            boolean readDesc = TrendAnalysisUtils.isContinuousDescendingLongType(
                    knowledgeBaseReviewData.stream().map(KnowledgeBaseReviewPO::getReadCount)
                            .collect(Collectors.toList())
            );
            params.put("downwardTrend", searchDesc || readDesc);
            if (searchDesc && readDesc) {
                params.put("knowledgeBaseTypeDesc", 3);
            } else if (searchDesc) {
                params.put("knowledgeBaseTypeDesc", 1);
            } else if (readDesc) {
                params.put("knowledgeBaseTypeDesc", 2);
            }

        } else {
            params.put("knowledgeDataNotEmpty", false);
        }
        String reportTemplate =
                reportTemplateService.getReportTemplate(EXAMINATION_REPORT, DEFAULT_PRODUCT_CODE, ReportConclusionTypeEnum.KNOWLEDGE_BASE_REVIEW.getConclusionType(), params);
        return reportTemplate;
    }

    private String buildProblemTypeTemplates(String productCode, List<QuestionClassificationPO> questionClassificationData) {
        Map<String, Object> params = new HashMap<>();
        params.put("modelType", ReportConclusionTypeEnum.PROBLEM_TYPE.getConclusionType());
        params.put("problemTypeCount", questionClassificationData.size());
        params.put("questionClassificationDataList", questionClassificationData.stream()
                .filter(item -> DoubleUtil.isNotEmpty(item.getRate()))
                .sorted(Comparator.comparing(QuestionClassificationPO::getRate).reversed())
                .collect(Collectors.toList())
        );

        String reportTemplate = reportTemplateService.getReportTemplate(EXAMINATION_REPORT,
                DEFAULT_PRODUCT_CODE, ReportConclusionTypeEnum.PROBLEM_TYPE.getConclusionType(), params);
        return reportTemplate;
    }

    private String buildProblemClassificationTemplates(String productCode, List<QuestionClassificationPO> questionModuleData) {
        Map<String, Object> params = new HashMap<>();
        params.put("modelType", ReportConclusionTypeEnum.PROBLEM_CLASSIFICATION.getConclusionType());
        params.put("templateSpecies", questionModuleData.size());
        params.put("questionModuleDataList", questionModuleData.stream()
                .filter(item -> DoubleUtil.isNotEmpty(item.getRate()))
                .sorted((o1, o2) -> {
                    if (o1.getRate().equals(o2.getRate())) {
                        if ("跨模块".equals(o1.getName()) && !"跨模块".equals(o2.getName())) {
                            return -1;
                        } else if (!"跨模块".equals(o1.getName()) && "跨模块".equals(o2.getName())) {
                            return 1;
                        } else {
                            return 0;
                        }
                    } else {
                        return o2.getRate().compareTo(o1.getRate());
                    }
                }).collect(Collectors.toList()));

        params.put("proportion", questionModuleData.subList(0, 1).stream()
                .filter(item -> DoubleUtil.isNotEmpty(item.getRate()))
                .mapToDouble(QuestionClassificationPO::getRate)
                .sum()
        );

        String reportTemplate = reportTemplateService.getReportTemplate(EXAMINATION_REPORT, DEFAULT_PRODUCT_CODE,
                ReportConclusionTypeEnum.PROBLEM_CLASSIFICATION.getConclusionType(), params);
        return reportTemplate;
    }

    private String buildProblemTrendsTemplates(String productCode, List<QuestionClosePO> questionCloseData) {
        Map<String, Object> params = new HashMap<>();
        params.put("modelType", ReportConclusionTypeEnum.PROBLEM_TRENDS.getConclusionType());

        params.put("reportStartTime", questionCloseData.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getTime()))
                .map(QuestionClosePO::getTime)
                .findFirst()
                .orElse("")
        );

        params.put("reportEndTime", questionCloseData.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getTime()))
                .map(QuestionClosePO::getTime)
                .reduce((first, second) -> second)
                .orElse("")
        );

        List<QuestionClosePO> filterNumCloseList = Optional.ofNullable(
                questionCloseData.stream()
                        .filter(item -> LongUtil.isNotEmpty(item.getCount()))
                        .collect(Collectors.toList())
        ).orElse(new ArrayList<>());

        params.put("maxProblem", filterNumCloseList.stream()
                .map(QuestionClosePO::getCount)
                .max(Comparator.comparing(s -> s))
                .orElse(0L)
        );

        params.put("minProblem", filterNumCloseList.stream()
                .map(QuestionClosePO::getCount)
                .min(Comparator.comparing(s -> s))
                .orElse(0L)
        );

        double averageCase = filterNumCloseList.stream()
                .mapToDouble(QuestionClosePO::getCount).sum() / filterNumCloseList.size();
        averageCase = Double.isNaN(averageCase) ? 0 : averageCase;

        params.put("averageCase", Long.parseLong(String.format("%.0f", averageCase)));

        params.put("maxMonth", filterNumCloseList.stream()
                .max(Comparator.comparing(QuestionClosePO::getCount))
                .map(QuestionClosePO::getTime)
                .orElse(null)
        );

        params.put("closed", questionCloseData.stream()
                .map(QuestionClosePO::getThreeDayCloseRate)
                .allMatch(s -> s > 0));

        params.put("closureRate", questionCloseData.stream()
                .map(QuestionClosePO::getThreeDayCloseRate)
                .allMatch(s -> s == 1.0));

        params.put("minClosureRate", questionCloseData.stream()
                .filter(item -> item.getThreeDayCloseRate() > 0)
                .min(Comparator.comparing(QuestionClosePO::getThreeDayCloseRate))
                .map(QuestionClosePO::getThreeDayCloseRate)
                .orElse(null)
        );

        params.put("closureNum", questionCloseData.stream()
                .filter(s -> DoubleUtil.isNotEmpty(s.getThreeDayCloseRate()))
                .filter(item -> item.getThreeDayCloseRate() == 1.0).collect(Collectors.toList()).size());

        String reportTemplate = reportTemplateService.getReportTemplate(EXAMINATION_REPORT, DEFAULT_PRODUCT_CODE,
                ReportConclusionTypeEnum.PROBLEM_TRENDS.getConclusionType(), params);

        return reportTemplate;
    }

    private void buildReportByE10SqlServerDbReport(ProductServiceReport report, SqlServerDbReport dbReport) {
        report.setServerConfiguration(dbReport.getServerConfiguration());
        report.setServerHealth(dbReport.getServerHealth());
        report.setDatabaseHealth(dbReport.getDatabaseHealth());
        report.setDatabaseBackup(dbReport.getDatabaseBackup());
        report.setInformationSecurity(dbReport.getInformationSecurity());
    }

    private DbReportRecord buildDbReportRecord(ProductServiceReportGenerateReqDTO dto) {
        DbReportRecord model = new DbReportRecord();
        BeanUtils.copyProperties(dto, model);
        //判断检测值是否包含对应的产品线，不包含使用9999
        List<String> reportTypeList = dbReportMapper.getReportType();
        String currentDbType = model.getCurrentType();
        if (!reportTypeList.contains(currentDbType)) {
            model.setUseDefReportType(true);
        }
        return model;
    }


    public void buildCostSetCycleResult(ProductServiceReport report, CountDownLatch countDownLatch, Future<String> costSetCycleInventoryRes, Future<String> costSetCycleLedgerRes,
                                        Future<String> costSetCycleReceivableRes, Future<String> costSetCyclePayableRes, Future<String> costSetCycleDaysRes) throws ExecutionException, InterruptedException {
        String costSetCycleInventoryQ = costSetCycleInventoryRes.get();
        String costSetCycleLedgerQ = costSetCycleLedgerRes.get();
        String costSetCycleReceivableQ = costSetCycleReceivableRes.get();
        String costSetCyclePayableQ = costSetCyclePayableRes.get();
        String costSetCycleDaysQ = costSetCycleDaysRes.get();
        String costSetCycleQ = costSetCycleInventoryQ + costSetCycleLedgerQ + costSetCycleReceivableQ + costSetCyclePayableQ + costSetCycleDaysQ;
        if (!StringUtils.isEmpty(costSetCycleQ) && aioItmsProperties.isProductServiceReportSend()) {
            String question = costSetCycleQ + ChatGPTQuestion.getDefaultRemark();
            Call<ChatGPTResponse> costSetCycleResp = chatGPTService.getChatGPTContent(token, question);
            costSetCycleResp.enqueue(new RetryCallback<ChatGPTResponse>(costSetCycleResp, retryCount, retryIntervalSecond) {
                @Override
                public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                    log.info("==========costSetCycleQ===========");
                    if (log.isDebugEnabled()) {
                        log.debug(question);
                    }
                    log.info("-->{}", response.body().getData());
                    report.setCostSetCycleResult(response.body().getData());
                    countDownLatch.countDown();
                }

                @Override
                public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                    log.error("costSetCycleQFail:{}", t.getMessage());
                    countDownLatch.countDown();
                }

                @Override
                public void handleRetry(Call<ChatGPTResponse> call) {
                    log.warn("costSetCycleQRetry:{}", LocalDateTime.now());

                }
            });
        } else {
            countDownLatch.countDown();
        }
    }

    public void buildProcessSmoothnessResult(ProductServiceReport report, CountDownLatch countDownLatch, Future<String> processSmoothnessPurchasePayableRes, Future<String> processSmoothnessSalesReceivableRes, Future<String> processSmoothnessProductionManagementRes) throws ExecutionException, InterruptedException {
        String processSmoothnessPurchasePayableQ = processSmoothnessPurchasePayableRes.get();
        String processSmoothnessSalesReceivableQ = processSmoothnessSalesReceivableRes.get();
        String processSmoothnessProductionManagementQ = processSmoothnessProductionManagementRes.get();
        String processSmoothnessQ = processSmoothnessPurchasePayableQ + processSmoothnessSalesReceivableQ + processSmoothnessProductionManagementQ;
        if (!StringUtils.isEmpty(processSmoothnessQ) && aioItmsProperties.isProductServiceReportSend()) {
            String question = processSmoothnessQ + ChatGPTQuestion.getDefaultRemark();
            Call<ChatGPTResponse> processSmoothnessResp = chatGPTService.getChatGPTContent(token, question);
            processSmoothnessResp.enqueue(new RetryCallback<ChatGPTResponse>(processSmoothnessResp, retryCount, retryIntervalSecond) {
                @Override
                public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                    log.info("==========processSmoothnessQ===========");
                    if (log.isDebugEnabled()) {
                        log.debug(question);
                    }
                    log.info("-->{}", response.body().getData());
                    report.setCircularBusinessExecutionStatusResult(response.body().getData());
                    countDownLatch.countDown();
                }

                @Override
                public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                    log.error("processSmoothnessQ:{}", t.getMessage());
                    countDownLatch.countDown();
                }

                @Override
                public void handleRetry(Call<ChatGPTResponse> call) {
                    log.warn("warehouseInventoryControlAbilityDataRetry:{}", LocalDateTime.now());
                }
            });
        } else {
            countDownLatch.countDown();
        }
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        productServiceReportStateHandler = new ProductServiceReportStateHandler(this);
    }

    public ProductServiceReportState get(ProductServiceReportModel model) {
        return productServiceReportStateHandler.getState(model);
    }

    public List<ProductServiceReportAnalysisProject> getAnalysisProject() {
        return productServiceReportMapper.getAnalysisProject();
    }

    private List<Map<String, Object>> getTrainCourse(ProductServiceReportGenerateReqDTO model) {
        return buildCourseSql(model, bigDataUtil);
    }

    private void settingAccountList(ProductServiceReportGenerateReqDTO model) {
        if (!org.springframework.util.StringUtils.isEmpty(model.getEntCode()) && CollectionUtils.isEmpty(model.getAccountList())) {
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT DISTINCT ");
            sb.append(" Account_Set AS t100_account_set_code, Account_set_name AS account_name_cn, Account_set_name AS account_name_tw");
            sb.append(" FROM ")
                    .append(bigDataUtil.getSrDbName()).append(".AccountSetInformation ");
            sb.append(" WHERE ")
                    .append(" eid = '" + model.getEid() + "'")
                    .append(" AND ifnull( enterpriseCode, '' ) != ''")
                    .append(" AND Product_Line = '" + model.getProductLine() + "'")
                    .append(" AND enterpriseCode = '" + model.getEntCode() + "'");
            sb.append(" ORDER BY Account_Set ");
            List<Map<String, Object>> mapList = bigDataUtil.srQuery(sb.toString());
            if (!CollectionUtils.isEmpty(mapList)) {
                ArrayList<ProductServiceReportAccountModel> accountList = new ArrayList<>(mapList.size());
                for (Map<String, Object> map : mapList) {
                    ProductServiceReportAccountModel accountModel = new ProductServiceReportAccountModel();
                    accountModel.setProductServiceReportId(model.getId());
                    accountModel.setAccountCode(map.getOrDefault("t100_account_set_code", "").toString());
                    String accountName = "CN".equals(serviceArea) ?
                            map.getOrDefault("account_name_cn", "").toString() :
                            map.getOrDefault("account_name_tw", "").toString();
                    accountModel.setAccountName(accountName);
                    accountList.add(accountModel);
                }
                model.setAccountList(accountList);
            }
        }
    }

    public static List<Map<String, Object>> buildCourseSql(ProductServiceReportGenerateReqDTO model, BigDataUtil bigDataUtil) {
        StringBuilder sb = new StringBuilder();

        sb.append(" SELECT tc.productCategory, tc.subject, tc.courseDate, ")
                .append(" (select count(DISTINCT courseDate, productCategory, subject, contactInfo, serviceCode) ")
                .append(" FROM ").append(bigDataUtil.getSrDbName()).append(".TrainCourseDetail ")
                .append(" WHERE subject=tc.subject")
                .append(" AND productCategory = tc.productCategory")
                .append(" AND courseDate = tc.courseDate")
                .append(" AND courseDate BETWEEN '")
                .append(model.getDataStartDate() + "' AND '" + model.getDataEndDate() + "'")
                .append(" AND eid=" + model.getEid())
                .append(" ) AS participantCount ");
        sb.append(" FROM ").append(bigDataUtil.getSrDbName()).append(".TrainCourse tc ");
        sb.append(" WHERE ")
                .append(" tc.productCategory='" + model.getProductName() + "'")
                .append(" AND tc.courseDate BETWEEN '")
                .append(model.getDataStartDate() + "' AND '" + model.getDataEndDate() + "'");
        sb.append(" ORDER BY tc.courseDate ASC, participantCount DESC");

        log.info("[buildCourseSql] SQL: {}", sb);
        List<Map<String, Object>> mapList = bigDataUtil.srQuery(sb.toString());
        if (!CollectionUtils.isEmpty(mapList)) {
            return mapList;
        }
        return new ArrayList<>(0);
    }
}
