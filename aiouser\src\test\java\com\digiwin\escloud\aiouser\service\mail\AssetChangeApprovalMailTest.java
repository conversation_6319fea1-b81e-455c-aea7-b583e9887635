package com.digiwin.escloud.aiouser.service.mail;

import com.digiwin.escloud.aiocmdb.model.assetchange.AssetChangeApplication;
import com.digiwin.escloud.aiouser.model.common.Invitation;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 资产变更审批邮件测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class AssetChangeApprovalMailTest {

    @Autowired
    private ActiveApproverMail activeApproverMail;

    @Test
    public void testAssetChangeApprovalMail() {
        // 创建测试数据
        Invitation invitation = new Invitation();
        invitation.setId(1L);
        invitation.setInvitedEmail("<EMAIL>");
        invitation.setInvitedName("张三");
        invitation.setLinkUrl("https://escloud.digiwin.com/approval/123");

        // 创建资产变更申请单
        AssetChangeApplication application = new AssetChangeApplication();
        application.setId(1L);
        application.setApplicationNumber("CR-20250815-001");
        application.setApplicationDate(LocalDate.now());
        application.setApplicantName("李四");
        application.setApplicantUnit("IT部门");
        application.setApplicationCategory("系统更新");
        application.setChangePriority("高");
        application.setChangeBackgroundReason("数据采集间问2022-12-01到2023-02-28，数据包含CPU、内存、磁盘、主机和Oracle状态信息");
        application.setChangeContentDescription("主要针对服务器、Oracle数据的性能状况以及T100产品运行，提供相应的监控记录，综合相关数据进行分析");
        application.setChangeRange("生产环境服务器");

        invitation.setApplication(application);

        // 创建其他信息供应商
        Supplier<Map<String, Object>> supplierOtherInfo = () -> new HashMap<>();

        // 调用邮件准备方法
        String[] result = activeApproverMail.prepareMail(invitation, supplierOtherInfo);

        // 验证结果
        if (result != null && result.length > 0) {
            System.out.println("邮件准备成功:");
            System.out.println(result[0]);
        } else {
            System.out.println("邮件准备失败");
        }
    }

    @Test
    public void testNormalApprovalMail() {
        // 测试普通审批邮件（非资产变更）
        Invitation invitation = new Invitation();
        invitation.setId(2L);
        invitation.setInvitedEmail("<EMAIL>");
        invitation.setInvitedName("王五");
        invitation.setInvitedServiceCode("TEST001");
        invitation.setInvitedTenantName("测试企业");
        invitation.setInvitedUserId("wangwu");
        invitation.setLinkUrl("https://escloud.digiwin.com/normal/456");
        // 不设置application，测试普通邮件流程

        Supplier<Map<String, Object>> supplierOtherInfo = () -> new HashMap<>();

        String[] result = activeApproverMail.prepareMail(invitation, supplierOtherInfo);

        if (result != null && result.length > 0) {
            System.out.println("普通邮件准备成功:");
            System.out.println(result[0]);
        } else {
            System.out.println("普通邮件准备失败");
        }
    }
}
