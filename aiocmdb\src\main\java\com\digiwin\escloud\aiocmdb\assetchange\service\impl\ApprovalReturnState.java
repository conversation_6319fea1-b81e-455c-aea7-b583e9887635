package com.digiwin.escloud.aiocmdb.assetchange.service.impl;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.aiocmdb.assetchange.service.BaseChangeRequestState;
import org.springframework.stereotype.Component;

@Component(ApplicationStatus.APPROVAL_RETURNED_NAME)
public class ApprovalReturnState extends BaseChangeRequestState {

    @Override
    public String getStateName() {
        return ApplicationStatus.APPROVAL_RETURNED.name();
    }

    @Override
    public void submitApproval(AssetChangeApplication context) {
        context.setApplicationStatus(ApplicationStatus.NOT_APPROVED.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.NOT_APPROVED.name()));
    }
}
