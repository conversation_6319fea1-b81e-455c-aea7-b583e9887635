<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiouser.dao.IUserDao">
    <resultMap id="UserMap" type="com.digiwin.escloud.aiouser.model.user.User">
    </resultMap>
    <resultMap id="InvitationMap" type="com.digiwin.escloud.aiouser.model.common.Invitation">
    </resultMap>
    <resultMap id="EmployeesMap" type="com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee">
    </resultMap>
    <resultMap id="MarsUser" type="com.digiwin.escloud.aiouser.model.user.MarsUser">
    </resultMap>
    <resultMap id="RoleMap" type="com.digiwin.escloud.aiouser.model.user.PermissionRole">
        <result column="id" property="id"/>
        <result column="eid" property="eid"/>
        <result column="roleSid" property="roleSid"/>
        <result column="roleCode" property="roleCode"/>
        <result column="roleName" property="roleName"/>
        <result column="roleName_CN" property="roleName_CN"/>
        <result column="roleName_TW" property="roleName_TW"/>
        <collection property="prdmList" columnPrefix="prdm_" resultMap="RoleMapMap"/>
    </resultMap>
    <resultMap id="RoleMapMap" type="com.digiwin.escloud.aiouser.model.user.PermissionRoleDimensionMap">
        <result column="id" property="id"/>
        <result column="prId" property="prId"/>
        <result column="pdId" property="pdId"/>
        <result column="useDataPermission" property="useDataPermission"/>
        <result column="remark" property="remark"/>
        <association property="permissionDimension" column="pdId"
                     select="com.digiwin.escloud.aiouser.dao.IUserDao.selectDataAuthDimensionByPdId"/>
        <collection property="prrList" column="id"
                    select="com.digiwin.escloud.aiouser.dao.IUserDao.selectDataAuthRuleByPrdmId"/>
    </resultMap>
    <resultMap id="PermissionDimensionMap" type="com.digiwin.escloud.aiouser.model.user.PermissionDimension">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="tableName" property="tableName"/>
        <result column="alias" property="alias"/>
        <result column="dimensionColumn" property="dimensionColumn"/>
        <collection  property="pdaList" columnPrefix="pda_" resultMap="PermissionDimensionAttributeMap"/>
    </resultMap>
    <resultMap id="PermissionDimensionAttributeMap" type="com.digiwin.escloud.aiouser.model.user.PermissionDimensionAttribute">
        <result column="id" property="id"/>
        <result column="pdId" property="pdId"/>
        <result column="attributeCode" property="attributeCode"/>
        <result column="attributeName" property="attributeName"/>
        <result column="dynamicValueLogin" property="dynamicValueLogin"/>
        <collection  property="pdacList" column="id"
                     select="com.digiwin.escloud.aiouser.dao.IUserDao.selectAttributeColumnByAttributeCode"/>
    </resultMap>

    <resultMap id="PermissionDimensionAttributeColumnMap" type="com.digiwin.escloud.aiouser.model.user.PermissionDimensionAttributeColumn">
        <result column="id" property="id"/>
        <result column="pdaId" property="pdaId"/>
        <result column="attributeColumnCode" property="attributeColumnCode"/>
        <result column="attributeColumnName" property="attributeColumnName"/>
        <result column="attributeColumnType" property="attributeColumnType"/>
        <result column="attributeColumnAlias" property="attributeColumnAlias"/>
    </resultMap>

    <resultMap id="PermissionRoleRuleMap" type="com.digiwin.escloud.aiouser.model.user.PermissionRoleRule">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="prdmId" property="prdmId" jdbcType="BIGINT"/>
        <result column="pdId" property="pdId" jdbcType="BIGINT"/>
        <result column="pdaId" property="pdaId" jdbcType="BIGINT"/>
        <result column="pdacId" property="pdacId" jdbcType="BIGINT"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="sourceCode" property="sourceCode" jdbcType="VARCHAR"/>
        <result column="dynamicValueCode" property="dynamicValueCode" jdbcType="VARCHAR"/>
        <result column="value" property="value" jdbcType="VARCHAR"/>
        <result column="leftValue" property="leftValue" jdbcType="VARCHAR"/>
        <result column="rightValue" property="rightValue" jdbcType="VARCHAR"/>
        <result column="logicalOperator" property="logicalOperator" jdbcType="VARCHAR"/>
        <result column="leftParenthesis" property="leftParenthesis" jdbcType="VARCHAR"/>
        <result column="rightParenthesis" property="rightParenthesis" jdbcType="VARCHAR"/>
        <result column="sequence" property="sequence" jdbcType="INTEGER"/>
        <collection  property="dynamicValList" columnPrefix="padv_" resultMap="PermissionAttributeDynamicValMap"/>
    </resultMap>

    <resultMap id="PermissionAttributeDynamicValMap" type="com.digiwin.escloud.aiouser.model.user.PermissionAttributeDynamicVal">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sourceCode" property="sourceCode" jdbcType="VARCHAR"/>
        <result column="dynamicValueCode" property="dynamicValueCode" jdbcType="VARCHAR"/>
        <result column="dynamicValueName" property="dynamicValueName" jdbcType="VARCHAR"/>
        <result column="dynamicValueType" property="dynamicValueType" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="DepartmentMap" type="com.digiwin.escloud.aiouser.model.org.Department">
        <result column="dpt_id" property="dptId"/>
        <result column="dpt_name" property="dptName"/>
        <result column="dpt_role" property="dptRole"/>
        <result column="upper_dpt_id" property="upperDptId"/>
        <result column="effective_date" property="effectiveDate"/>
        <result column="expired_date" property="expiredDate"/>
        <result column="dpt_area" property="dptArea"/>
    </resultMap>

    <select id="getUserByEmail" resultMap="UserMap">
        select sid,id,name,email,telephone,phone,defaultEid,defaultEidSid,defaultSid,defaultSidEid,status
        from user
        where email=#{email} or id=#{email}
        limit 1
    </select>

    <select id="getUserByEmails" parameterType="java.util.List" resultMap="UserMap">
        select sid,id,name,email,telephone,phone,openId from user
        where email in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="userNoticeContactByUserIdList" resultType="Long">
        SELECT unc.id
        FROM user_notify_contact unc
                 JOIN (
            SELECT userId, MAX(id) AS max_id
            FROM user_notify_contact
            WHERE userId IN
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            GROUP BY userId
        ) AS tmp ON unc.userId = tmp.userId AND unc.id = tmp.max_id;
    </select>

    <select id="getUserSuppliers" resultType="java.util.Map">
        SELECT a.*,d.contractStartDate,d.contractExprityDate,
        case when DATE_FORMAT( d.contractExprityDate,'%Y-%m-%d')  &lt; DATE_FORMAT( NOW(),'%Y-%m-%d')  then 1 ELSE 0 END expired
        FROM supplier a
        LEFT JOIN user_tenant_map b ON a.sid = b.sid AND a.eid = b.eid
        LEFT JOIN user c ON c.sid = b.userSid
        LEFT JOIN supplier_contract d ON d.sid = a.sid AND d.productCode = 'CSS'
        WHERE c.id =#{id}
    </select>

    <insert id="insertInvitation" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiouser.model.common.Invitation">
        insert into aio_invitation(id, inviterName, invitedEid,invitedTenantId,invitedTenantName,invitedTaxCode,
        invitedEmployeeId, invitationDate,invitedSid,linkUrl,inviterUserSid,inviteType,invitedServiceCode,invitedName,
        invitedEmail,invitedPhone,invitedUserId,newUser,activated,lastNoticeTime)
        values(#{id}, #{inviterName}, #{invitedEid}, #{invitedTenantId}, #{invitedTenantName},#{invitedTaxCode},
        #{invitedEmployeeId}, #{invitationDate}, #{invitedSid}, #{linkUrl},#{inviterUserSid},
        #{inviteType},#{invitedServiceCode},#{invitedName},#{invitedEmail},#{invitedPhone},#{invitedUserId},
        #{newUser},#{activated},#{lastNoticeTime})
    </insert>

    <select id="getInvitation" resultMap="InvitationMap">
        select id,inviterName,invitedEid,invitedTenantId,invitedTenantName,invitedTaxCode,invitedEmployeeId,invitationDate,
               invitedSid,linkUrl,inviterUserSid,inviteType,invitedServiceCode,invitedName,invitedEmail,invitedPhone,
               invitedUserId,newUser,activated,lastNoticeTime,createTime,updateTime
        from aio_invitation
        where id=#{id}
    </select>

    <select id="getInvitationByInviteType" resultMap="InvitationMap">
        select id,inviterName,invitedEid,invitedTenantId,invitedTenantName,invitedTaxCode,invitedEmployeeId,invitationDate,
               invitedSid,linkUrl,inviterUserSid,inviteType,invitedServiceCode,invitedName,invitedEmail,invitedPhone,
               invitedUserId,newUser,activated,lastNoticeTime,createTime,updateTime
        from aio_invitation
        where invitedEid=#{eid} and inviteType=#{inviteType}
        order by id asc
    </select>

    <select id="getLastInvitationByUserId" resultMap="InvitationMap">
        select id,inviterName,invitedEid,invitedTenantId,invitedTenantName,invitedTaxCode,invitedEmployeeId,invitationDate,
               invitedSid,linkUrl,inviterUserSid,inviteType,invitedServiceCode,invitedName,invitedEmail,invitedPhone,
               invitedUserId,newUser,activated,lastNoticeTime,createTime,updateTime
        from aio_invitation
        where invitedUserId=#{invitedUserId} and inviteType=#{inviteType}
        order by id desc
        limit 1
    </select>

    <select id="getLastInvitationByUserInviteType" resultMap="InvitationMap">
        select id,inviterName,invitedEid,invitedTenantId,invitedTenantName,invitedTaxCode,invitedEmployeeId,invitationDate,
               invitedSid,linkUrl,inviterUserSid,inviteType,invitedServiceCode,invitedName,invitedEmail,invitedPhone,
               invitedUserId,newUser,activated,lastNoticeTime,createTime,updateTime
        from aio_invitation
        where invitedUserId=#{invitedUserId} and inviteType like CONCAT(#{inviteType} ,'%')
        order by id desc
        limit 1
    </select>

    <select id="getActivateUserCnt" resultType="java.util.Map">
        select invitedEid as eid,count(distinct invitedUserId) as cnt
        from aio_invitation
        where inviteType=#{inviteType}
        <foreach collection="eidList" item="item" open=" AND invitedEid IN(" separator=", " close=")">
            #{item}
        </foreach>
        GROUP BY invitedEid
    </select>

    <update id="setUserActivated">
        update aio_invitation
        set activated = #{activated}
        where id = #{id}
    </update>

    <update id="setUserIsNew">
        update aio_invitation
        set newUser = #{newUser}
        where id = #{id}
    </update>

    <update id="batchUpdateInvitationNoticeTime">
        update aio_invitation
        set lastNoticeTime = #{lastNoticeTime}
        where id in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getEmployees" resultMap="EmployeesMap">
        select a.id,a.name,a.workNo,a.orgSid,a.orgUri,a.email,a.telephone,a.status,a.userSid,a.language,a.timeZone,a.eid,a.sid,a.deptIds,
        SUBSTRING_INDEX(a.orgUri,':',-1) as orgLabel, CASE WHEN IFNULL(a.userSid,'')='' then 0 else 1 end activated
        <if test="status ==1 ">
            ,b.authorizedProductCodes,b.defaultProductCode,c.id as userId
        </if>
        from supplier_employee a
        <if test="status ==1 ">
            left join user_tenant_map b on a.userSid=b.userSid and a.eid=b.eid
            left join user c on a.userSid=c.sid
        </if>
        where 1=1 and a.eid=#{eid} and a.status=#{status}
        <if test="orgUri != '' and orgUri!=null">
            AND orgUri like #{orgUri}"%"
        </if>
        <if test="orgUriList != null and orgUriList.size()>0">
            <foreach collection="orgUriList" item="item" open=" AND (orgUri LIKE " separator=" OR orgUri LIKE " close=")">
                concat(#{item},'%')
            </foreach>
        </if>
          <if test="sid != null and sid!=0">
             AND a.sid=#{sid}
          </if>
        <if test="content != '' and content!=null">
            AND (a.name like "%"#{content}"%" or a.workNo like "%"#{content}"%" or a.email like concat('%',#{content},'%'))
        </if>
        <if test="id != null and id!=0">
            AND a.id=#{id}
        </if>
<!--        <if test="userSids != null and userSids.size()!=0">-->
<!--            and a.userSid in-->
<!--            <foreach item="item" collection="userSids" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
        order by a.workNo
        limit #{start},#{count}
    </select>

    <select id="getSupplierEmployeeInfo" resultType="com.digiwin.escloud.aiouser.model.user.SupplierEmployeeInfo">
        select a.id,a.name,a.workNo,a.orgSid,a.email,a.status,a.userSid,a.language,a.timeZone,a.eid,a.sid
        from supplier_employee a
        where a.sid=#{sid} and a.status = 1 and a.userSid= #{userSid}
        limit 1
    </select>

    <select id="getSupplierEmployeeInfoByName" resultType="com.digiwin.escloud.aiouser.model.user.SupplierEmployeeInfo">
        select a.id,a.name,a.workNo,a.orgSid,a.email,a.status,a.userSid,a.language,a.timeZone,a.eid,a.sid
        from supplier_employee a
        where a.name  = #{name}
        order by updateTime desc
        limit 1
    </select>

    <select id="getUserPersonalInfo" resultType="com.digiwin.escloud.aiouser.model.user.UserPersonInfo">
        select a.sid id,a.name,a.email,a.email,a.telephone,a.status,b.language,b.timeZone
        from user a
        left join user_personalinfo b on a.sid=b.sid
        where a.status =1 and a.sid=#{userSid}
    </select>
    <select id="getEmployeesCount" resultType="java.lang.Long">
        select count(*)
        from supplier_employee a
        where a.sid=#{sid} and a.eid=#{eid} and a.status=#{status}
        <if test="orgUri != '' and orgUri!=null">
            AND orgUri like #{orgUri}"%"
        </if>
        <if test="content != '' and content!=null">
            AND (a.name like "%"#{content}"%" or a.workNo like "%"#{content}"%")
        </if>
    </select>

    <select id="getEmployeeByWorkNo" resultType="java.lang.Integer">
        select count(*)
        from supplier_employee
        where workNo=#{workNo} and eid=#{eid}
        <if test="mode == 'update' ">
            and id!=#{id}
        </if>
    </select>

    <select id="getEmployeeByEmail" resultType="java.lang.Integer">
        select 1
        from supplier_employee
        where email=#{email} and eid=#{eid}
        <if test="mode == 'update' ">
            and id!=#{id}
        </if>
        limit 1
    </select>

    <select id="getEmployeeByCol" resultType="java.lang.Integer">
        select 1
        from supplier_employee
        where ${col}=#{value} and eid=#{eid}
        <if test="mode == 'update' ">
            and id!=#{id}
        </if>
        limit 1
    </select>

    <insert id="insertEmployee" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee">
        insert into supplier_employee(id, name, workNo, orgSid, orgUri, email, telephone, status, userSid,eid, sid
        <if test="language != null and language != ''">
            ,language
        </if>
        )
        values(#{id}, #{name}, #{workNo}, #{orgSid}, #{orgUri}, #{email}, #{telephone}, #{status}, #{userSid},#{eid}, #{sid}
        <if test="language != null and language != ''">
            ,#{language}
        </if>
        )
    </insert>

    <update id="updateEmployee">
        update supplier_employee
        set name = #{name},workNo = #{workNo},orgSid = #{orgSid},orgUri = #{orgUri},email = #{email},telephone = #{telephone},status = #{status}
        <if test="language != null and language != ''">
            ,language=#{language}
        </if>
        where id = #{id}
    </update>

    <select id="getSupplierEmployee" resultMap="EmployeesMap">
        select a.id,a.name,a.workNo,a.orgSid,a.email,a.telephone, a.status,a.userSid,a.language,a.timeZone,a.eid,a.sid
        from supplier_employee a
        where a.id=#{id}
    </select>

    <select id="selectUser" resultType="com.digiwin.escloud.aiouser.model.user.User">
        select sid, id, name, email, telephone,phone,defaultEid,defaultEidSid,defaultSid,defaultSidEid,status,wechat,
        CASE WHEN ifnull(wechat,'')='' THEN 0 ELSE 1  END AS bandingWeChat
        from user
        where sid = #{sid}
    </select>

    <insert id="insertUser" keyProperty="sid" keyColumn="sid" parameterType="com.digiwin.escloud.aiouser.model.user.User">
        insert into user(sid, id, name, email, telephone,defaultEid,defaultEidSid,defaultSid,defaultSidEid,status
        <if test="wechat != null and wechat != ''">
            ,wechat
        </if>
        )
        values(#{sid}, #{id}, #{name}, #{email}, #{telephone}, #{defaultEid}, #{defaultEidSid},
        #{defaultSid},#{defaultSidEid},#{status}
        <if test="wechat != null and wechat != ''">
            ,#{wechat}
        </if>
        )
    </insert>

    <update id="updateUser">
        update user
        set name = #{name},email = #{email},telephone = #{telephone},phone = #{phone}
        where id = #{id}
    </update>

    <update id="updateUserWeChat">
        update user
        set wechat = #{wechat}
        where id = #{id}
    </update>

    <insert id="insertUserTenantMap" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiouser.model.user.UserTenantMap">
        insert into user_tenant_map(id, userSid, eid, sid, enterprise
        <if test="authorizedProductCodes != null and authorizedProductCodes != ''">
            ,authorizedProductCodes
        </if>
        <if test="defaultProductCode != null and defaultProductCode != ''">
            ,defaultProductCode
        </if>
        )
        values(#{id}, #{userSid}, #{eid}, #{sid}, #{enterprise}
        <if test="authorizedProductCodes != null and authorizedProductCodes != ''">
            ,#{authorizedProductCodes}
        </if>
        <if test="defaultProductCode != null and defaultProductCode != ''">
            ,#{defaultProductCode}
        </if>
        )
    </insert>

    <update id="updateUserTenantMapProduct">
        update user_tenant_map
        set authorizedProductCodes=#{authorizedProductCodes},defaultProductCode=#{defaultProductCode}
        where userSid=#{userSid} and  eid=#{eid}
    </update>

    <insert id="insertUserPersonalInfo" keyProperty="sid" keyColumn="sid" parameterType="com.digiwin.escloud.aiouser.model.user.UserPersonalInfo">
        insert into user_personalinfo(sid, language, serviceRegion, timeZone)
        values(#{sid}, #{language}, #{serviceRegion}, #{timeZone})
    </insert>

    <select id="getOrgSid" resultType="java.lang.Long">
        select orgSid
        from supplier_employee
        where sid=#{sid} and eid=#{eid} and userSid=#{userSid}
        limit 1;
    </select>

    <select id="queryEmployeeList" resultType="com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee">
        select a.id,a.name,a.workNo,a.orgSid,a.orgUri,SUBSTRING_INDEX(a.orgUri,':',-1) as orgLabel,
a.email,a.status,a.userSid,a.language,a.timeZone,a.eid,a.sid,u.id userId
        from supplier_employee a
        left join user_tenant_map b on a.userSid=b.userSid and a.eid=b.eid and a.sid= b.sid
        LEFT JOIN user u ON u.sid = a.userSid
        where a.status = 1
        <if test="sid != 0">
            and a.sid=#{sid}
        </if>
        <if test="eid != 0">
            and a.eid=#{eid}
        </if>
        <if test="productCode != null and productCode != ''">
            and FIND_IN_SET(#{productCode},b.authorizedProductCodes)
        </if>
        <if test="orgSid != 0">
            and a.orgSid=#{orgSid}
        </if>
        <if test="userSid != 0">
            and a.userSid=#{userSid}
        </if>
        <if test="content != ''">
            and (a.name like "%"#{content}"%" or a.workNo like "%"#{content}"%")
        </if>
    </select>

    <insert id="insertVisitorUser">
        INSERT INTO user_visitor (sid, realSid, supplierSid, nickname)
        VALUES (#{visitorUser.sid}, #{visitorUser.realSid}, #{visitorUser.supplierSid}, #{visitorUser.nickname})
    </insert>

    <update id="updateVisitorRealSidBySid">
        UPDATE user_visitor
        SET realSid = #{realSid}
        WHERE sid = #{sid}
    </update>

    <select id="selectVisitorUserBySid" resultType="com.digiwin.escloud.aiouser.model.user.VisitorUser">
        SELECT * FROM user_visitor WHERE sid = #{sid}
    </select>

    <update id="updateEmployeeActive">
        update supplier_employee
        set userSid = #{userSid},status = 1
        where id = #{id}
    </update>

    <select id="getOrgEmployeesCount" resultType="java.lang.Integer">
        SELECT 1 FROM supplier_employee
        WHERE eid = #{eid} AND orgUri like #{orgUri}"%"
        limit 1
    </select>

    <select id="getUserEmail" resultType="java.lang.String">
        select email
        from user
        where id=#{userId}
    </select>

    <update id="updateUserEmail">
        update user
        set email = #{email}
        where id = #{userId}
    </update>

    <select id="getUserByTelephone" resultMap="UserMap">
        select sid,id,name,email,telephone,phone,defaultEid,defaultEidSid,defaultSid,defaultSidEid,status
        from user
        where telephone=#{telephone}
        limit 1
    </select>

    <select id="getUserTenantMap" resultType="com.digiwin.escloud.aiouser.model.user.UserTenantMap">
        select id,userSid,eid,sid,authorizedProductCodes,defaultProductCode
        from user_tenant_map
        where userSid=#{userSid} and eid=#{eid}
    </select>

    <update id="updateUserDefaultSet">
        update user
        set
        <choose>
            <when test="defaultEid != null">
                defaultEid = #{defaultEid},defaultEidSid = #{defaultEidSid}
            </when>
            <otherwise>
                defaultSid = #{defaultSid},defaultSidEid = #{defaultSidEid}
            </otherwise>
        </choose>
        <if test="name != null and name != ''">
            ,name=#{name}
        </if>
        where sid = #{sid}
    </update>
    <update id="updateUserDefaultEidAndSid">
        update user
        set  defaultEid = #{defaultEid},defaultEidSid = #{defaultEidSid},
             defaultSid = #{defaultSid},defaultSidEid = #{defaultSidEid}
        where sid = #{userSid}
    </update>

    <select id="getTpUserEmails" resultType="java.lang.String">
        select tpUserEmail
        from a1temp
    </select>
	
    <select id="getEmployeeActivated" resultType="java.lang.Integer">
        select 1
        from user
        where email=#{email}
        limit 1
    </select>

    <select id="getUserPairDefaultTenant" resultType="java.util.Map">
        select
            a.sid,
            a.email,
            a.telephone,
            c.id as tenantId
        from
            user a
                inner join user_tenant_map b on a.sid = b.userSid and a.defaultEid = b.eid
                inner join tenant c on b.eid = c.sid
        where a.sid = #{userSid}
    </select>
	
    <select id="getTenantAndAttention" resultType="com.digiwin.escloud.aiouser.model.user.UserTenantAttention">
        select * from user_tenant_attention
        <where>
            <if test="userSid != null">
                and userSid=#{userSid}
            </if>
            <if test="sid != null">
                and sid=#{sid}
            </if>
            <if test="attention != null">
                and attention=#{attention}
            </if>
        </where>
    </select>
    <select id="getTenantAndAttention4Org"
            resultType="com.digiwin.escloud.aiouser.model.user.UserTenantAttention">
        select u.* from  supplier_employee s,user_tenant_attention u
        where s.userSid = u.userSid and s.sid = u.sid
        <if test="orgSid != null ">
            and s.orgSid=#{orgSid}
        </if>
        <if test="attention != null">
            and attention=#{attention}
        </if>
    </select>

    <insert id="insertUserTenantAttention" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiouser.model.user.UserTenantAttention">
        insert into user_tenant_attention(id, userSid, eid, sid, attention, receiveWarningMail)
        values(#{id}, #{userSid}, #{eid}, #{sid}, #{attention}, #{receiveWarningMail})
        ON DUPLICATE KEY UPDATE attention=#{attention}, receiveWarningMail= #{receiveWarningMail}
    </insert>

    <select id="checkUserCanAttention" resultType="java.lang.Integer">
        select 1
        from tenant_contract
        where sid=#{sid} and eid=#{eid} and productCode IN
        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        limit 1
    </select>

    <select id="getUserExist" resultType="java.lang.Integer">
        select 1
        from user
        where sid=#{userSid}
        limit 1
    </select>

    <select id="getAttentionUserByEid" resultType="com.digiwin.escloud.aiouser.model.user.User">
        select distinct u.* from user u,user_tenant_attention uta
        where u.sid = uta.userSid and u.status =1 and uta.eid=#{eid} and uta.attention =1
    </select>

    <select id="getAttentionAndReceiveWarningMailUserByEid" resultType="com.digiwin.escloud.aiouser.model.user.User">
        select distinct u.* from user u,user_tenant_attention uta
        where u.sid = uta.userSid and u.status =1 and uta.eid=#{eid} and uta.attention =1 and uta.receiveWarningMail = 1
    </select>

    <select id="getUserTenantAttentions" resultType="com.digiwin.escloud.aiouser.model.user.UserTenantAttention">
        select uta.id, uta.userSid, uta.eid,t.id as tenantId,t.name as tenantName, uta.sid, uta.attention, uta.receiveWarningMail
        from user_tenant_attention uta
        inner join tenant t on uta.eid=t.sid
        where uta.sid=#{sid} and uta.userSid = #{userSid} and uta.attention =#{attention}
        order by uta.updateTime desc
        LIMIT #{start} , #{size}
    </select>

    <select id="getUserTenantAttentionsCount" resultType="java.lang.Integer">
        select count(*)
        from user_tenant_attention uta
        inner join tenant t on uta.eid=t.sid
        where uta.sid=#{sid} and uta.userSid = #{userSid} and uta.attention =#{attention}
    </select>

    <select id="getEmployeeBasicInfo" resultType="com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee">
        select se.id,se.name,se.workNo,se.orgSid,se.email,se.status,se.userSid,se.language,se.timeZone,se.eid,se.sid
        from supplier_employee se
        where se.status = 1 and se.name not like CONCAT('JmeterTestStaff', '%')
        <if test="sid != 0">
            and se.sid=#{sid}
        </if>
        <if test="content != ''">
            and (se.name like "%"#{content}"%" or se.workNo like "%"#{content}"%")
        </if>
        <if test="removeAttention ==true ">
            and not exists(select 1 from user_tenant_attention uta
            where uta.userSid=se.userSid and uta.eid=#{eid} and uta.sid=#{sid} and uta.attention=1)
        </if>
        order by se.name
        LIMIT #{start} , #{size}
    </select>

    <select id="getEmployeeBasicInfoCount" resultType="java.lang.Integer">
        select count(*)
        from supplier_employee se
        where se.status = 1 and se.name not like CONCAT('JmeterTestStaff', '%')
        <if test="sid != 0">
            and se.sid=#{sid}
        </if>
        <if test="content != ''">
            and (se.name like "%"#{content}"%" or se.workNo like "%"#{content}"%")
        </if>
        <if test="removeAttention ==true ">
            and not exists(select 1 from user_tenant_attention uta
            where uta.userSid=se.userSid and uta.eid=#{eid} and uta.sid=#{sid} and uta.attention=1)
        </if>
    </select>

    <insert id="batchSaveUserTenantAttention" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiouser.model.user.UserTenantAttention">
        insert into user_tenant_attention(id, userSid, eid, sid, attention, receiveWarningMail)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.userSid},#{item.eid},#{item.sid},#{item.attention},#{item.receiveWarningMail})
        </foreach>
        ON DUPLICATE KEY UPDATE attention = values(attention), receiveWarningMail= values(receiveWarningMail)
    </insert>

    <select id="getTenantAttentionUsers" resultType="com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee">
        select se.id,se.name,se.workNo,se.orgSid,se.email,se.status,se.userSid,se.language,se.timeZone,se.eid,se.sid
        from user_tenant_attention uta
        inner join supplier_employee se on uta.userSid=se.userSid and uta.sid=se.sid
        where se.status = 1 and uta.sid=#{sid} and uta.eid=#{eid} and uta.attention=1
        order by se.workNo
        LIMIT #{start} , #{size}
    </select>

    <select id="getTenantAttentionUsersCount" resultType="java.lang.Integer">
        select count(*)
        from user_tenant_attention uta
        inner join supplier_employee se on uta.userSid=se.userSid and uta.sid=se.sid
        where se.status = 1 and uta.sid=#{sid} and uta.eid=#{eid} and uta.attention=1
    </select>

    <select id="getEmployeesByDept" resultMap="EmployeesMap">
        select a.id,a.name,a.workNo,a.orgSid,a.orgUri,a.email,a.telephone,a.status,a.userSid,a.language,a.timeZone,a.eid,a.sid,
        SUBSTRING_INDEX(a.orgUri,':',-1) as orgLabel, CASE WHEN IFNULL(a.userSid,'')='' then 0 else 1 end activated
        from supplier_employee a
        where a.sid=#{sid}
        <if test="deptCodes != null and deptCodes.length >0">
            AND SUBSTRING_INDEX(a.orgUri,':',-1) in
            <foreach collection="deptCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="content != '' and content!=null">
            AND (a.name like "%"#{content}"%" or a.workNo like "%"#{content}"%")
        </if>
        order by a.workNo
    </select>

    <select id="getPwd" resultType="java.lang.String">
        select password
        from user
        where id=#{userId}
    </select>

    <select id="getUserExistedById" resultType="java.lang.Integer">
        select 1
        from user
        where id=#{userId}
    </select>

    <select id="getUserById" resultMap="UserMap">
        select sid,id,name,email,telephone,phone,defaultEid,defaultEidSid,defaultSid,defaultSidEid,status
        from user
        where id=#{userId}
        limit 1
    </select>

    <update id="updatePassword">
        update user
        set password = #{password}
        where id = #{account} or email = #{account} or telephone = #{account}
    </update>

    <select id="getEmailExist" resultType="java.lang.Integer">
        select 1
        from user
        where email=#{email}
    </select>

    <select id="getTelephoneExist" resultType="java.lang.Integer">
        select 1
        from user
        where email=#{telephone}
    </select>

    <select id="getUserIdExist" resultType="java.lang.Integer">
        select 1
        from user
        where id=#{userId}
    </select>

    <select id="selectUserNameBySidList" resultType="java.util.Map">
        SELECT sid, name
        FROM `user`
        WHERE 1 != 1
        <if test="userSidList != null">
            <foreach collection="userSidList" item="item" open=" OR sid IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectSeUserSidByWorkNo" resultType="java.lang.Long">
        SELECT userSid
        FROM supplier_employee
        WHERE sid = #{sid} AND workNo = #{workNo}
    </select>
    
    <select id="checkTenantIsAttention" resultType="java.lang.Integer">
        select 1
        from user_tenant_attention
        where sid=#{sid} and eid=#{eid} and userSid=#{userSid} and attention=#{attention}
        limit 1
    </select>

    <update id="updateAttentionTenantReceiveWarningMail">
        update user_tenant_attention
        set receiveWarningMail = #{receiveWarningMail}
        where sid=#{sid} and eid=#{eid} and userSid=#{userSid} and attention=#{attention}
    </update>
    <update id="updateUserDefaultSupplier" parameterType="com.digiwin.escloud.aiouser.model.user.User">
        update user
        set defaultSid = #{defaultSid},defaultSidEid = #{defaultSidEid}
        where sid = #{sid}
    </update>
    <insert id="updateSupplierContractDevice" parameterType="com.digiwin.escloud.aiouser.model.supplier.SupplierContractDevice">
        insert into supplier_contract_device(sid, supplier_contract_id, deviceId, operaterType, userSid)
        values(#{sid}, #{supplier_contract_id}, #{deviceId}, #{operaterType}, #{userSid})
        ON DUPLICATE KEY UPDATE operaterType=#{operaterType},updateTime = now()
    </insert>
    <select id="getISVAuthorizeCount" resultType="java.util.Map">
        SELECT a.authorizedNum,c.onLineServiceNum FROM supplier_contract a
        LEFT JOIN (SELECT COUNT(*) onLineServiceNum
                FROM supplier_contract_device b
                WHERE b.sid = #{sid}
                AND b.supplier_contract_id = #{supplier_contract_id}
                AND b.operaterType = 'loginin'
                AND b.updateTime >= DATE_ADD(NOW(), INTERVAL - 5 MINUTE)) c ON 1=1
        WHERE a.sid = #{sid}
    </select>

    <select id="getMarsUser" resultMap="MarsUser">
        select mu.ID, mu.CustomerServiceCode
        from `${dbName}`.mars_user mu
        left join `aio-db`.supplier_tenant_map stm
        on stm.serviceCode = mu.CustomerServiceCode
        and stm.serviceCode = mu.Username
        where stm.serviceCode = #{eid}
    </select>

    <insert id="batchInsertUserTenantAttention">
        insert into user_tenant_attention
        (id, userSid, eid, sid, attention, createTime, updateTime,receiveWarningMail)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.userSid}, #{item.eid}, #{item.sid}, #{item.attention},
            now(), now(), #{item.receiveWarningMail})
        </foreach>
    </insert>


    <select id="selectDataAuthRole" resultMap="RoleMap">
        select pr.*,prdm.id prdm_id,prdm.prId prdm_prId,prdm.pdId prdm_pdId,prdm.useDataPermission prdm_useDataPermission
        from permission_role pr
        LEFT JOIN permission_role_dimension_map prdm on pr.id = prdm.prId
        where eid = #{eid} AND sid = #{sid}
          <if test="roleCodeList != null and roleCodeList.size()>0">
              <foreach collection="roleCodeList" item="item" open=" AND roleCode IN (" separator=", " close=")">
                  #{item}
              </foreach>
          </if>
    </select>

    <select id="selectDataAuthRoleDetail" resultMap="RoleMap">
        select pr.*
        ,prdm.id   prdm_id  ,prdm.prId prdm_prId,prdm.pdId prdm_pdId,prdm.useDataPermission prdm_useDataPermission
        from permission_role pr
        LEFT JOIN permission_role_dimension_map prdm on pr.id = prdm.prId
        where eid = #{eid} AND sid=#{sid}
        <if test="roleCodeList != null and roleCodeList.size()>0">
            <foreach collection="roleCodeList" item="item" open=" AND roleCode IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectDataAuthDimensionByPdId" resultMap="PermissionDimensionMap">
        SELECT pd.code                code
             , pd.name                name
             , pd.id                  id
             , pd.tableName           tableName
             , pd.alias               alias
             , pd.dimensionColumn               dimensionColumn
             , pda.pdId      pda_pdId
             , pda.id      pda_id
             , pda.attributeCode      pda_attributeCode
             , pda.attributeName      pda_attributeName
             , pda.dynamicValueLogin        pda_dynamicValueLogin
        FROM permission_dimension pd
                 LEFT JOIN permission_dimension_attribute pda on pd.id = pda.pdId
        WHERE pd.id = #{pdId}
    </select>

    <select id="selectDataAuthRuleByPrdmId" resultMap="PermissionRoleRuleMap">
        SELECT prr.prdmId
             , prr.pdId
             , prr.pdaId
             , prr.id
             , prr.pdacId
             , prr.operator
             , prr.sourceCode
             , prr.dynamicValueCode
             , prr.value
             , prr.leftValue
             , prr.rightValue
             , prr.logicalOperator
             , prr.leftParenthesis
             , prr.rightParenthesis
             , prr.sequence
             , padv.id      padv_id
             , padv.sourceCode      padv_sourceCode
             , padv.dynamicValueCode      padv_dynamicValueCode
             , padv.dynamicValueName           padv_dynamicValueName
             , padv.dynamicValueType padv_dynamicValueType
        FROM permission_role_rule prr
                 LEFT JOIN permission_attribute_dynamic_val padv on padv.sourceCode = prr.sourceCode
        WHERE prr.prdmId = #{id}
        ORDER BY prr.sequence
    </select>
    <select id="selectAttributeColumnByAttributeCode" resultMap="PermissionDimensionAttributeColumnMap">
        select id,pdaId,attributeColumnCode,attributeColumnName,attributeColumnType,attributeColumnAlias FROM
            permission_dimension_attribute_column
        where pdaId = #{id}
    </select>
    <insert id="batchInsertOrUpdateRole">
        INSERT INTO permission_role (eid, sid, roleSid, roleCode, roleName, createTime, updateTime)
        <foreach collection="roleInfoResList" item="item" open="VALUES (" separator="), (" close=")">
            #{eid}, #{sid}, #{item.sid}, #{item.id}, #{item.name}, NOW(), NOW()
        </foreach>
        ON DUPLICATE KEY UPDATE roleCode = VALUES(roleCode),
                                roleName = VALUES(roleName),
                                updateTime = NOW()
    </insert>

    <select id="getRoleByEid" parameterType="Long" resultType="com.digiwin.escloud.aiouser.model.user.PermissionRole">
        SELECT id, eid, roleSid, roleCode, roleName, roleName_CN, roleName_TW,priority
        FROM permission_role
        WHERE eid = #{eid} and sid = #{sid}
        order by id asc
    </select>

    <select id="selectDepartment" resultMap="DepartmentMap">
        SELECT dpt_id, dpt_name, dpt_role, upper_dpt_id, effective_date, expired_date, expired_date
        FROM departments
    </select>

    <update id="updateEmployeeDeptIds">
        UPDATE supplier_employee
        SET deptIds = #{deptIds},
            updateTime = now()
        WHERE id = #{id}
    </update>

    <select id="selectSelfDepartmentByUserId" resultMap="DepartmentMap">
        SELECT dpt_name
        FROM departments
        WHERE dpt_id in (SELECT departmentcode FROM mars_customerservicesatff WHERE email = #{userId})
    </select>
    <select id="selectManagerDepartmentByDeptIdList" resultMap="DepartmentMap">
        SELECT dpt_name
        FROM departments
        WHERE 1=1
            <if test="deptIdList != null and deptIdList.size()>0">
                <foreach collection="deptIdList" item="item" open="AND dpt_id IN(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>
    <select id="selectDeptInfoByUserId" resultType="com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee">
        SELECT deptIds,orgUri FROM supplier_employee WHERE email = #{userId}
    </select>

    <update id="updateEmployeeNotActive">
        update supplier_employee
        set status = 0
        where id = #{id}
    </update>

    <update id="updateRolePriority" parameterType="com.digiwin.escloud.aiouser.model.user.PermissionRole">
        update permission_role
        set priority = #{priority}
        where roleCode = #{roleCode}
    </update>

    <select id="selectModuleStaffSyncMapping"  resultType="com.digiwin.escloud.aiouser.model.user.ModuleStaffSyncMapping">
        SELECT
        mssm.id,  mssm.staffId,  mssm.moduleCode,  mssm.staffName, mssm.moduleId
        FROM
        module_staff_sync_mapping mssm
        LEFT JOIN  supplier_employee se ON mssm.staffId = se.userSid
        WHERE
        se.userSid IS NOT NULL
        <if test="staffId != null and staffId > 0">
            AND  mssm.staffId = #{staffId}
        </if>
        <if test="moduleId != null">
            AND  mssm.moduleId = #{moduleId}
        </if>
        ORDER BY mssm.updateTime DESC
        LIMIT 1
    </select>
    <delete id="deleteModuleStaffSyncMapping">
        DELETE FROM module_staff_sync_mapping WHERE staffId = #{staffId} AND moduleId = #{moduleId}
    </delete>

    <!-- insertOrUpdate (Upsert) 操作 -->
    <insert id="insertOrUpdateModuleStaffSyncMapping" parameterType="com.digiwin.escloud.aiouser.model.user.ModuleStaffSyncMapping">
        INSERT INTO module_staff_sync_mapping
            (id,staffId, moduleCode, staffName, moduleId)
        VALUES (#{id},#{staffId}, #{moduleCode}, #{staffName}, #{moduleId})
        ON DUPLICATE KEY UPDATE staffName = VALUES(staffName),
                                moduleId  = VALUES(moduleId)
    </insert>

    <select id="selectModuleStaff"  resultType="com.digiwin.escloud.aiouser.model.user.User">
        select u.*,mcs.CustomerServiceCode as serviceCode
        from user u
                 left join `${escloudDBName}`.mars_customerservice mcs on mcs.ServiceStaffContact = u.id
        where ServiceStaffCode IS NOT NULL
          AND ServiceStaffCode != ''
          AND ServiceStaffCode != '9111'
          <if test="request.productLine != null and request.productLine!=''" >
              AND ProductCode = #{request.productLine}
          </if>

         <if test="request.serviceCodeList != null and request.serviceCodeList.size()>0">
            AND mcs.CustomerServiceCode IN
            <foreach collection="request.serviceCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY updateTime DESC
    </select>
</mapper>