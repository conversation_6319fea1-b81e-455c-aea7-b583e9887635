package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.digiwin.escloud.aiocmdb.asset.model.AssetCategory;
import com.digiwin.escloud.aiocmdb.assetchange.service.ChangeRequestState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 资产变更申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Data
@ApiModel(value = "AssetChangeApplication对象", description = "资产变更申请单")
public class AssetChangeApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("eid")
    private Long eid;

    @ApiModelProperty("申请单编号（唯一索引）")
    private String applicationNumber;

    @ApiModelProperty("申请日期")
    private LocalDate applicationDate;

    @ApiModelProperty("申请人用户ID")
    private String applicantUserId;

    @ApiModelProperty("申请人名称")
    private String applicantName;

    @ApiModelProperty("申请人单位")
    private String applicantUnit;

    @ApiModelProperty("申请类别")
    private String applicationCategory;

    private String changeRange;

    @ApiModelProperty("申请单状态")
    private String applicationStatus;

    @ApiModelProperty("最近一次审批意见")
    private String latestApprovalComment;

    @ApiModelProperty("最近一次验收意见")
    private String latestAcceptanceComment;

    @ApiModelProperty("变更背景原因")
    private String changeBackgroundReason;

    @ApiModelProperty("变更内容描述")
    private String changeContentDescription;

    @ApiModelProperty("变更优先级")
    private String changePriority;

    @ApiModelProperty("风险评估")
    private String riskAssessment;

    @ApiModelProperty("还原计划")
    private String rollbackPlan;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;


    private transient ChangeRequestState currentState;

    private List<AssetChangeApplicationAssetList> assetChangeApplicationAssetList = new ArrayList<>();
    private List<AssetChangeApplicationExecutionPlan> assetChangeApplicationExecutionPlan = new ArrayList<>();
    private List<AssetCategory> assetCategory = new ArrayList<>();
    private List<AssetChangeApplicationOperationRecord> assetChangeApplicationOperationRecord = new ArrayList<>();
    private List<AssetChangeApplicationReportRecord> assetChangeApplicationReportRecord = new ArrayList<>();

    // 用于打印状态变更日志
    private void logStateChange() {
        System.out.println("---");
        System.out.println("流程 [" + this.id + "] 状态变更为: " + this.currentState.getStateName());
    }

    // 将所有操作委托给当前状态
    public void submitApproval() {
        currentState.submitApproval(this);
        logStateChange();
    }

    public void approvePass() {
        currentState.approvePass(this);
        logStateChange();
    }

    public void approveFail() {
        currentState.approveFail(this);
        logStateChange();
    }

    public void approveAdjust() {
        currentState.approveAdjust(this);
        logStateChange();
    }

    public void execute() {
        currentState.execute(this);
        logStateChange();
    }

    public void submitAcceptance() {
        currentState.submitAcceptance(this);
        logStateChange();
    }

    public void acceptPass() {
        currentState.acceptPass(this);
        logStateChange();
    }

    public void acceptFail() {
        currentState.acceptFail(this);
        logStateChange();
    }

    public void acceptAdjust() {
        currentState.acceptAdjust(this);
        logStateChange();
    }
}
