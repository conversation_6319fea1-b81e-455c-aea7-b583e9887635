package com.digiwin.escloud.aiouser.dao;

import com.digiwin.escloud.aiouser.model.product.ProductClassification;
import com.digiwin.escloud.aiouser.model.supplier.*;
import com.digiwin.escloud.aiouser.model.tenant.Tenant;
import com.digiwin.escloud.aiouser.model.tenant.TenantContract;
import com.digiwin.escloud.aiouser.model.user.User;
import com.digiwin.escloud.aiouser.model.user.UserPersonalInfo;
import com.digiwin.escloud.aiouser.model.user.UserTenantMap;
import com.digiwin.escloud.aiouser.model.user.UserTenantInfoResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023-04-17
 * @Description
 */
public interface IISVAppDao {
    int saveSupplier(Supplier supplier);
    int getMaxSupplierContractID();
    int selectSupplierContract(SupplierContract supplierContract);
    int insertSupplierContract(SupplierContract supplierContract);
    int updateSupplierContract(SupplierContract supplierContract);
    int saveTenant(Tenant tenant);
    int updateTenant(Tenant tenant);
    int getSupplierTenantMapCount(SupplierTenantMap supplierTenantMap);
    int saveSupplierTenantMap(SupplierTenantMap supplierTenantMap);
    int selectTenantContract(TenantContract tenantContract);
    int insertTenantContract(TenantContract tenantContract);
    int updateTenantContract(TenantContract tenantContract);
    long getSupplierSid(@Param(value = "sid") long sid, @Param(value = "customerServiceCode") String customerServiceCode);
    int getAppCount(@Param(value = "sid") long sid, @Param(value = "productCode") String productCode);
    int saveSupplierProduct(SupplierProduct supplierProduct);
    List<ProductClassification> getDefaultClassification();
    int initSupplierProductClassification(Map<String,Object> map);
    Supplier getSuppler(@Param(value = "tenantId") String tenantId);
    Supplier getSupplerBySid(@Param(value = "sid") long sid);
    SupplierTenantMap getSupplierTenantMap(@Param(value = "tenantId") String tenantId);
    String getMaxWorkNo(@Param(value = "sid") long sid, @Param(value = "tenantId") String tenantId);
    User getUser(User user);
    int insertUser(User user);
    int updateUser(User user);
    int updateUserTenantMap(UserTenantMap userTenantMap);
    int existUserPersonalinfoCount(@Param(value = "sid") long sid);
    int insertUserPersonalinfo(UserPersonalInfo userPersonalInfo);
    int selectSupplierEmployee(SupplierEmployee supplierEmployee);
    int insertSupplierEmployee(SupplierEmployee supplierEmployee);
    int updateSupplierEmployee(SupplierEmployee supplierEmployee);
    List<Supplier> getTenantList(@Param(value = "tenantId") String tenantId);
    List<Tenant> getTenantListNew(@Param(value = "sid") long sid);
    List<Supplier> getSupplierList(@Param(value = "tenantId") String tenantId);
    List<SupplierProduct> getSupplierProductList(Map<String,Object> map);
    UserTenantMap getUserTenantMap(@Param(value = "userId") String userId, @Param(value = "tenantId") String tenantId);
    int updateUserAuthorizeApp(Map<String,Object> map);
    SupplierProductModule getSupplierProductModule(SupplierProductModule module);
    int updateSupplierProductModule(SupplierProductModule module);
    int insertSupplierProductModule(SupplierProductModule module);
    Long getSupplierSidByTenantId(@Param(value = "tenantId") String tenantId);
    Tenant getTenant(@Param(value = "tenantId") String tenantId);
    List<TenantContract> getContractsByEid(@Param(value = "eid") long eid);
    List<Supplier> getISVUserSuppliers(@Param(value = "id") String id);
    Long getEidByAccount(@Param(value = "id") String id,@Param(value = "sid") long sid);
    List<Map<String,Object>> getTenantsById(@Param(value = "sid") long sid,@Param(value = "id") String id);
    
    /**
     * 根据用户ID查询用户租户信息
     * @param userId 用户ID
     * @return 用户租户信息列表
     */
    UserTenantInfoResponse selectUserInfo(@Param(value = "userId") String userId);
    UserTenantInfoResponse selectTenantInfo(@Param(value = "tenantSid") Long tenantSid);
}
