package com.digiwin.escloud.aiouser.amqp;

import com.digiwin.escloud.aiouser.constant.MqConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RabbitInitializer implements InitializingBean {

    @Autowired
    private AmqpAdmin amqpAdmin;

    /**
     * 初始化交换机，队列，绑定关系
     */
    @Override
    public void afterPropertiesSet() {
        ini();
    }

    private void ini() {

        DirectExchange directExchange = declareDirectExchange(MqConst.AIO_USER_MAIL_EXCHANGE);
        amqpAdmin.declareExchange(directExchange);
        Queue queue = declareQueue(MqConst.AIO_USER_MAIL_QUEUE);
        amqpAdmin.declareQueue(queue);
        Binding binding = declareDirectBinding(queue, directExchange, MqConst.AIO_USER_MAIL_ROUTING_KEY);
        amqpAdmin.declareBinding(binding);
        log.info("RabbitInitializer , {}",queue);
    }

    public Queue declareQueue(String queueName) {
        return new Queue(queueName);
    }

    public DirectExchange declareDirectExchange(String exchangeName) {
        return new DirectExchange(exchangeName);
    }

    public Binding declareDirectBinding(Queue queue, DirectExchange exchange, String routingKey) {
        return BindingBuilder.bind(queue).to(exchange).with(routingKey);
    }
}