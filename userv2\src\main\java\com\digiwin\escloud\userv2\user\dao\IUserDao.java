package com.digiwin.escloud.userv2.user.dao;

import com.digiwin.escloud.userv2.user.model.eservice.EServiceUser;
import com.digiwin.escloud.userv2.user.model.staff.StaffAccount;
import com.digiwin.escloud.userv2.user.model.user.User;
import com.digiwin.escloud.userv2.user.model.user.UserHasServiceCode;
import com.digiwin.escloud.userv2.user.model.user.UserPersonalInfo;
import com.digiwin.escloud.userv2.user.model.user.UserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IUserDao {
    int updateUserEmail(@Param("userId") String userId, @Param("email") String email);

    int updateUserPhone(@Param("userId") String userId, @Param("phone") String phone);

    UserPersonalInfo getUserPersonalInfo(String userId);

    int insertUser(User user);

    int insertUserV2(User user); //传userType

    int insertStaffAccount(@Param("userId") String userId, @Param("itcode") String itcode);

    User getUserByUserName(String userName);

    User getUserByUserNameAndUserType(@Param("userName") String userName, @Param("userType") String userType);

    User getStaffUserByEmail(@Param("email") String email, @Param("userType") String userType);

    User getUserByUserId(String userId);

    String getUserRoleExist(@Param("userId") String userId, @Param("role") String role);

    int insertUserRole(@Param("userId") String userId, @Param("role") String role);

    int saveUserPersonalInfo(UserPersonalInfo userPersonInfo);

    List<UserRole> getUserAllRoles(String userId);

    UserHasServiceCode getUserHasServiceCode(@Param("userId") String userId, @Param("serviceCode") String serviceCode);

    int insertUserHasServiceCode(UserHasServiceCode userHasServiceCode);

    int updateStaffUserName(User user);

    StaffAccount selectStaffAccount(@Param("itcode") String itcode);

    int getEServiceUser(@Param("email") String email,@Param("serviceCode") String serviceCode);

    EServiceUser getEServiceUserByEmail(@Param("email") String email);

    int insertEServiceUser(EServiceUser eServiceUserHis );

    int updateEserviceAccount(@Param("email") String email,@Param("userId") String userId);
}
