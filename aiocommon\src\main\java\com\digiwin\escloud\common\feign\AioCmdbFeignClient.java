package com.digiwin.escloud.common.feign;

import com.digiwin.escloud.aiocmdb.model.Field;
import com.digiwin.escloud.aiocmdb.model.FieldTypeEnum;
import com.digiwin.escloud.aiocmdb.model.ModelRelate;
import com.digiwin.escloud.aiocmdb.model.ModelRelateInstance;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemContextDTO;
import com.digiwin.escloud.common.constant.AioConstant;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.StarRocksQueryRequest;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.etl.model.EtlEngine;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@FeignClient(value = "aiocmdb", url = "${feign.aiocmdb.url:}")
public interface AioCmdbFeignClient {

    @ApiOperation(value = "根据id保存维护记录")
    @PostMapping(value = "/api/mr/save", consumes = MediaType.APPLICATION_JSON_VALUE)
    BaseResponse saveMrDetail(@RequestHeader(value = AioConstant.EID, required = false) Long eid,
                              @RequestParam(value = "modelCode") String modelCode,
                              @RequestParam(value = "id") String id,
                              @RequestBody Object targetValue);

    @ApiOperation(value = "根据模型key(支持多组)查询维护记录")
    @PostMapping(value = "/api/mr/detail/by/key/list")
    BaseResponse gerMrDetailByKey(
            @ApiParam(value = "模型编号", required = true)
            @RequestParam(value = "modelCode") String modelCode,
            @ApiParam(value = "是否包含分隔符")
            @RequestParam(value = "containSplitSign", required = false, defaultValue = "false") Boolean containSplitSign,
            @ApiParam(value = "模型keys", required = true)
            @RequestBody() List<String> keyList);
//
//    @ApiOperation(value = "根据模型条件字典查询维护记录")
//    @PostMapping(value = "/api/mr/detail/by/map")
//    BaseResponse gerMrDetailByMap(
//            @ApiParam(value = "模型编号", required = true)
//            @RequestParam(value = "modelCode") String modelCode,
//            @ApiParam(value = "条件字典", required = true)
//            @RequestBody() Map<String, Object> conditionMap);
//
//    @ApiOperation(value = "根据模型分组条件字典查询维护记录")
//    @PostMapping(value = "/detail/by/group/map")
//    BaseResponse gerMrDetailByGroupMap(
//            @ApiParam(value = "模型编号", required = true)
//            @RequestParam(value = "modelCode") String modelCode,
//            @ApiParam(value = "分组条件字典，依据DataContent、BasicInfo等modelGroupCode分组", required = true)
//            @RequestBody() Map<String, Object> groupConditionMap);
//
//    @ApiOperation(value = "根据模型分组条件字典删除维护记录")
//    @DeleteMapping(value = "/detail/by/group/map")
//    BaseResponse removeMrDetailByGroupMap(
//            @ApiParam(value = "模型编号", required = true)
//            @RequestParam(value = "modelCode") String modelCode,
//            @ApiParam(value = "分组条件字典，依据DataContent、BasicInfo等modelGroupCode分组", required = true)
//            @RequestBody() Map<String, Object> groupConditionMap);

    @ApiOperation(value = "获取所有模型")
    @GetMapping(value = "/api/model/getAllModel")
    BaseResponse getAllModel(@RequestParam(value = "modelGroupCode", required = false) String modelGroupCode);

    @ApiOperation(value = "根据模型code获取模型json")
    @PostMapping(value = "/api/model/json")
    BaseResponse<Map<String, Object>> getModelJson(
            @ApiParam(required = true, value = "模型Code")
            @RequestBody Collection<String> modelCodes);

    @ApiOperation(value = "根据模型code获取模型主键")
    @GetMapping(value = "/api/model/key/field")
    BaseResponse getModelKeyField(@RequestParam(value = "modelCode", required = true) String modelCode);

    @ApiOperation(value = "批量保存模型关联")
    @PostMapping(value = "/api/model/relate/batch/save")
    BaseResponse batchSaveModelRelate(
            @ApiParam(required = true, value = "模型关联列表")
            @RequestBody List<ModelRelate> modelRelateList);

    @ApiOperation(value = "依据来源信息获取模型关联数据")
    @PostMapping(value = "/api/model/relate/instance/by/source/info")
    BaseResponse getModelRelateInstanceBySourceInfo(
            @ApiParam(value = "来源条件列表", required = true)
            @RequestBody() List<Map<String, Object>> sourceConditions);

    @ApiOperation(value = "完整保存模型关联实例数据，此方法不填充任何Id，请再调用前确定已填妥")
    @PostMapping(value = "/api/model/relate/instance/complete")
    BaseResponse saveModelRelateInstanceComplete(
            @ApiParam("是否需要保存到大数据平台")
            @RequestParam(value = "needSaveBigDataPlatform", required = false, defaultValue = "false") Boolean needSaveBigDataPlatform,
            @ApiParam(value = "模型关系实例列表", required = true)
            @RequestBody List<ModelRelateInstance> modelRelateInstances);

    @ApiOperation(value = "删除模型关联实例数据")
    @DeleteMapping(value = "/api/model/relate/instance")
    BaseResponse removeModelRelateInstance(
            @ApiParam("关联实例Id")
            @RequestBody() List<Long> mriIdList);

    @ApiOperation(value = "批量依据模型关联实例条件删除模型关联实例")
    @DeleteMapping(value = "/api/model/batch/relate/instance/by/condition")
    BaseResponse batchRemoveModelRelateInstanceByCondition(
            @ApiParam("模型关联实例条件列表")
            @RequestBody() List<ModelRelateInstance> modelRelateInstanceList);

    @ApiOperation(value = "依据模型代号列表获取模型名称")
    @PostMapping(value = "/api/model/name/by/code/list")
    BaseResponse<Map<String, String>> getModelNameMapByCodeList(
            @ApiParam("运维商Id")
            @RequestHeader(value = AioConstant.SID, required = false) Long sid,
            @ApiParam("模型代号列表")
            @RequestBody() Collection<String> codeList);

    @ApiOperation(value = "生成资产维护的设备")
    @PostMapping(value = "/asset")
    BaseResponse<Map<String, String>> generateMaintenanceAsset(
            @ApiParam("旧设备类型")
            @RequestParam(value = "oldDeviceType") String oldDeviceType,
            @ApiParam("新设备类型")
            @RequestParam(value = "newDeviceType") String newDeviceType,
            @ApiParam("设备id")
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam("eid")
            @RequestParam(value = "eid") Long eid,
            @ApiParam("是否来源于心跳包")
            @RequestParam(value = "isSaveByHeartbeat") Boolean isSaveByHeartbeat);

    @GetMapping(value = "/api/model/dmp/field")
    BaseResponse<List<Map<String, String>>> getModelDmpField(@RequestParam(value = "modelCode") String modelCode);

    @ApiOperation(value = "依据字典列表查询维护记录")
    @PostMapping(value = "/api/mr/detail/by/map/list")
    BaseResponse<List<Map<String, Object>>> getMrDetailByMapList(
            @ApiParam("租户Sid")
            @RequestHeader(value = AioConstant.EID, required = false) Long eid,
            @ApiParam("字典列表")
            @RequestBody() List<Map<String, Object>> mapList);

    @ApiOperation(value = "根据id列表删除维护记录")
    @DeleteMapping(value = "/api/mr/by/id/list")
    BaseResponse removeMrDetailByIdList(
            @ApiParam("租户Sid")
            @RequestHeader(value = AioConstant.EID, required = false) Long eid,
            @ApiParam("模型代号")
            @RequestParam(value = "modelCode") String modelCode,
            @ApiParam("id列表")
            @RequestBody() List<String> idList);

    @ApiOperation(value = "依据条件字典获取etl列表")
    @PostMapping(value = "/api/etl/list/by/map")
    BaseResponse<List<EtlEngine>> getEtlListByMap(
            @ApiParam("应用代号")
            @RequestHeader(value = AioConstant.APP_CODE, required = false, defaultValue = AioConstant.DEFAULT_APP) String appCode,
            @ApiParam(value = "条件字典", required = true)
            @RequestBody() Map<String, Object> map);

    @ApiOperation(value = "异动sinkFieldsJson")
    @PutMapping(value = "/api/etl/sink/fields/json")
    BaseResponse modifyEtlFieldJson(
            @ApiParam(value = "etl引擎Id", required = true)
            @RequestParam("eeId") Long eeId,
            @ApiParam(value = "字段集合", required = true)
            @RequestBody() Collection<Map<String, Object>> sinkFields);


    @ApiOperation(value = "栏位筛选列表")
    @GetMapping(value = "/api/etl/field/list")
    BaseResponse getEtlSinkFields(@RequestParam(value = "sinkType") String sinkType,
                                         @RequestParam(value = "schemaName") String schemaName,
                                         @RequestParam(value = "sinkName") String sinkName);
    @ApiOperation(value = "更新资产设备名称")
    @PutMapping(value = "/api/assetmaintenance/updateDeviceName")
    BaseResponse updateDeviceName(@RequestParam(value = "modelCode") String modelCode,
                                  @RequestParam(value = "deviceId") String deviceId,
                                  @RequestParam(value = "deviceName") String deviceName);

    @ApiOperation(value = "更新资产设备名称")
    @PutMapping(value = "/asset/invalidCmdbAsset")
    BaseResponse invalidCmdbAsset(List<AiopsItemContextDTO> aicList);

    @PostMapping(value = "/api/field/findList")
    BaseResponse<List<Field>> getFieldListByFieldCodeList(
            @RequestBody List<String> fieldCodeList);

    @ApiOperation(value = "根据字段编码列表获取枚举配置")
    @PostMapping(value = "/api/field/getFieldTypeEnumByFieldCodes")
    BaseResponse<List<FieldTypeEnum>> getFieldTypeEnumByFieldCodes(
            @RequestBody List<String> fieldCodes);

    /**
     * 支持传入查询列和过滤条件，因为这是union all所以查询列和过滤条件列每个表都需要有
     *
     * @param request 查询请求参数
     * @return 查询结果
     */
    @ApiOperation(value = "StarRocks数据查询", notes = "支持传入查询列和过滤条件的StarRocks查询API")
    @PostMapping(value = "/api/starrocks/union/query")
    ResponseBase<List<Map<String, Object>>> queryStarRocksData(@RequestBody StarRocksQueryRequest request);

    @ApiOperation(value = "StarRocks数据查询", notes = "支持传入查询列和过滤条件的StarRocks查询API")
    @PostMapping(value = "/api/starrocks/union/queryByAiopsItems")
    ResponseBase<List<Map<String, Object>>> queryByAiopsItems(@RequestBody StarRocksQueryRequest request);

    @GetMapping("/asset/assetChangeApplication/detailWithOperationRecords/{applicationId}")
    ResponseBase<com.digiwin.escloud.aiocmdb.model.assetchange.AssetChangeApplication> getAssetChangeApplicationDetailWithOperationRecords(@PathVariable Long applicationId);

    }
