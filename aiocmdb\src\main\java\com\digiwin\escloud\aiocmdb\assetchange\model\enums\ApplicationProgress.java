package com.digiwin.escloud.aiocmdb.assetchange.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 申请进度枚举
 * 用于前端展示和搜索条件转换
 * 
 * 进度分类：
 * - 申请进度：NOT_SUBMITTED
 * - 审批进度：NOT_APPROVED、APPROVAL_RETURNED
 * - 执行进度：NOT_EXECUTED、IN_EXECUTION
 * - 验收进度：NOT_ACCEPTED、ACCEPTANCE_RETURNED
 * - 结案进度：CLOSED
 */
@Getter
public enum ApplicationProgress {

    /**
     * 申请进度
     */
    APPLICATION("申请进度", Arrays.asList(ApplicationStatus.NOT_SUBMITTED)),

    /**
     * 审批进度
     */
    APPROVAL("审批进度", Arrays.asList(ApplicationStatus.NOT_APPROVED, ApplicationStatus.APPROVAL_RETURNED)),

    /**
     * 执行进度
     */
    EXECUTION("执行进度", Arrays.asList(ApplicationStatus.NOT_EXECUTED, ApplicationStatus.IN_EXECUTION)),

    /**
     * 验收进度
     */
    ACCEPTANCE("验收进度", Arrays.asList(ApplicationStatus.NOT_ACCEPTED, ApplicationStatus.ACCEPTANCE_RETURNED)),

    /**
     * 结案进度
     */
    CLOSED("结案进度", Arrays.asList(ApplicationStatus.CLOSED));

    private final String label;
    private final List<ApplicationStatus> applicationStatuses;

    ApplicationProgress(String label, List<ApplicationStatus> applicationStatuses) {
        this.label = label;
        this.applicationStatuses = applicationStatuses;
    }

    /**
     * 根据申请单状态获取对应的申请进度
     *
     * @param applicationStatus 申请单状态
     * @return 对应的申请进度，如果不存在则返回null
     */
    public static ApplicationProgress fromApplicationStatus(ApplicationStatus applicationStatus) {
        if (applicationStatus == null) {
            return null;
        }

        for (ApplicationProgress progress : ApplicationProgress.values()) {
            if (progress.getApplicationStatuses().contains(applicationStatus)) {
                return progress;
            }
        }
        return null;
    }

    /**
     * 根据申请单状态字符串获取对应的申请进度
     *
     * @param applicationStatusStr 申请单状态字符串
     * @return 对应的申请进度，如果不存在则返回null
     */
    public static ApplicationProgress fromApplicationStatusString(String applicationStatusStr) {
        ApplicationStatus status = ApplicationStatus.fromString(applicationStatusStr);
        return fromApplicationStatus(status);
    }

    /**
     * 根据进度枚举名称获取对应的申请单状态列表
     *
     * @param progressName 进度枚举名称
     * @return 对应的申请单状态列表，如果不存在则返回空列表
     */
    public static List<ApplicationStatus> getApplicationStatusesByProgressName(String progressName) {
        if (progressName == null) {
            return Arrays.asList();
        }

        for (ApplicationProgress progress : ApplicationProgress.values()) {
            if (progress.name().equals(progressName)) {
                return progress.getApplicationStatuses();
            }
        }
        return Arrays.asList();
    }

    /**
     * 根据进度标签获取对应的申请单状态列表
     *
     * @param progressLabel 进度标签
     * @return 对应的申请单状态列表，如果不存在则返回空列表
     */
    public static List<ApplicationStatus> getApplicationStatusesByProgressLabel(String progressLabel) {
        if (progressLabel == null) {
            return Arrays.asList();
        }

        for (ApplicationProgress progress : ApplicationProgress.values()) {
            if (progress.getLabel().equals(progressLabel)) {
                return progress.getApplicationStatuses();
            }
        }
        return Arrays.asList();
    }

    /**
     * 根据字符串获取对应的申请单状态列表（支持枚举名称和标签）
     *
     * @param progressStr 进度字符串（枚举名称或标签）
     * @return 对应的申请单状态列表，如果不存在则返回空列表
     */
    public static List<ApplicationStatus> getApplicationStatusesByString(String progressStr) {
        if (progressStr == null) {
            return Arrays.asList();
        }

        // 先尝试按枚举名称匹配
        List<ApplicationStatus> statusesByName = getApplicationStatusesByProgressName(progressStr);
        if (!statusesByName.isEmpty()) {
            return statusesByName;
        }

        // 再尝试按标签匹配
        return getApplicationStatusesByProgressLabel(progressStr);
    }

    /**
     * 判断传入的字符串是否与指定的进度枚举相等
     *
     * @param value    要比较的字符串（可以是枚举名称或标签）
     * @param progress 指定的进度枚举值
     * @return 如果匹配则返回true，否则返回false
     */
    public static boolean isEqual(String value, ApplicationProgress progress) {
        if (value == null || progress == null) {
            return false;
        }
        return progress.name().equals(value) || progress.getLabel().equals(value);
    }
}
