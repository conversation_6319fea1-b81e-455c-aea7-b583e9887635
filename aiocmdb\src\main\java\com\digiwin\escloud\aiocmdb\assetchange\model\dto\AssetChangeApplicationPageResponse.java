package com.digiwin.escloud.aiocmdb.assetchange.model.dto;

import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationProgress;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 资产变更申请单分页查询响应DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@ApiModel(value = "AssetChangeApplicationPageResponse", description = "资产变更申请单分页查询响应")
@Data
public class AssetChangeApplicationPageResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("企业ID")
    private Long eid;

    @ApiModelProperty("申请单编号")
    private String applicationNumber;

    @ApiModelProperty("申请日期")
    private LocalDate applicationDate;

    @ApiModelProperty("申请人用户ID")
    private String applicantUserId;

    @ApiModelProperty("申请人名称")
    private String applicantName;

    @ApiModelProperty("申请人单位")
    private String applicantUnit;

    @ApiModelProperty("申请类别")
    private String applicationCategory;

    @ApiModelProperty("变更范围")
    private String changeRange;

    @ApiModelProperty("申请单状态")
    private String applicationStatus;

    @ApiModelProperty("申请进度（根据申请单状态计算得出）")
    private String applicationProgress;

    @ApiModelProperty("申请进度标签（用于前端显示）")
    private String applicationProgressLabel;

    @ApiModelProperty("最近一次审批意见")
    private String latestApprovalComment;

    @ApiModelProperty("最近一次验收意见")
    private String latestAcceptanceComment;

    @ApiModelProperty("变更背景原因")
    private String changeBackgroundReason;

    @ApiModelProperty("变更内容描述")
    private String changeContentDescription;

    @ApiModelProperty("变更优先级")
    private String changePriority;

    @ApiModelProperty("风险评估")
    private String riskAssessment;

    @ApiModelProperty("还原计划")
    private String rollbackPlan;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    private Double executionComplete;

    /**
     * 设置申请进度相关字段
     * 根据申请单状态自动计算申请进度
     *
     * @param applicationStatus 申请单状态
     */
    public void setApplicationStatusAndProgress(String applicationStatus) {
        this.applicationStatus = applicationStatus;
        
        // 根据申请单状态计算申请进度
        ApplicationProgress progress = ApplicationProgress.fromApplicationStatusString(applicationStatus);
        if (progress != null) {
            this.applicationProgress = progress.name();
            this.applicationProgressLabel = progress.getLabel();
        } else {
            this.applicationProgress = null;
            this.applicationProgressLabel = null;
        }
    }
}
