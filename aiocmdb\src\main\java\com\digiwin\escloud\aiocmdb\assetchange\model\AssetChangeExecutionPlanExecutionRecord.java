package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 执行记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Data
@ApiModel(value = "AssetChangeExecutionPlanExecutionRecord对象", description = "执行记录")
public class AssetChangeExecutionPlanExecutionRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("计划ID")
    private Long planId;

    @ApiModelProperty("执行人用户ID")
    private String executorUserId;

    @ApiModelProperty("执行人名称")
    private String executorName;

    @ApiModelProperty("计划状态")
    private String planStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("实际计划开始日期")
    private LocalDateTime actualPlanStartDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("实际计划结束日期")
    private LocalDateTime actualPlanEndDate;

    @ApiModelProperty("系统变更前版本")
    private String systemPreChangeVersion;

    @ApiModelProperty("系统变更前描述")
    private String systemPreChangeDescription;

    @ApiModelProperty("系统变更后版本")
    private String systemPostChangeVersion;

    @ApiModelProperty("系统变更后描述")
    private String systemPostChangeDescription;

    @ApiModelProperty("执行说明")
    private String executionExplanation;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;



}
