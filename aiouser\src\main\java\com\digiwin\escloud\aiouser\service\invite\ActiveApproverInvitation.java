package com.digiwin.escloud.aiouser.service.invite;

import com.digiwin.escloud.aiouser.model.common.InvitationGetResponse;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date: 2025-05-19 17:43
 * @Description
 */
@Service("ACTIVATE_ASSET_APPROVER")
public class ActiveApproverInvitation extends ActivateInvitation {
    @Override
    public InvitationGetResponse buildInvitation(InvitationGetResponse res) {
        return super.buildInvitation(res);
    }
}
