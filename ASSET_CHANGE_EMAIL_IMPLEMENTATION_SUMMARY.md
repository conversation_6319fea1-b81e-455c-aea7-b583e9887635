# 资产变更审批邮件实现总结

## 🎯 实现概述

根据您提供的图片要求，已完成资产变更审批邮件的HTML模板和Java代码实现，支持：
1. ✅ 使用行内样式的HTML邮件模板
2. ✅ 动态参数填充（%s占位符）
3. ✅ 背景图片处理（立即审批按钮）
4. ✅ 表格数据动态显示
5. ✅ 申请说明字段组合逻辑

## 📁 已创建/修改的文件

### 1. HTML邮件模板
**文件**: `aiomail/src/main/resources/temp/mail/zh_TW/assetChangeApprovalMail.html`
- ✅ 完全使用行内样式，兼容邮件客户端
- ✅ 响应式表格设计
- ✅ 立即审批按钮使用背景图片
- ✅ 8个%s占位符对应Java参数

### 2. Java邮件处理逻辑
**文件**: `aiouser/src/main/java/com/digiwin/escloud/aiouser/service/mail/ActiveApproverMail.java`
- ✅ 自动识别资产变更邮件（通过invitation.getApplication()判断）
- ✅ 动态构建申请说明HTML内容
- ✅ 支持空值处理和默认值

### 3. 邮件服务图片处理
**文件**: `aiomail/src/main/java/com/digiwin/escloud/aiomail/services/Impl/MailService.java`
- ✅ 为ActiveApprover类型添加背景图片处理
- ✅ 自动加载`asset_change_backgroud.png`图片

### 4. 测试和文档
- ✅ `AssetChangeApprovalMailTest.java` - 测试类
- ✅ `ASSET_CHANGE_MAIL_USAGE.md` - 使用说明文档

## 🔧 参数映射关系

HTML模板中的%s占位符按顺序对应：

| 序号 | HTML占位符 | Java参数 | 说明 |
|------|------------|----------|------|
| 1 | `%s` | `invitation.getInvitedName()` | 收件人姓名 |
| 2 | `%s` | `invitation.getLinkUrl()` | 立即审批链接 |
| 3 | `%s` | `application.getApplicationNumber()` | 申请单编号 |
| 4 | `%s` | `application.getApplicantUnit()` | 申请单位 |
| 5 | `%s` | `application.getApplicantName()` | 申请人 |
| 6 | `%s` | `application.getApplicationDate()` | 申请日期 |
| 7 | `%s` | `application.getApplicationCategory()` | 申请类别 |
| 8 | `%s` | `buildApplicationDescription()` | 申请说明（动态构建） |

## 📋 申请说明构建逻辑

`buildApplicationDescription()`方法会检查以下字段，只显示有值的字段：

```java
// 变更优先级（红色星号标记）
if (StringUtils.hasText(application.getChangePriority())) {
    description.append("<strong style='color: #d32f2f;'>* 变更优先级</strong><br>");
    description.append(application.getChangePriority());
}

// 变更背景与原因
if (StringUtils.hasText(application.getChangeBackgroundReason())) {
    description.append("<strong>变更背景与原因</strong><br>");
    description.append(application.getChangeBackgroundReason());
}

// 变更内容描述
if (StringUtils.hasText(application.getChangeContentDescription())) {
    description.append("<strong>变更内容描述</strong><br>");
    description.append(application.getChangeContentDescription());
}

// 变更范围
if (StringUtils.hasText(application.getChangeRange())) {
    description.append("<strong>变更范围</strong><br>");
    description.append(application.getChangeRange());
}
```

## 🖼️ 图片资源处理

### 背景图片
- **文件名**: `asset_change_backgroud.png`
- **位置**: `aiomail/src/main/resources/temp/mail/zh_TW/`
- **用途**: "立即审批"按钮背景
- **HTML引用**: `url('cid:imageAssetChangeBackground')`

### 邮件头尾
- **邮件头**: `cid:imageMailHeader`
- **邮件尾**: `cid:imageMailFooter`

## 🚀 使用方法

### 1. 创建Invitation对象
```java
Invitation invitation = new Invitation();
invitation.setInvitedEmail("<EMAIL>");
invitation.setInvitedName("审批者姓名");
invitation.setLinkUrl("https://escloud.digiwin.com/approval/123");

// 设置资产变更申请单
AssetChangeApplication application = new AssetChangeApplication();
application.setApplicationNumber("CR-20250815-001");
application.setApplicantName("申请人");
application.setApplicantUnit("申请单位");
application.setApplicationCategory("申请类别");
application.setChangePriority("高");
application.setChangeBackgroundReason("变更背景原因");
application.setChangeContentDescription("变更内容描述");
application.setChangeRange("变更范围");

invitation.setApplication(application);
```

### 2. 发送邮件
```java
@Autowired
private ActiveApproverMail activeApproverMail;

String[] mailJson = activeApproverMail.prepareMail(invitation, supplierOtherInfo);
// 邮件JSON已准备好，可发送到消息队列
```

## ✅ 实现特点

### 1. 自动识别邮件类型
- 如果`invitation.getApplication() != null`，发送资产变更审批邮件
- 否则发送普通审批邮件

### 2. 完全行内样式
- 所有CSS样式都使用行内方式
- 确保在各种邮件客户端中正常显示

### 3. 响应式设计
- 表格使用百分比宽度
- 适配不同屏幕尺寸

### 4. 图片兼容性
- 使用`cid:`引用内嵌图片
- 自动处理图片资源加载

## 🧪 测试验证

运行测试类验证功能：
```bash
mvn test -Dtest=AssetChangeApprovalMailTest
```

## 📝 注意事项

1. **参数顺序**: String.format()中的参数顺序必须与HTML模板中的%s顺序完全一致
2. **图片文件**: 确保`asset_change_backgroud.png`文件存在于正确路径
3. **空值处理**: 代码已处理空值情况，避免显示null
4. **邮件兼容性**: 使用行内样式确保跨邮件客户端兼容

## 🔄 扩展支持

### 多语言支持
- 当前支持繁体中文 (zh_TW)
- 可扩展其他语言，需创建对应目录和HTML模板

### 自定义字段
- 在HTML模板中添加新的%s占位符
- 在Java代码的String.format()中添加对应参数

---

**实现完成！** 🎉 

您现在可以使用这个完整的邮件系统来发送资产变更审批邮件，所有功能都按照您的要求实现，包括行内样式、动态参数、背景图片和表格数据展示。
