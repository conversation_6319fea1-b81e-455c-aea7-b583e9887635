package com.digiwin.escloud.aiocmdb.assetchange.service.impl;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.aiocmdb.assetchange.service.BaseChangeRequestState;
import org.springframework.stereotype.Component;

@Component(ApplicationStatus.NOT_ACCEPTED_NAME)
public class PendingAcceptanceState extends BaseChangeRequestState {
    @Override
    public String getStateName() {
        return ApplicationStatus.NOT_ACCEPTED.name();
    }

    @Override
    public void acceptPass(AssetChangeApplication context) {
        context.setApplicationStatus(ApplicationStatus.CLOSED.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.CLOSED.name()));
    }

    @Override
    public void acceptFail(AssetChangeApplication context) {
        context.setApplicationStatus(ApplicationStatus.CLOSED.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.CLOSED.name()));
    }

    @Override
    public void acceptAdjust(AssetChangeApplication context) {
        // “验收退回”要求返工，回到“验收退回”状态
        context.setApplicationStatus(ApplicationStatus.ACCEPTANCE_RETURNED.name());
        context.setCurrentState(stateFactory.getState(ApplicationStatus.ACCEPTANCE_RETURNED.name()));
    }
}

