package com.digiwin.escloud.aiobasic.assetrisk.service.impl;

import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.digiwin.escloud.aiobasic.assetrisk.dao.AssetRiskAssessmentMapper;
import com.digiwin.escloud.aiobasic.assetrisk.dao.RiskAssessmentProjectMapper;
import com.digiwin.escloud.aiobasic.assetrisk.dao.RiskImprovePlanMapper;
import com.digiwin.escloud.aiobasic.assetrisk.exception.RiskException;
import com.digiwin.escloud.aiobasic.assetrisk.model.ImprovePlanExcelData;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskImprovePlan;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskImprovePlanDetail;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.ProcessDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskImprovePlan2DTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskImprovePlanSystemDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.AssessmentStatus;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.PlanOperate;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.PlanStatus;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskAssessmentParam;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskImprovePlanParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.RiskImprovePlanService;
import com.digiwin.escloud.aiobasic.edr.util.DateUtils;
import com.digiwin.escloud.aiobasic.mail.MailSendService;
import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.aiouser.model.tenant.TenantMapDTO;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.messagelibrary.Model.MessageDestination;
import com.digiwin.escloud.messagelibrary.ProducerBaseInterface;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aiobasic.assetrisk.model.enums.PlanStatus.*;


@Slf4j
@Service
public class RiskImprovePlanServiceImpl implements RiskImprovePlanService {
    @Resource
    private RiskImprovePlanMapper riskImprovePlanMapper;
    @Resource
    private AssetRiskAssessmentMapper assetRiskAssessmentMapper;

    @Resource
    private UploadFileService uploadFileService;


    @Value("${aiobasic.address}")
    private String aiobasicAddress;
    @Resource
    private AioUserFeignClient aioUserFeignClient;

    @Resource
    private RiskAssessmentProjectMapper riskAssessmentProjectMapper;

    @Resource
    private MailSendService mailSendService;
    @Resource
    private RiskCheckService riskCheckService;

    @Override
    public ResponseBase improvePlanGet(RiskImprovePlanParam param) {

        if (StringUtils.isBlank(param.getAssessmentId())) {
            return ResponseBase.error(ResponseCode.RISK_ASSESSMENT_NEED);
        }

        return ResponseBase.ok(planGet(param));
    }

    private PageInfo<RiskImprovePlan2DTO> planGet(RiskImprovePlanParam param) {
        //如果改善控制措施的id已经没有被任何风险分析使用，改善计划也会删除
        riskImprovePlanMapper.selectAllByCondition(param).forEach(plan -> {
            String planId = riskAssessmentProjectMapper.selectPlanIdUsed(plan.getControlPlanId(), param.getAssessmentId());
            if (Objects.isNull(planId)) {
                //删除改善计划
                riskImprovePlanMapper.deletePlanById(plan.getId());
            }
        });

        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<RiskImprovePlan2DTO> improvePlan = riskImprovePlanMapper.selectAllByCondition(param);

        List<String> controlPlanIds =
                improvePlan.stream().map(RiskImprovePlan2DTO::getControlPlanId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(controlPlanIds)) {
            List<RiskImprovePlanSystemDTO> riskImprovePlanSystemDTOS =
                    riskImprovePlanMapper.selectEffectSystemByControlPlanIds(controlPlanIds, param.getAssessmentId());
            Map<String, List<RiskImprovePlanSystemDTO>> idMap =
                    riskImprovePlanSystemDTOS.stream().
                            collect(Collectors.groupingBy(RiskImprovePlanSystemDTO::getControlPlanId));
            improvePlan.forEach(plan -> {
                        Set<RiskImprovePlanSystemDTO> effectSystem = plan.getEffectSystem();
                        if (Objects.nonNull(idMap.get(plan.getControlPlanId()))) {
                            effectSystem.addAll(idMap.get(plan.getControlPlanId()));
                        }
                        if (Objects.isNull(plan.getObject())) {
                            plan.setObject(effectSystem.stream()
                                    .map(RiskImprovePlanSystemDTO::getAssetSystemName).collect(Collectors.joining(",")));
                            RiskImprovePlan actualPlan = new RiskImprovePlan();
                            BeanUtils.copyProperties(plan, actualPlan);
                            riskImprovePlanMapper.updateRiskImprovePlan(actualPlan);
                        }
                    }
            );
        }
        return new PageInfo<>(improvePlan);
    }

    @Override
    public ResponseBase improvePlanDetailGet(RiskImprovePlanParam param) {
        if (StringUtils.isBlank(param.getId())) {
            return ResponseBase.error(ResponseCode.RISK_IS_NEED);
        }
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<RiskImprovePlanDetail> detail = riskImprovePlanMapper.selectDetailByPlanId(param.getId());

        return ResponseBase.ok(new PageInfo<>(detail));
    }

    @Override
    public ResponseBase improvePlanUpdate(RiskImprovePlanParam param) {
        if (checkPlanUD(param.getId())) {
            return ResponseBase.error(ResponseCode.RISK_ASSESSMENT_NO_UD);
        }
        param.setRemark(PlanOperate.DRAFT.getDescription());
        return planUpdate(param, PlanOperate.DRAFT, ALREADY_PLAN);
    }

    @Override
    public ResponseBase improvePlanDown(RiskImprovePlanParam param, String eid) {
        List<ImprovePlanExcelData> excelData = dataGet(param);
        String url;
        try {
            //客户代号 supplier_tenant_map
            Map<Long, TenantMapDTO> eMap = getTenantMap(Long.valueOf(eid));
            String fileName = (Objects.isNull(eMap.get(Long.valueOf(eid))) ? "" : eMap.get(Long.valueOf(eid)).getServiceCode())
                    + "_" + (Objects.isNull(eMap.get(Long.valueOf(eid))) ? "" : eMap.get(Long.valueOf(eid)).getName()) +
                    (StringUtils.isNotBlank(param.getStyle()) && param.getStyle().equals("2") ? "_改善计划清单_" : "_改善計劃清單_")
                    + DateUtil.getNowFormatString("yyyyMMddHHmmss");

            url = uploadFileService.createAssessmentImproveExcel(excelData, fileName, "改善計劃", ImprovePlanExcelData.class
                    , new LongestMatchColumnWidthStyleStrategy());
        } catch (RiskException e) {
            return ResponseBase.error(e.getErrorCode(), e.getMessage());
        }
        return ResponseBase.ok(url);
    }

    @Override
    public void planDetailSave(String planId, String tracker, PlanOperate operate, String operatingDesc, String remark) {
        //保存明细
        RiskImprovePlanDetail detail = new RiskImprovePlanDetail();
        detail.setId(SnowFlake.getInstance().newIdStr());
        detail.setPlanId(planId);
        detail.setTrackDate(new Date());
        detail.setTracker(tracker);
        detail.setOperate(operate);
        detail.setOperatingDesc(operatingDesc);
        detail.setRemark(remark);
        riskImprovePlanMapper.insertImproveDetail(detail);
    }

    @Override
    public ResponseBase planReview(RiskImprovePlanParam param, Long eid) {
        if (StringUtils.isBlank(param.getReviewMail())) {
            return ResponseBase.error(ResponseCode.RISK_IMPROVE_PLAN_REVIEW_MAIL_ERROR);
        }

        if (StringUtils.isBlank(param.getTracker())) {
            return ResponseBase.error(ResponseCode.RISK_IMPROVE_PLAN_PARAM_ERROR);
        }

        List<ImprovePlanExcelData> plans = dataGet(param);
        if (plans.stream().anyMatch(i -> i.getPlanStatus().equals(PlanStatus.NO_PLAN.getDescription()))) {
            return ResponseBase.error(ResponseCode.RISK_IMPROVE_PLAN_STATUS_ERROR);
        }
        plans = plans.stream()
                .filter(i -> i.getPlanStatus().equals(ALREADY_PLAN.getDescription())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(plans)) {
            return ResponseBase.ok();
        }
        Map<Long, TenantMapDTO> eMap = getTenantMap(eid);
        List<String> receivers = mailReceivers(param.getReviewMail()).stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        for (String receiver:receivers) {
            String mailText = reviewMailGenerate(plans, param.getAssessmentId(), receiver,param.getLanguage());
            log.info("[planReview] send mail : {}",receiver);
            mailSendService.sendEdrReportMail(eMap.get(eid).getServiceCode(), Lists.newArrayList(receiver), param.getLanguage().equals("zh-TW")?"資安風險評估改善計劃審核通知(系統發送請勿回信)"
                            :"资安风险评估改善计划审核通知(系统发送请勿回信)", mailText
                    , param.getLanguage(), null, MailSourceType.RiskImprovePlan);
            log.info("[planReview] mailText:{}", mailText);
        }

        planReview(param.getAssessmentId(), PlanStatus.IN_REVIEW, ALREADY_PLAN, param.getTracker());
        plans.forEach(plan -> planDetailSave(plan.getId(), param.getTracker(),
                PlanOperate.IN_REVIEW, PlanOperate.IN_REVIEW.getDescription(), param.getReviewMail()));
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase isPlanReview(RiskImprovePlanParam param, Long eid) {
        List<ImprovePlanExcelData> plans = dataGet(param);
        if (CollectionUtils.isEmpty(plans) ) {
            return ResponseBase.error(ResponseCode.RISK_IMPROVE_PLAN_STATUS_ERROR);
        }
        if (plans.stream().anyMatch(i -> i.getPlanStatus().equals(PlanStatus.NO_PLAN.getDescription()))) {
            return ResponseBase.error(ResponseCode.RISK_IMPROVE_PLAN_STATUS_NEED_DO);
        }
        if (plans.stream().anyMatch(i -> i.getPlanStatus().equals(PlanStatus.ALREADY_PLAN.getDescription()))) {
            return ResponseBase.ok();
        }
        return ResponseBase.error(ResponseCode.RISK_IMPROVE_PLAN_STATUS_ERROR);
    }

    @Override
    public ResponseBase planReview(String assessmentId, PlanStatus planStatus, PlanStatus oldPlanStatus, String tracker) {
        RiskImprovePlanParam param = new RiskImprovePlanParam();
        param.setAssessmentId(assessmentId);
        List<ImprovePlanExcelData> plans = dataGet(param);
        RiskImprovePlan plan = new RiskImprovePlan();
        plan.setAssessmentId(assessmentId);
        plan.setPlanStatus(planStatus);
        plan.setOldPlanStatus(oldPlanStatus);
        int row = riskImprovePlanMapper.updateRiskImprovePlanStatusByAssessmentId(plan);
        //审批通过，评估变成评估完成
        if (row > 0) {
            if (planStatus.equals(REVIEW_COMPLETE)) {
                RiskAssessmentParam assessmentParam = RiskAssessmentParam.builder().id(assessmentId)
                        .newAssessmentStatus(AssessmentStatus.ASSESS_COMPLETE)
                        .oldAssessmentStatus(AssessmentStatus.ASSESSING).build();
                assetRiskAssessmentMapper.updateRiskAssessmentStatus(assessmentParam);
            }
            if (!planStatus.equals(IN_REVIEW)) {
                plans.forEach(i -> planDetailSave(i.getId(), tracker,
                        PlanOperate.REVIEW_COMPLETE, "審核狀態_" + planStatus.getDescription(), tracker));
            }
        }else {

            plans.forEach(i -> planDetailSave(i.getId(), tracker,
                    PlanOperate.REVIEW_COMPLETE, "審核狀態錯誤_重複點擊審核", tracker));
        }

        return ResponseBase.ok(row);
    }

    @Override
    public ResponseBase improvePlanExecute(RiskImprovePlanParam param) {
        param.setRemark(PlanOperate.EXECUTING.getDescription());
        return planUpdate(param, PlanOperate.EXECUTING, PlanStatus.EXECUTING);
    }

    @Override
    public ResponseBase improvePlanExecuteProcess(String assessmentId) {
        Map<String, List<ProcessDTO>> retMap = new HashMap<>();
        List<ProcessDTO> processDTOS =
                riskImprovePlanMapper.selectExecuteProcessByAssessmentId(assessmentId);
        retMap.put("process", processDTOS);
        return ResponseBase.ok(retMap);
    }

    @Override
    public ResponseBase improvePlanExecuteComplete(RiskImprovePlanParam param) {
        param.setRemark(EXECUTING_COMPLETE.getDescription());
        return planUpdate(param, PlanOperate.EXECUTING_COMPLETE, PlanStatus.EXECUTING_COMPLETE);
    }

    private List<ImprovePlanExcelData> dataGet(RiskImprovePlanParam param) {
        //todo 暂时写死取最大值
        param.setPageSize(10000);
        PageInfo<RiskImprovePlan2DTO> riskImprovePlan2DTOPageInfo = planGet(param);
        List<RiskImprovePlan2DTO> improvePlan = riskImprovePlan2DTOPageInfo.getList();
        return improvePlan.stream().map(this::convert).collect(Collectors.toList());
    }

    private String reviewMailGenerate(List<ImprovePlanExcelData> data, String assessmentId, String mail,String language) {
        String success = aiobasicAddress + "/risk/improve/plan/planReview?assessmentId=" + assessmentId
                + "&planStatus=REVIEW_COMPLETE" + "&tracker=" + mail+"&area="+language;
        String back = aiobasicAddress + "/risk/improve/plan/planReview?assessmentId=" + assessmentId
                + "&planStatus=ALREADY_PLAN" + "&tracker=" + mail+"&area="+language;
        StringBuilder htmlTextBuilder = new StringBuilder();
        if (language.equals("zh-CN")) {
            htmlTextBuilder.append("<div>");
            htmlTextBuilder.append("<div style='font-family:Noto Sans TC'>\n"
                            + "    <div style='background:#F5F5F5;text-indent: 2em;color:#666666;padding:10px;margin:8px 0px 12px 0px '>于 <span\n"
                            + "            style='color:#000000'>").append(DateUtil.getNowFormatString(DateUtils.DATE_FORMATTER))
                    .append("</span>发起的资安风险评估已完成，并已制定改善计划，需要请您审核！\n").append("    </div>\n").append("    <div>\n")
                    .append("         <a href='" + success + "'><button style='cursor:pointer;color:#ffffff;background:#1890ff;border:#1890ff;padding:8px 12px;margin-right:24px;border-radius:4px'\n")
                    .append("                onclick=\"javascript:{\n").append("  var xhr = new XMLHttpRequest();\n")
                    .append("  xhr.open('GET', '")
                    .append(success)
                    .append("');\n")
                    .append("  xhr.send();\n")
                    .append("}\">\n")
                    .append("            审核通过\n")
                    .append("        </button></a>\n")
                    .append("       <a href='" + back + "'> <button style='cursor:pointer;;border:1px #E6E6E6 solid;padding:8px 12px;border-radius:4px;background:#ffffff'\n")
                    .append("                onclick=\"javascript:{\n")
                    .append("  var xhr = new XMLHttpRequest();\n")
                    .append("  xhr.open('GET', '").append(back).append("');\n")
                    .append("  xhr.send();\n").append("}\">审核不通过\n")
                    .append("        </button></a>\n").append("    </div>\n")
                    .append("    <p style='padding-top:12px'>改善计划如下：</p>");

            mailTableBuild(data, htmlTextBuilder);
            htmlTextBuilder.append("</div>");
            htmlTextBuilder.append("</div>");
            return htmlTextBuilder.toString();
        }
        htmlTextBuilder.append("<div>");
        htmlTextBuilder.append("<div style='font-family:Noto Sans TC'>\n"
                        + "    <div style='background:#F5F5F5;text-indent: 2em;color:#666666;padding:10px;margin:8px 0px 12px 0px '>於 <span\n"
                        + "            style='color:#000000'>").append(DateUtil.getNowFormatString(DateUtils.DATE_FORMATTER))
                .append("</span>發起的資安風險評估已完成，並已制定改善計劃，需要請您審核！\n").append("    </div>\n").append("    <div>\n")
                .append("         <a href='" + success + "'><button style='cursor:pointer;color:#ffffff;background:#1890ff;border:#1890ff;padding:8px 12px;margin-right:24px;border-radius:4px'\n")
                .append("                onclick=\"javascript:{\n").append("  var xhr = new XMLHttpRequest();\n")
                .append("  xhr.open('GET', '")
                .append(success)
                .append("');\n")
                .append("  xhr.send();\n")
                .append("}\">\n")
                .append("            審核通過\n")
                .append("        </button></a>\n")
                .append("       <a href='" + back + "'> <button style='cursor:pointer;;border:1px #E6E6E6 solid;padding:8px 12px;border-radius:4px;background:#ffffff'\n")
                .append("                onclick=\"javascript:{\n")
                .append("  var xhr = new XMLHttpRequest();\n")
                .append("  xhr.open('GET', '").append(back).append("');\n")
                .append("  xhr.send();\n").append("}\">審核不通過\n")
                .append("        </button></a>\n").append("    </div>\n")
                .append("    <p style='padding-top:12px'>改善計劃如下：</p>");

        mailTableBuild(data, htmlTextBuilder);
        htmlTextBuilder.append("</div>");
        htmlTextBuilder.append("</div>");
        return htmlTextBuilder.toString();
    }

    private Map<Long, TenantMapDTO> getTenantMap(Long eid) {
        List<Long> tenantIdList = new ArrayList<>();
        tenantIdList.add(eid);
        return aioUserFeignClient.tenantServiceCodeGet(tenantIdList).getTenantMapDTO().stream()
                .collect(Collectors.toMap(TenantMapDTO::getEid, x -> x, (x, y) -> x));
    }


    private ResponseBase planUpdate(RiskImprovePlanParam param, PlanOperate operate, PlanStatus planStatus) {
        if (StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getTracker())) {
            return ResponseBase.error(ResponseCode.RISK_IMPROVE_PLAN_PARAM_ERROR);
        }
        RiskImprovePlan plan = new RiskImprovePlan();
        BeanUtils.copyProperties(param, plan);
        plan.setPlanStatus(planStatus);

        planDetailSave(plan.getId(), param.getTracker(), operate, param.getOperatingDesc(), param.getRemark());
        int i;
        if (planStatus.equals(ALREADY_PLAN)) {
            i = riskImprovePlanMapper.updateRiskImprovePlan(plan);
        } else {
            i = riskImprovePlanMapper.updateExecuteRiskImprovePlan(plan);
        }

        if (i == 0) {
            return ResponseBase.error(ResponseCode.RISK_DELETE_FAILED);
        }
        return ResponseBase.ok();
    }

    public void mailTableBuild(List<ImprovePlanExcelData> data, StringBuilder htmlTextBuilder) {


        // 添加表格行
        for (ImprovePlanExcelData datum : data) {
            // 添加表格頭部
            htmlTextBuilder.append("<table style='width:100%;background:#EDF4FF;'>\n" +
                    "        <thead>\n" +
                    "        <tr style='color:#666666;text-align:left;'>\n" +
                    "            <th style='width:7%;padding:12px;'>編號</th>\n" +
                    "            <th style='width:43%;padding:12px'>控制措施</th>\n" +
                    "            <th style='width:14%;padding:12px'>責任部門</th>\n" +
                    "            <th style='width:14%;padding:12px'>負責人</th>\n" +
                    "            <th style='width:22%;padding:12px'>預計執行時間</th>\n" +
                    "        </tr>\n" +
                    "        </thead>\n" +
                    "        <tbody>\n");
            htmlTextBuilder.append("<tr style='color:#002FA9;'>")
                    .append("<td style='padding:12px'>").append(datum.getImproveNo()).append("</td>")
                    .append("<td style='padding:12px'>").append(datum.getImprovePlan()).append("</td>")
                    .append("<td style='padding:12px'>").append(Objects.isNull(datum.getResponsibleDepartment()) ? "" : datum.getResponsibleDepartment())
                    .append("</td>")
                    .append("<td style='padding:12px'>").append(Objects.isNull(datum.getHead()) ? "" : datum.getHead()).append("</td>")
                    .append("<td style='padding:12px'>")
                    .append(Objects.isNull(datum.getEstimateExecutionTime()) ? "" : datum.getEstimateExecutionTime());
            if (Objects.nonNull(datum.getEstimateExecutionTime()) && Objects.nonNull(datum.getEstimateCompleteTime())) {
                htmlTextBuilder.append("-");
            }
            htmlTextBuilder.append(Objects.isNull(datum.getEstimateCompleteTime()) ? "" : datum.getEstimateCompleteTime())
                    .append("</td>")
                    .append("</tr>");
            htmlTextBuilder.append("        </tbody>\n" +
                    "    </table>");

            htmlTextBuilder.append(" <div style='margin:16px;width:100%;display:flex'>\n"
                            + "        <div style='width:50%;display:flex;'>\n"
                            + "            <div style='min-width:50px;'><span style='color:#666666'>對象：</span></div>\n"
                            + "            <div style='padding-right:16px'><span> ")
                    .append(datum.getObject())
                    .append(" </span></div>\n")
                    .append("        </div>\n")
                    .append("        <div style='width:50%;display:flex;padding-right:16px;'>\n")
                    .append("           <div style='width:150px;'><span style='color:#666666'>受影響的資通系統：</span></div>\n")
                    .append("            <div style='width:calc(100% - 150px)'><span> ").append(datum.getSystemNames())
                    .append(" </span></div>\n").append("        </div>\n").append("    </div>");
            htmlTextBuilder.append("<div style='margin:16px;width:100%;display:flex;'>\n"
                            + "        <div style='min-width:50px;'><span style='color:#666666'>效益：</span></div>\n"
                            + "        <div style='padding-right:16px'>\n" + "            <span> ")
                    .append(datum.getBenefit())
                    .append("</span>\n")
                    .append("        </div>\n")
                    .append("    </div>\n")
                    .append("    <div style='width:100%'></div>");
        }
    }

    public String urlParse(String url) {
        // 找到第三个 / 出现的位置
        int thirdSlashIndex = -1;
        int count = 0;
        for (int i = 0; i < url.length(); i++) {
            if (url.charAt(i) == '/') {
                count++;
                if (count == 3) {
                    thirdSlashIndex = i;
                    break;
                }
            }
        }

        // 使用 substring 函数查找第三个 / 之前的内容
        return url.substring(0, thirdSlashIndex + 1);
    }

    @Override
    public List<String> mailReceivers(String reviewMail) {
        List<String> receivers = new ArrayList<>();

        if (reviewMail.contains(",")) {
            receivers.addAll(Arrays.asList(reviewMail.split(",")));
        } else {
            receivers.add(reviewMail);
        }

        List<String> additionalReceivers = receivers.stream()
                .filter(i -> i.contains(";"))
                .flatMap(i -> Arrays.stream(i.split(";")))
                .collect(Collectors.toList());

        receivers.removeIf(i -> i.contains(";"));
        receivers.addAll(additionalReceivers);
        return receivers;
    }

    public ImprovePlanExcelData convert(RiskImprovePlan2DTO i) {
        ImprovePlanExcelData improvePlanExcelData = new ImprovePlanExcelData();
        BeanUtils.copyProperties(i, improvePlanExcelData);
        Set<String> systemNames = i.getEffectSystem().stream().map(RiskImprovePlanSystemDTO::getAssetSystemName).collect(Collectors.toSet());
        improvePlanExcelData.setSystemNames(String.join(",", systemNames));
        improvePlanExcelData.setPlanStatus(i.getPlanStatus().getDescription());
        improvePlanExcelData.setDoPriority(i.getDoPriority().getDescription());
        improvePlanExcelData.setEstimateExecutionTime(Objects.isNull(i.getEstimateExecutionTime()) ? null
                : DateUtils.formatDate(i.getEstimateExecutionTime()
                ,DateUtils.DATE_FORMATTER));
        improvePlanExcelData.setEstimateCompleteTime(Objects.isNull(i.getEstimateCompleteTime()) ? null : DateUtils.formatDate(i.getEstimateCompleteTime()
                , DateUtils.DATE_FORMATTER));
        improvePlanExcelData.setActualStartTime(Objects.isNull(i.getActualStartTime()) ? null : DateUtils.formatDate(i.getActualStartTime()
                , DateUtils.DATE_FORMATTER));
        improvePlanExcelData.setActualCompleteTime(Objects.isNull(i.getActualCompleteTime()) ? null : DateUtils.formatDate(i.getActualCompleteTime()
                , DateUtils.DATE_FORMATTER));
        return improvePlanExcelData;
    }

    private boolean checkPlanUD(String  planId) {
        RiskImprovePlan plan = riskImprovePlanMapper.selectById(planId);
        return riskCheckService.checkRiskAssessmentUD(plan.getAssessmentId());
    }


}
